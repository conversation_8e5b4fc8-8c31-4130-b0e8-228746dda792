import { CommonModule, isPlatformBrowser } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, HostListener, Inject, PLATFORM_ID } from '@angular/core';
import { MaterialModule } from '../../material-module';
import { Form<PERSON>uilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Subject, takeUntil } from 'rxjs';
import { environment } from '../../../environments/environment';
import { Notification } from '../../services/notification';
import { colorObj } from '../../shared/color-object';
import { Auth } from '../../services/auth';
import { Router } from '@angular/router';

@Component({
  selector: 'app-login',
  imports: [CommonModule, MaterialModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './login.html',
  styleUrl: './login.scss'
})
export class Login {

  isLogin = true;
  hidePassword = true;
  loginForm: FormGroup;
  country: FormControl = new FormControl();
  disabled: boolean = false;
  private _onDestroy = new Subject<void>();
  countryList: any = [];
  filterList: any = [];
  countryCode: any = '';
  selectedCountry: any;
  mobilenopattern = /^[0-9]{1,50}$/;
  public emailPattern = `${environment.EMAIL_PATTERN}`;
  public passwordPattern = `${environment.PASSWORD_PATTERN}`;
  mobile: boolean;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenWidth();
  }
  passwordRules = {
    minLength: false,
    upper: false,
    lower: false,
    number: false,
    special: false
  };

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    public dialogRef: MatDialogRef<Login>,
    private fb: FormBuilder,
    private authService: Auth,
    private notify: Notification,
    private dialog: MatDialog,
    private router: Router
  ) {
    this.loginForm = this.fb.group({
      firstName: [''],
      lastName: [''],
      emailId: ['', [Validators.required, Validators.pattern(this.emailPattern)]],
      phoneCode: [''],
      phoneNumber: ['', [Validators.pattern(this.mobilenopattern)]],
      companyName: [''],
      password: ['', [Validators.required, Validators.pattern(this.passwordPattern)]],
      tnc: ['']
    });
  }

  ngOnInit() {
    this.checkScreenWidth();
    if (isPlatformBrowser(this.platformId)) {
      const countryListArray = window.localStorage.getItem('COUNTRY');
      this.countryList = countryListArray;
      this.countryList = this.countryList ? JSON.parse(this.countryList) : [];
      this.filterList = this.countryList;
    }
    this.loginForm.get('password')?.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => this.onPasswordInput());
  }

  ngAfterViewInit() {
    this.country.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.filterCountry();
      });
  }

  private checkScreenWidth() {
    if (isPlatformBrowser(this.platformId)) {
      if (window.innerWidth <= 1250) {
        this.mobile = true;
      } else {
        this.mobile = false;
      }
    }
  }

  switchForm() {
    this.isLogin = !this.isLogin;
    this.loginForm.reset();
    this.country.setValue('');
    this.countryCode = '';
    this.selectedCountry = '';
    if (this.isLogin) {
      this.loginForm.controls["firstName"].clearValidators();
      this.loginForm.controls["lastName"].clearValidators();
      this.loginForm.controls["phoneCode"].clearValidators();
      this.loginForm.controls["phoneNumber"].clearValidators();
      this.loginForm.controls["companyName"].clearValidators();
      this.loginForm.controls["tnc"].clearValidators();
      this.loginForm.controls["firstName"].updateValueAndValidity();
      this.loginForm.controls["lastName"].updateValueAndValidity();
      this.loginForm.controls["phoneCode"].updateValueAndValidity();
      this.loginForm.controls["phoneNumber"].updateValueAndValidity();
      this.loginForm.controls["companyName"].updateValueAndValidity();
      this.loginForm.controls["tnc"].updateValueAndValidity();
    }
    else if (!this.isLogin) {
      this.loginForm.controls["firstName"].setValidators([Validators.required]);
      this.loginForm.controls["lastName"].setValidators([Validators.required]);
      this.loginForm.controls["phoneCode"].setValidators([Validators.required]);
      this.loginForm.controls["phoneNumber"].setValidators([Validators.required, Validators.pattern(this.mobilenopattern)]);
      this.loginForm.controls["companyName"].setValidators([Validators.required]);
      this.loginForm.controls["tnc"].setValidators([Validators.required]);
      this.loginForm.controls["firstName"].updateValueAndValidity();
      this.loginForm.controls["lastName"].updateValueAndValidity();
      this.loginForm.controls["phoneCode"].updateValueAndValidity();
      this.loginForm.controls["phoneNumber"].updateValueAndValidity();
      this.loginForm.controls["companyName"].updateValueAndValidity();
      this.loginForm.controls["tnc"].updateValueAndValidity();
    }
    if (isPlatformBrowser(this.platformId)) {
      window.scrollTo(0, 0);
    }
  }

  filterCountry() {
    let search = this.country.value;
    this.filterList = this.countryList.filter((country: any) =>
      (!!country.NAME ? country.NAME.toLowerCase() : '').indexOf(!!search ? search.toLowerCase() : '') !== -1 || (!!country.CODE ? country.CODE.toLowerCase() : '').indexOf(!!search ? search.toLowerCase() : '') !== -1);
  }

  selectContact() {
    if (isPlatformBrowser(this.platformId)) {
      const contactFormSection = document.getElementById('contactFormSection');
      if (contactFormSection) {
        contactFormSection.scrollIntoView({ behavior: 'smooth' });
      }
    }
  }

  changeCountry(country: number) {
    for (let coun of this.countryList) {
      if (coun.ID == country) {
        this.countryCode = '+' + coun.CODE;
        this.selectedCountry = coun.NAME;
      }
    }
    this.loginForm.controls["phoneNumber"].addValidators([Validators.required]);
    this.loginForm.controls["phoneNumber"].updateValueAndValidity();
  }

  getInput(event: any) {
    if (event.target.value.length != 0) {
      this.loginForm.controls["phoneCode"].setValidators([Validators.required]);
    } else {
      this.loginForm.controls["phoneCode"].clearValidators();
    }
    this.loginForm.controls["phoneCode"].updateValueAndValidity();
  }

  onPasswordInput() {
    const value = this.loginForm.get('password')?.value || '';
    this.passwordRules.minLength = value.length >= 8;
    this.passwordRules.upper = /[A-Z]/.test(value);
    this.passwordRules.lower = /[a-z]/.test(value);
    this.passwordRules.number = /[0-9]/.test(value);
    this.passwordRules.special = /[!?@#$%^&*]/.test(value);
  }

  createAccount() {
    this.disabled = true;
    let obj = {
      "FIRST_NAME": this.loginForm.value.firstName,
      "LAST_NAME": this.loginForm.value.lastName,
      "EMAIL": this.loginForm.value.emailId,
      "PASSWORD": this.loginForm.value.password,
      "COUNTRY_CODE_ID": this.loginForm.value.phoneCode,
      "PHONE_NUMBER": this.loginForm.value.phoneNumber,
      "COMPANY_NAME": this.loginForm.value.companyName,
      "PRIVACY_POLICY": true,
      "TERMS_CONDITION": true
    }
    this.authService.userSignUp(obj).subscribe({
      next: (data) => {
        if (isPlatformBrowser(this.platformId)) {
          window.localStorage.setItem(
            'userInfo',
            JSON.stringify({
              userId: data.object.ID,
              cognitoId: data.object.COGNITO_ID,
              firstName: data.object.FIRST_NAME,
              lastName: data.object.LAST_NAME,
              emailId: data.object.EMAIL_ID,
              mobile: data.object.PHONE_NUMBER,
              companyName: data.object.COMPANY_NAME,
              emailVerified: data.object.EMAIL_VERIFIED,
              mobileVerified: data.object.MOBILE_VERIFIED,
              userConfirmed: data.object.USER_CONFIRMED,
              roleId: data.object.ROLE_ID,
              access_token: data.object.TOKEN_DATA.ACCESS_TOKEN,
              id_token: data.object.TOKEN_DATA.ID_TOKEN,
              refresh_token: data.object.TOKEN_DATA.REFRESH_TOKEN,
              expiresIn: data.object.TOKEN_DATA.EXPIRES_IN
            })
          )
          window.localStorage.setItem('id_token', data.object.TOKEN_DATA.ID_TOKEN);
          window.localStorage.setItem('access_token', data.object.TOKEN_DATA.ACCESS_TOKEN);
          window.localStorage.setItem('userLoggedIn', 'true');
          this.disabled = false;
          this.dialogRef.close(true);
          this.notify.showNotification(
            "Signup successful",
            "top",
            (!!colorObj[data.status] ? colorObj[data.status] : "success"),
            data.status
          );
        }
      },
      error: (e) => {
        this.disabled = false;
        this.dialogRef.close(false);
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        );
      }
    })
  }

  userLogin() {
    this.disabled = true;
    let obj = {
      "USERNAME": this.loginForm.value.emailId,
      "PASSWORD": this.loginForm.value.password
    }
    this.authService.userLogIn(obj).subscribe({
      next: (data) => {
        if (isPlatformBrowser(this.platformId)) {
          window.localStorage.setItem(
            'userInfo',
            JSON.stringify({
              userId: data.object.ID,
              cognitoId: data.object.COGNITO_ID,
              firstName: data.object.FIRST_NAME,
              lastName: data.object.LAST_NAME,
              emailId: data.object.EMAIL_ID,
              mobile: data.object.PHONE_NUMBER,
              companyName: data.object.COMPANY_NAME,
              emailVerified: data.object.EMAIL_VERIFIED,
              mobileVerified: data.object.MOBILE_VERIFIED,
              userConfirmed: data.object.USER_CONFIRMED,
              roleId: data.object.ROLE_ID,
              access_token: data.object.TOKEN_DATA.ACCESS_TOKEN,
              id_token: data.object.TOKEN_DATA.ID_TOKEN,
              refresh_token: data.object.TOKEN_DATA.REFRESH_TOKEN,
              expiresIn: data.object.TOKEN_DATA.EXPIRES_IN
            })
          )
          window.localStorage.setItem('id_token', data.object.TOKEN_DATA.ID_TOKEN);
          window.localStorage.setItem('access_token', data.object.TOKEN_DATA.ACCESS_TOKEN);
          window.localStorage.setItem('userLoggedIn', 'true');
          this.disabled = false;
          this.dialogRef.close(true);
          this.notify.showNotification(
            "Login successful",
            "top",
            (!!colorObj[data.status] ? colorObj[data.status] : "success"),
            data.status
          );
        }
      },
      error: (e) => {
        this.disabled = false;
        this.dialogRef.close(false);
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        );
      }
    })
  }

  viewPrivacyPolicy() {
    if (isPlatformBrowser(this.platformId)) {
      window.open('/legal/privacy-policy', '_blank');
    }
    this.dialogRef.close();
  }

  viewServiceTerms() {
    if (isPlatformBrowser(this.platformId)) {
      window.open('/legal/terms-services', '_blank');
    }
    this.dialogRef.close();
  }

}