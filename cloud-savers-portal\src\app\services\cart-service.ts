import { isPlatformServer, isPlatformBrowser } from '@angular/common';
import { Injectable, TransferState, makeStateKey, StateKey, PLATFORM_ID, Inject } from '@angular/core';
import { Observable, Subject, BehaviorSubject, of } from 'rxjs';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { tap, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class CartService {

  private GET_CART_ITEMS: StateKey<any> = makeStateKey<any>('get-cart-items');
  private ADD_ITEM_TO_CART: StateKey<any> = makeStateKey<any>('add-item-to-cart');
  private REMOVE_CART_ITEM: StateKey<any> = makeStateKey<any>('remove-cart-item');
  private CONFIRM_ORDER: StateKey<any> = makeStateKey<any>('confirm-order');
  private GET_ORDER_HISTORY: StateKey<any> = makeStateKey<any>('get-order-history');
  private COOKIE_POLICY: StateKey<any> = makeStateKey<any>('cookie-policy');
  private GET_COMPARISON_DATA: StateKey<any> = makeStateKey<any>('get-comparison-data');
  private ADD_TO_COMPARISON: StateKey<any> = makeStateKey<any>('add-to-comparison');
  private REMOVE_COMPARISON: StateKey<any> = makeStateKey<any>('remove-comparison');

  public resourceUrl = `${environment.API_BASE_URL}`;
  cartUpdated$ = new Subject<void>();
  private cartItemCountSubject = new BehaviorSubject<number>(0);
  cartItemCount$ = this.cartItemCountSubject.asObservable();
  // Add login success event emitter
  private loginSuccessSubject = new Subject<void>();
  loginSuccess$ = this.loginSuccessSubject.asObservable();
  refreshComparisonPanel$ = new Subject<void>();
  triggerComparisonRefresh() {
    this.refreshComparisonPanel$.next();
  }

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private transferState: TransferState,
    private http: HttpClient) { }

  // Method to broadcast login success across all components
  notifyLoginSuccess(): void {
    this.loginSuccessSubject.next();
  }

  getCartItems(): Observable<any> {
    if (this.transferState.hasKey(this.GET_CART_ITEMS)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_CART_ITEMS, null);
      this.transferState.remove(this.GET_CART_ITEMS); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.get<any>(`${this.resourceUrl}/web/reve/portal/user/member/cart`).pipe(
        map(response => {
          const items = Array.isArray(response?.object) ? response?.object : [];
          const count = Array.isArray(items) ? items.length : 0;
          this.cartItemCountSubject.next(count);
          return response;
        }),
        tap((response) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_CART_ITEMS, response);
          }
        })
      );
    }
  }

  addItemToCart(obj: any): Observable<any> {
    if (this.transferState.hasKey(this.ADD_ITEM_TO_CART)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.ADD_ITEM_TO_CART, null);
      this.transferState.remove(this.ADD_ITEM_TO_CART); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.post<any>(`${this.resourceUrl}/web/reve/portal/user/member/cart/add`, obj).pipe(
        map(response => {
          const items = Array.isArray(response?.object) ? response?.object : [];
          const count = Array.isArray(items) ? items.length : 0;
          this.cartItemCountSubject.next(count);
          return response;
        }),
        tap((response) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.ADD_ITEM_TO_CART, response);
          }
        })
      );
    }
  }

  removeCartItem(obj: any): Observable<any> {
    if (this.transferState.hasKey(this.REMOVE_CART_ITEM)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.REMOVE_CART_ITEM, null);
      this.transferState.remove(this.REMOVE_CART_ITEM); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.put<any>(`${this.resourceUrl}/web/reve/portal/user/member/cart/update`, obj).pipe(
        map(response => {
          const items = Array.isArray(response?.object) ? response?.object : [];
          const count = Array.isArray(items) ? items.length : 0;
          this.cartItemCountSubject.next(count);
          return response;
        }),
        tap((response) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.REMOVE_CART_ITEM, response);
          }
        })
      );
    }
  }

  confirmOrder(obj: any): Observable<any> {
    if (this.transferState.hasKey(this.CONFIRM_ORDER)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.CONFIRM_ORDER, null);
      this.transferState.remove(this.CONFIRM_ORDER); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.post<any>(`${this.resourceUrl}/web/reve/portal/user/member/order`, obj).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.CONFIRM_ORDER, data);
          }
        })
      );
    }
  }

  getOrderHistory(): Observable<any> {
    if (this.transferState.hasKey(this.GET_ORDER_HISTORY)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_ORDER_HISTORY, null);
      this.transferState.remove(this.GET_ORDER_HISTORY); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.get<any>(`${this.resourceUrl}/web/reve/portal/user/member/order`).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_ORDER_HISTORY, data);
          }
        })
      );
    }
  }

  cookiePolicy(obj: any): Observable<any> {
    if (this.transferState.hasKey(this.COOKIE_POLICY)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.COOKIE_POLICY, null);
      this.transferState.remove(this.COOKIE_POLICY); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.put<any>(`${this.resourceUrl}/web/reve/user/member/update/cookie-policy`, obj).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.COOKIE_POLICY, data);
          }
        })
      );
    }
  }

  getComparisonData(): Observable<any> {
    if (this.transferState.hasKey(this.GET_COMPARISON_DATA)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_COMPARISON_DATA, null);
      this.transferState.remove(this.GET_COMPARISON_DATA); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.get<any>(`${this.resourceUrl}/web/reve/portal/user/member/comparison`).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_COMPARISON_DATA, data);
          }
        })
      );
    }
  }

  addToComparison(obj: any): Observable<any> {
    if (this.transferState.hasKey(this.ADD_TO_COMPARISON)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.ADD_TO_COMPARISON, null);
      this.transferState.remove(this.ADD_TO_COMPARISON); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.post<any>(`${this.resourceUrl}/web/reve/portal/user/member/comparison/add`, obj).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.ADD_TO_COMPARISON, data);
          }
        })
      );
    }
  }

  removeComparison(obj: any): Observable<any> {
    if (this.transferState.hasKey(this.REMOVE_COMPARISON)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.REMOVE_COMPARISON, null);
      this.transferState.remove(this.REMOVE_COMPARISON); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.put<any>(`${this.resourceUrl}/web/reve/portal/user/member/comparison/update`, obj).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.REMOVE_COMPARISON, data);
          }
        })
      );
    }
  }

}