<div class="custom-overlay" *ngIf="loading">
    <div class="custom-overlay__inner">
        <div class="custom-loader">
            <svg class="progress-circle" viewBox="0 0 50 50">
                <circle class="progress-bg" cx="25" cy="25" r="20" />
                <circle class="progress-bar" cx="25" cy="25" r="20" />
            </svg>
            <div class="loader-title">Loading data</div>
            <div class="loader-desc">Please wait...</div>
        </div>
    </div>
</div>
<div>
    <app-header></app-header>
    <div class="container">
        <app-search-panel></app-search-panel>
    </div>
    <div class="head-row" id="detailContent">
        <div (click)="backToInstances()" class="back-arrow" *ngIf="!mobile">
            <iconify-icon icon="material-symbols:arrow-back-rounded"></iconify-icon>Back to Instances
        </div>
        <div (click)="backToInstances()" class="back-arrow" *ngIf="mobile">
            <iconify-icon icon="material-symbols:arrow-back-rounded"></iconify-icon>
        </div>
        <div class="pricing-head">{{instanceDetails?.service_provider | uppercase}} {{instanceDetails?.vm_name}}
            Instance Details</div>
    </div>
    <div class="detail-content">
        <section class="specs-container">
            <div class="specs-section">
                <div class="spec-details">
                    <div class="title-row">
                        <img class="provider-logo"
                            [src]="'assets/images/' + providerIconMap[instanceDetails?.service_provider | lowercase]">
                        <div class="provider-details">
                            <div class="provider-name">{{instanceDetails?.product_type}} -
                                {{instanceDetails?.product_name}} - {{instanceDetails?.vm_name}}
                            </div>
                            <div class="provider-region">{{instanceDetails?.cs_global_region}} -
                                {{instanceDetails?.cs_geo_region}} -
                                {{instanceDetails?.cs_region}} - {{instanceDetails?.cs_city}}</div>
                        </div>
                    </div>
                    <div class="spec-row">
                        <div class="spec-card">
                            <div class="spec-title">vCPU</div>
                            <div class="spec-value">{{instanceDetails?.vcpu_cores}} vCPU{{instanceDetails?.vcpu_cores >
                                1 ? 's' : ''}}
                            </div>
                        </div>
                        <div class="spec-card">
                            <div class="spec-title">Memory</div>
                            <div class="spec-value">{{instanceDetails?.ram_gb}} GB</div>
                        </div>
                        <div class="spec-card">
                            <div class="spec-title">Storage</div>
                            <div class="spec-value">{{instanceDetails?.storage_spec}}</div>
                        </div>
                    </div>
                </div>
                <div class="price-card">
                    <div class="price-details">
                        <div class="payment-details">
                            <div class="payment-option"
                                *ngIf="(instanceDetails?.payment_option | lowercase) == 'partial upfront' || (instanceDetails?.payment_option | lowercase) == 'all upfront'">
                                {{formatText(instanceDetails?.payment_option)}}
                            </div>
                            <div class="price-item"
                                *ngIf="(instanceDetails?.payment_option | lowercase) == 'partial upfront' || (instanceDetails?.payment_option | lowercase) == 'all upfront'">
                                {{formatText(instanceDetails?.term)}} -
                                <span>${{(instanceDetails?.upfront_amount_usd)?.toFixed(2)}}</span>
                            </div>
                            <div class="price-item">Monthly -
                                <span>${{(instanceDetails?.monthly_rate_usd)?.toFixed(2)}}</span>
                            </div>
                            <div class="payment-option">{{formatText(instanceDetails?.payment_plan)}}</div>
                        </div>
                        <div class="price-actions">
                            <div class="price">
                                <span class="price-period">Avg.</span>
                                ${{getMonthlyPrice(instanceDetails?.hourly_rate_usd,
                                instanceDetails?.service_provider)}}
                                <span class="price-period">/mo</span>
                            </div>
                            <button mat-button class="cart-btn" (click)="addToCart(instanceDetails)">
                                Add to Cart
                            </button>
                        </div>
                    </div>
                    <!-- <div class="price-actions">
                        <button mat-button class="deploy-btn">Deploy Instance </button>
                    </div> -->
                </div>
            </div>
            <div class="description" *ngIf="resourceDetails?.DESCRIPTION">
                <div class="desc-title">Description</div>
                <div class="desc-content">{{resourceDetails?.DESCRIPTION}}</div>
            </div>
        </section>
        <section class="compute-section">
            <mat-tab-group class="detail-tabs">
                <mat-tab label="Compute">
                    <div class="compute-tab">
                        <div class="left-panel">
                            <div class="feature-title">CPU Specifications</div>
                            <div class="spec-row">Processor<span>{{instanceDetails?.cpu_model == 'na' ? '--' :
                                    instanceDetails?.cpu_model}}</span></div>
                            <div class="spec-row">Architecture<span>{{instanceDetails?.cpu_architecture == 'na' ? '--' :
                                    instanceDetails?.cpu_architecture}}</span></div>
                            <div class="spec-row">Clock Speed<span>{{instanceDetails?.cpu_clock_ghz == 'na' ? '--' :
                                    instanceDetails?.cpu_clock_ghz}}</span></div>
                            <div class="spec-row">Instance Family<span>{{instanceDetails?.instance_family == 'na' ? '--'
                                    : instanceDetails?.instance_family}}</span></div>
                            <div class="spec-row">Burst Performance Capable
                                <span>{{instanceDetails?.burst_performance_capable == 'na' ? '--' :
                                    instanceDetails?.burst_performance_capable}}</span>
                            </div>
                            <div class="spec-row">Auto Recovery Capable<span>{{instanceDetails?.auto_recovery_capable ==
                                    'na' ? '--' : instanceDetails?.auto_recovery_capable}}</span></div>
                            <div class="spec-row">Dedicated Host Capable<span>{{instanceDetails?.dedicated_host_capable
                                    == 'na' ? '--' : instanceDetails?.dedicated_host_capable}}</span></div>
                            <div class="spec-row">GPU Available<span>{{instanceDetails?.gpu_available == 'na' ? '--' :
                                    instanceDetails?.gpu_available}}</span></div>
                            <div class="spec-row">GPU Brand<span>{{instanceDetails?.gpu_brand == 'na' ? '--' :
                                    instanceDetails?.gpu_brand}}</span></div>
                            <div class="spec-row">GPU Count<span>{{instanceDetails?.gpu_count == 'na' ? '--' :
                                    instanceDetails?.gpu_count}}</span></div>
                            <div class="spec-row">GPU Memory<span>{{instanceDetails?.gpu_memory_mb == 'na' ? '--' :
                                    instanceDetails?.gpu_memory_mb + ' ' + 'MB'}}</span></div>
                            <div class="spec-row">GPU Model<span>{{instanceDetails?.gpu_model == 'na' ? '--' :
                                    instanceDetails?.gpu_model}}</span></div>
                            <div class="spec-row">Host Type<span>{{instanceDetails?.host_type == 'na' ? '--' :
                                    instanceDetails?.host_type}}</span></div>
                            <div class="spec-row">IPV4 (Per Interface)<span>{{instanceDetails?.ipv4_per_interface ==
                                    'na' ? '--' : instanceDetails?.ipv4_per_interface}}</span></div>
                            <div class="spec-row">IPV6 Capable<span>{{instanceDetails?.ipv6_capable == 'na' ? '--' :
                                    instanceDetails?.ipv6_capable}}</span></div>
                            <div class="spec-row">IPV6 (Per Interface)<span>{{instanceDetails?.ipv6_per_interface ==
                                    'na' ? '--' : instanceDetails?.ipv6_per_interface}}</span></div>
                            <div class="spec-row">Latest Generation<span>{{instanceDetails?.is_latest_generation == 'na'
                                    ? '--' : instanceDetails?.is_latest_generation}}</span></div>
                        </div>
                        <div class="right-panel">
                            <div>
                                <div class="benchmark-scores" *ngIf="resourceDetails?.CPU_BENCHMARK_SCORES">
                                    <div class="benchmark-title">CPU Benchmark Scores</div>
                                    <div class="benchmark-item">
                                        <div class="benchmark-header">
                                            <div class="benchmark-label">Single-Core Score</div>
                                            <div class="benchmark-value">
                                                {{resourceDetails?.CPU_BENCHMARK_SCORES?.SINGLE_CORE_SCORE?.VALUE}}
                                            </div>
                                        </div>
                                        <div class="benchmark-bar">
                                            <div class="benchmark-progress"
                                                [style.width]="(resourceDetails?.CPU_BENCHMARK_SCORES?.SINGLE_CORE_SCORE?.VALUE / resourceDetails?.CPU_BENCHMARK_SCORES?.SINGLE_CORE_SCORE?.MAX) * 100 + '%'">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="benchmark-item">
                                        <div class="benchmark-header">
                                            <div class="benchmark-label">Multi-Core Score</div>
                                            <div class="benchmark-value">
                                                {{resourceDetails?.CPU_BENCHMARK_SCORES?.MULTI_CORE_SCORE?.VALUE}}</div>
                                        </div>
                                        <div class="benchmark-bar">
                                            <div class="benchmark-progress"
                                                [style.width]="(resourceDetails?.CPU_BENCHMARK_SCORES?.MULTI_CORE_SCORE?.VALUE / resourceDetails?.CPU_BENCHMARK_SCORES?.MULTI_CORE_SCORE?.MAX) * 100 + '%'">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="benchmark-item">
                                        <div class="benchmark-header">
                                            <div class="benchmark-label">Floating Point Performance</div>
                                            <div class="benchmark-value">
                                                {{resourceDetails?.CPU_BENCHMARK_SCORES?.FLOATING_POINT_PERFORMANCE?.VALUE}}
                                            </div>
                                        </div>
                                        <div class="benchmark-bar">
                                            <div class="benchmark-progress"
                                                [style.width]="(resourceDetails?.CPU_BENCHMARK_SCORES?.FLOATING_POINT_PERFORMANCE?.VALUE / resourceDetails?.CPU_BENCHMARK_SCORES?.FLOATING_POINT_PERFORMANCE?.MAX) * 100 + '%'">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="benchmark-item">
                                        <div class="benchmark-header">
                                            <div class="benchmark-label">Integer Performance</div>
                                            <div class="benchmark-value">
                                                {{resourceDetails?.CPU_BENCHMARK_SCORES?.INTEGER_PERFORMANCE?.VALUE}}
                                            </div>
                                        </div>
                                        <div class="benchmark-bar">
                                            <div class="benchmark-progress"
                                                [style.width]="(resourceDetails?.CPU_BENCHMARK_SCORES?.INTEGER_PERFORMANCE?.VALUE / resourceDetails?.CPU_BENCHMARK_SCORES?.INTEGER_PERFORMANCE?.MAX) * 100 + '%'">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="performance-description" *ngIf="resourceDetails?.CPU_PERFORMANCE">
                                    <div class="feature-title">CPU Performance</div>
                                    <div class="desc-content">{{resourceDetails?.CPU_PERFORMANCE}}</div>
                                </div>
                            </div>
                            <div>
                                <div class="recommendation" *ngIf="resourceDetails?.RECOMMENDATION">
                                    <div class="recc-title">
                                        <iconify-icon icon="ic:outline-info"></iconify-icon>
                                        Recommendation
                                    </div>
                                    <div class="recc-desc">
                                        {{resourceDetails?.RECOMMENDATION}}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </mat-tab>
                <mat-tab label="Memory">
                    <div class="compute-tab">
                        <div class="left-panel">
                            <div class="feature-title">Memory Specifications</div>
                            <div class="spec-row">Operating System<span>{{instanceDetails?.os == 'na' ? '--' :
                                    instanceDetails?.os}}</span></div>
                            <div class="spec-row">Operating System Type<span>{{instanceDetails?.os_type == 'na' ?
                                    '--' : instanceDetails?.os_type}}</span></div>
                            <div class="spec-row">RAM<span>{{instanceDetails?.ram_gb == 'na' ? '--' :
                                    instanceDetails?.ram_gb + ' ' + 'GB'}}</span></div>
                            <div class="spec-row">RAM Type<span>{{instanceDetails?.ram_type == 'na' ? '--'
                                    : instanceDetails?.ram_type}}</span></div>
                            <div class="spec-row">Total GPU Memory<span>{{instanceDetails?.total_gpu_memory_mb ==
                                    'na' ? '--' : instanceDetails?.total_gpu_memory_mb + ' ' + 'MB'}}</span></div>
                        </div>
                        <div class="right-panel">
                            <div class="benchmark-scores" *ngIf="resourceDetails?.MEMORY_BENCHMARK_SCORES">
                                <div class="benchmark-title">Memory Benchmark Scores</div>
                                <div class="benchmark-item">
                                    <div class="benchmark-header">
                                        <div class="benchmark-label">Memory Read</div>
                                        <div class="benchmark-value">
                                            {{resourceDetails?.MEMORY_BENCHMARK_SCORES?.MEMORY_READ_SPEED?.SPEED |
                                            number:'1.0-0'}} GB/s</div>
                                    </div>
                                    <div class="benchmark-bar">
                                        <div class="benchmark-progress"
                                            [style.width]="(resourceDetails?.MEMORY_BENCHMARK_SCORES?.MEMORY_READ_SPEED?.SPEED / resourceDetails?.MEMORY_BENCHMARK_SCORES?.MEMORY_READ_SPEED?.MAX) * 100 + '%'">
                                        </div>
                                    </div>
                                </div>
                                <div class="benchmark-item">
                                    <div class="benchmark-header">
                                        <div class="benchmark-label">Memory Write</div>
                                        <div class="benchmark-value">
                                            {{resourceDetails?.MEMORY_BENCHMARK_SCORES?.MEMORY_WRITE_SPEED?.SPEED |
                                            number:'1.0-0'}} GB/s
                                        </div>
                                    </div>
                                    <div class="benchmark-bar">
                                        <div class="benchmark-progress"
                                            [style.width]="(resourceDetails?.MEMORY_BENCHMARK_SCORES?.MEMORY_WRITE_SPEED?.SPEED / resourceDetails?.MEMORY_BENCHMARK_SCORES?.MEMORY_WRITE_SPEED?.MAX) * 100 + '%'">
                                        </div>
                                    </div>
                                </div>
                                <div class="benchmark-item">
                                    <div class="benchmark-header">
                                        <div class="benchmark-label">Memory Copy</div>
                                        <div class="benchmark-value">
                                            {{resourceDetails?.MEMORY_BENCHMARK_SCORES?.MEMORY_COPY_SPEED?.SPEED |
                                            number:'1.0-0'}} GB/s
                                        </div>
                                    </div>
                                    <div class="benchmark-bar">
                                        <div class="benchmark-progress"
                                            [style.width]="(resourceDetails?.MEMORY_BENCHMARK_SCORES?.MEMORY_COPY_SPEED?.SPEED / resourceDetails?.MEMORY_BENCHMARK_SCORES?.MEMORY_COPY_SPEED?.MAX) * 100 + '%'">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="spec-column" *ngIf="resourceDetails?.MEMORY_RECOMMENDATION">
                                <div class="feature-title">Memory Usage Recommendations</div>
                                <div class="memory-recommendation">
                                    <div class="desc-content">{{resourceDetails.MEMORY_RECOMMENDATION}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </mat-tab>
                <mat-tab label="Storage">
                    <div class="compute-tab">
                        <div class="left-panel">
                            <div class="feature-title">Storage Specifications</div>
                            <div class="spec-row">Storage Option Available<span>{{instanceDetails?.storage_option ==
                                    'na' ? '--' : instanceDetails?.storage_option}}</span></div>
                            <div class="spec-row">Storage<span>{{instanceDetails?.storage_spec == 'na' ? '--' :
                                    instanceDetails?.storage_spec}}</span></div>
                            <div class="spec-row">Storage Type<span>{{instanceDetails?.storage_type == 'na' ? '--' :
                                    instanceDetails?.storage_type}}</span></div>
                        </div>
                        <div class="right-panel">
                            <div class="benchmark-scores" *ngIf="resourceDetails?.STORAGE_BENCHMARK_SCORES">
                                <div class="benchmark-title">Storage Benchmark Scores</div>
                                <div class="benchmark-item">
                                    <div class="benchmark-header">
                                        <div class="benchmark-label">Sequential Read</div>
                                        <div class="benchmark-value">
                                            {{resourceDetails?.STORAGE_BENCHMARK_SCORES?.STORAGE_SEQUENTIAL_READ_SPEED?.SPEED
                                            | number:'1.0-0'}} MB/s
                                        </div>
                                    </div>
                                    <div class="benchmark-bar">
                                        <div class="benchmark-progress"
                                            [style.width]="(resourceDetails?.STORAGE_BENCHMARK_SCORES?.STORAGE_SEQUENTIAL_READ_SPEED?.SPEED / resourceDetails?.STORAGE_BENCHMARK_SCORES?.STORAGE_SEQUENTIAL_READ_SPEED?.MAX) * 100 + '%'">
                                        </div>
                                    </div>
                                </div>
                                <div class="benchmark-item">
                                    <div class="benchmark-header">
                                        <div class="benchmark-label">Sequential Write</div>
                                        <div class="benchmark-value">
                                            {{resourceDetails?.STORAGE_BENCHMARK_SCORES?.STORAGE_SEQUENTIAL_WRITE_SPEED?.SPEED
                                            | number:'1.0-0'}} MB/s
                                        </div>
                                    </div>
                                    <div class="benchmark-bar">
                                        <div class="benchmark-progress"
                                            [style.width]="(resourceDetails?.STORAGE_BENCHMARK_SCORES?.STORAGE_SEQUENTIAL_WRITE_SPEED?.SPEED / resourceDetails?.STORAGE_BENCHMARK_SCORES?.STORAGE_SEQUENTIAL_WRITE_SPEED?.MAX) * 100 + '%'">
                                        </div>
                                    </div>
                                </div>
                                <div class="benchmark-item">
                                    <div class="benchmark-header">
                                        <div class="benchmark-label">Random Read (4K)</div>
                                        <div class="benchmark-value">
                                            {{resourceDetails?.STORAGE_BENCHMARK_SCORES?.STORAGE_RANDOM_READ_SPEED?.SPEED
                                            | number:'1.0-0'}} IOPS
                                        </div>
                                    </div>
                                    <div class="benchmark-bar">
                                        <div class="benchmark-progress"
                                            [style.width]="(resourceDetails?.STORAGE_BENCHMARK_SCORES?.STORAGE_RANDOM_READ_SPEED?.SPEED / resourceDetails?.STORAGE_BENCHMARK_SCORES?.STORAGE_RANDOM_READ_SPEED?.MAX) * 100 + '%'">
                                        </div>
                                    </div>
                                </div>
                                <div class="benchmark-item">
                                    <div class="benchmark-header">
                                        <div class="benchmark-label">Random Write (4K)</div>
                                        <div class="benchmark-value">
                                            {{resourceDetails?.STORAGE_BENCHMARK_SCORES?.STORAGE_RANDOM_WRITE_SPEED?.SPEED
                                            | number:'1.0-0'}} IOPS
                                        </div>
                                    </div>
                                    <div class="benchmark-bar">
                                        <div class="benchmark-progress"
                                            [style.width]="(resourceDetails?.STORAGE_BENCHMARK_SCORES?.STORAGE_RANDOM_WRITE_SPEED?.SPEED / resourceDetails?.STORAGE_BENCHMARK_SCORES?.STORAGE_RANDOM_WRITE_SPEED?.MAX) * 100 + '%'">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </mat-tab>
                <mat-tab label="Network">
                    <div class="compute-tab">
                        <div class="left-panel">
                            <div class="feature-title">Network Specifications</div>
                            <div class="spec-row">Network Bandwidth<span>{{instanceDetails?.network_bandwidth_gbps
                                    == 'na' ? '--' : instanceDetails?.network_bandwidth_gbps + ' ' + 'GBPS'}}</span>
                            </div>
                            <div class="spec-row">Max Network
                                Bandwidth<span>{{instanceDetails?.network_bandwidth_max_gbps == 'na' ? '--' :
                                    instanceDetails?.network_bandwidth_max_gbps + ' ' + 'GBPS'}}</span></div>
                            <div class="spec-row">Min Network
                                Bandwidth<span>{{instanceDetails?.network_bandwidth_min_gbps == 'na' ? '--' :
                                    instanceDetails?.network_bandwidth_min_gbps + ' ' + 'GBPS'}}</span></div>
                            <div class="spec-row">Max Network Cards<span>{{instanceDetails?.network_cards_max ==
                                    'na' ? '--' : instanceDetails?.network_cards_max}}</span></div>
                            <div class="spec-row">Network Encryption
                                Capable<span>{{instanceDetails?.network_encryption_capable == 'na' ? '--'
                                    : instanceDetails?.network_encryption_capable}}</span></div>
                            <div class="spec-row">Max Network
                                Interfaces<span>{{instanceDetails?.network_interfaces_max
                                    == 'na' ? '--' : instanceDetails?.network_interfaces_max}}</span></div>
                            <div class="spec-row">Network Performance<span>{{instanceDetails?.network_performance ==
                                    'na' ? '--' : instanceDetails?.network_performance}}</span></div>
                        </div>
                        <div class="right-panel">
                            <div class="benchmark-scores" *ngIf="resourceDetails?.NETWORK_BENCHMARK_SCORES">
                                <div class="benchmark-title">Network Benchmark Scores</div>
                                <div class="benchmark-item">
                                    <div class="benchmark-header">
                                        <div class="benchmark-label">Throughput</div>
                                        <div class="benchmark-value">
                                            {{resourceDetails?.NETWORK_BENCHMARK_SCORES?.NETWORK_THROUGHPUT_SPEED?.SPEED
                                            | number:'1.0-0'}} Gbps
                                        </div>
                                    </div>
                                    <div class="benchmark-bar">
                                        <div class="benchmark-progress"
                                            [style.width]="(resourceDetails?.NETWORK_BENCHMARK_SCORES?.NETWORK_THROUGHPUT_SPEED?.SPEED / resourceDetails?.NETWORK_BENCHMARK_SCORES?.NETWORK_THROUGHPUT_SPEED?.MAX) * 100 + '%'">
                                        </div>
                                    </div>
                                </div>
                                <div class="benchmark-item">
                                    <div class="benchmark-header">
                                        <div class="benchmark-label">Packet Rate</div>
                                        <div class="benchmark-value">
                                            {{resourceDetails?.NETWORK_BENCHMARK_SCORES?.NETWORK_PACKET_RATE_SPEED?.SPEED
                                            | number:'1.0-0'}} PPS
                                        </div>
                                    </div>
                                    <div class="benchmark-bar">
                                        <div class="benchmark-progress"
                                            [style.width]="(resourceDetails?.NETWORK_BENCHMARK_SCORES?.NETWORK_PACKET_RATE_SPEED?.SPEED / resourceDetails?.NETWORK_BENCHMARK_SCORES?.NETWORK_PACKET_RATE_SPEED?.MAX) * 100 + '%'">
                                        </div>
                                    </div>
                                </div>
                                <div class="benchmark-item">
                                    <div class="benchmark-header">
                                        <div class="benchmark-label">Latency (same AZ)</div>
                                        <div class="benchmark-value">
                                            {{resourceDetails?.NETWORK_BENCHMARK_SCORES?.NETWORK_LATENCY_SAME_AZ?.SPEED
                                            | number:'1.0-2'}} ms
                                        </div>
                                    </div>
                                    <div class="benchmark-bar">
                                        <div class="benchmark-progress"
                                            [style.width]="(resourceDetails?.NETWORK_BENCHMARK_SCORES?.NETWORK_LATENCY_SAME_AZ?.SPEED / resourceDetails?.NETWORK_BENCHMARK_SCORES?.NETWORK_LATENCY_SAME_AZ?.MAX) * 100 + '%'">
                                        </div>
                                    </div>
                                </div>
                                <div class="benchmark-item">
                                    <div class="benchmark-header">
                                        <div class="benchmark-label">Latency (cross-AZ)</div>
                                        <div class="benchmark-value">
                                            {{resourceDetails?.NETWORK_BENCHMARK_SCORES?.NETWORK_LATENCY_CROSS_AZ?.SPEED
                                            | number:'1.0-2'}} ms
                                        </div>
                                    </div>
                                    <div class="benchmark-bar">
                                        <div class="benchmark-progress"
                                            [style.width]="(resourceDetails?.NETWORK_BENCHMARK_SCORES?.NETWORK_LATENCY_CROSS_AZ?.SPEED / resourceDetails?.NETWORK_BENCHMARK_SCORES?.NETWORK_LATENCY_CROSS_AZ?.MAX) * 100 + '%'">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="spec-column" *ngIf="resourceDetails?.NETWORK_SECURITY_FEATURES">
                                <div class="feature-title">Network Security Features</div>
                                <div class="feature-row"
                                    *ngFor="let ruc of resourceDetails?.NETWORK_SECURITY_FEATURES || []">
                                    <div class="feature-head">
                                        <div class="title"><iconify-icon icon="charm:tick" width="16"
                                                height="16"></iconify-icon> {{ruc?.HEADING}}</div>
                                        <div class="desc">&nbsp;{{ruc?.DETAIL}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </mat-tab>
            </mat-tab-group>
        </section>
        <section class="atlerpilot-container" *ngIf="!mobile">
            <div class="atler-row">
                <div class="atler-column">
                    <div class="atler-title">CloudAtler Atlerpilot</div>
                    <div class="atler-sub-title">Put your cloud on Atlerpilot</div>
                    <div class="atler-sub-desc">
                        Operate multi-cloud with confidence—see spend in real time, automate fixes, and enforce
                        guardrails without slowing teams
                    </div>
                    <div class="atler-sub-row">
                        <div class="atler-sub-content">
                            <img src="assets/images/analytics.png">
                            <span class="atler-sub-item"> Real-time cost tracking</span>
                        </div>
                        <div class="atler-sub-content">
                            <img src="assets/images/automated-settings.png">
                            <span class="atler-sub-item"> Automated optimization</span>
                        </div>
                        <div class="atler-sub-content">
                            <img src="assets/images/security-guard.png">
                            <span class="atler-sub-item"> Security guardrails</span>
                        </div>

                    </div>
                    <button class="atler-button" (click)="getAdminPortal($event)">Get Started for free</button>
                </div>
                <div>
                    <img src="assets/images/atler-dashboard2.png" alt="atler-dashboard" class="atler-dashboard">
                </div>
            </div>
        </section>
        <section class="specs-container">
            <div *ngIf="pricingLoading" class="loading-spinner">
                <mat-progress-spinner color="primary" mode="indeterminate" diameter="50">
                </mat-progress-spinner>
            </div>
            <div [hidden]="pricingLoading" class="pricing-container">
                <div class="feature-title">
                    Regional Pricing Comparison
                </div>
                <div class="price-table-container">
                    <table mat-table [dataSource]="dataSource" class="price-table" *ngIf="dataSource.data.length > 0">
                        <ng-container matColumnDef="region_name">
                            <th mat-header-cell *matHeaderCellDef class="table-header">Region</th>
                            <td mat-cell *matCellDef="let element" class="table-cell">
                                {{element?.document?.actual_region_name}}</td>
                        </ng-container>
                        <ng-container matColumnDef="payment_option">
                            <th mat-header-cell *matHeaderCellDef class="table-header">Payment Option</th>
                            <td mat-cell *matCellDef="let element" class="table-cell">
                                {{formatText(element?.document?.payment_option) || '--'}}</td>
                        </ng-container>
                        <ng-container matColumnDef="payment_plan">
                            <th mat-header-cell *matHeaderCellDef class="table-header">Payment Plan</th>
                            <td mat-cell *matCellDef="let element" class="table-cell">
                                {{formatText(element?.document?.payment_plan) || '--'}}</td>
                        </ng-container>
                        <ng-container matColumnDef="avg_monthly_price">
                            <th mat-header-cell *matHeaderCellDef class="table-header">Average Monthly Price</th>
                            <td mat-cell *matCellDef="let element" class="table-cell">
                                ${{getMonthlyPrice(element?.document?.hourly_rate_usd,
                                element?.document?.service_provider)}}</td>
                        </ng-container>
                        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                        <tr mat-row *matRowDef="let element; columns: displayedColumns;"></tr>
                    </table>
                </div>
                <mat-paginator #paginator [pageSize]="5" [pageIndex]="0" [hidePageSize]="true"></mat-paginator>
                <div class="no-data" *ngIf="dataSource.data.length == 0">No pricing data available</div>
            </div>
        </section>
        <section class="specs-container">
            <div *ngIf="dealLoading" class="loading-spinner">
                <mat-progress-spinner color="primary" mode="indeterminate" diameter="50">
                </mat-progress-spinner>
            </div>
            <div *ngIf="!dealLoading" class="pricing-container">
                <div class="feature-title">
                    Similar Deals
                </div>
                <div class="instance-cards">
                    <div *ngFor="let group of similarDeals || []" class="instance-card-container">
                        <div class="instance-card">
                            <div class="instance-header">
                                <div class="provider-type">
                                    <img class="provider-logo"
                                        [src]="'assets/images/' + providerIconMap[group?.hits[0]?.document?.service_provider | lowercase]">
                                    <div class="provider-details">
                                        <div class="provider-name">{{group?.hits[0]?.document?.service_provider |
                                            uppercase}}
                                            <span>- {{group?.hits[0]?.document?.product_type}}</span>
                                        </div>
                                        <div class="instance-details">{{group?.hits[0]?.document?.vcpu_cores}}
                                            vCPU • {{group?.hits[0]?.document?.ram_gb}} GB RAM •
                                            {{group?.hits[0]?.document?.storage_spec}}
                                        </div>
                                    </div>
                                </div>
                                <div class="instance-price">
                                    <div class="price">
                                        <div class="price-period-icon"
                                            matTooltip="This is an approximate price, actual price may vary.">
                                            <iconify-icon icon="material-symbols:info-outline-rounded"></iconify-icon>
                                        </div>
                                        <div class="price-period">Avg.</div>
                                        <div>${{getMonthlyPrice(group?.hits[0]?.document?.hourly_rate_usd,
                                            group?.hits[0]?.document?.service_provider)}}</div>
                                        <div class="price-period">/mo</div>
                                    </div>
                                    <div class="instance-family">
                                        {{group?.hits[0]?.document?.instance_family}}
                                    </div>
                                </div>
                            </div>
                            <mat-divider></mat-divider>
                            <div class="instance-specs">
                                <div class="spec-item">
                                    <div class="spec-label">Product Name</div>
                                    <div class="spec-value">{{group?.hits[0]?.document?.product_name}}</div>
                                </div>
                                <div class="spec-item">
                                    <div class="spec-label">Instance Type</div>
                                    <div class="spec-value">{{group?.hits[0]?.document?.vm_name}}</div>
                                </div>
                                <div class="spec-item">
                                    <div class="spec-label">Processor Type</div>
                                    <div class="spec-value">{{group?.hits[0]?.document?.cpu_model}}
                                    </div>
                                </div>
                                <div class="spec-item">
                                    <div class="spec-label">Network</div>
                                    <div class="spec-value">{{group?.hits[0]?.document?.network_performance}}
                                    </div>
                                </div>
                            </div>
                            <div class="instance-action">
                                <div class="location">
                                    <div class="location-div">
                                        <iconify-icon
                                            icon="material-symbols:location-on-outline-rounded"></iconify-icon>
                                        {{group?.hits[0]?.document?.cs_global_region}} -
                                        {{group?.hits[0]?.document?.cs_geo_region}} -
                                        {{group?.hits[0]?.document?.cs_region}} - {{group?.hits[0]?.document?.cs_city}}
                                    </div>
                                    <div class="location-div">
                                        <iconify-icon icon="material-symbols:settings-outline-rounded"></iconify-icon>
                                        {{group?.hits[0]?.document?.os_type}} - {{group?.hits[0]?.document?.os}}
                                    </div>
                                </div>
                                <div class="action-buttons">
                                    <button mat-icon-button (click)="viewInstanceDetails(group?.hits[0]?.document)"
                                        class="action-icons">
                                        <iconify-icon icon="lsicon:view-filled"></iconify-icon>
                                    </button>
                                    <button mat-icon-button (click)="addToCart(group?.hits[0]?.document)"
                                        class="action-icons">
                                        <iconify-icon icon="mdi:cart-outline"></iconify-icon>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="instance-card instance-specs instance-card-price">
                            <div class="price-item">
                                <div class="payment-option">{{formatText(group?.hits[0]?.document?.payment_option)}}
                                </div>
                            </div>
                            <div class="price-item">
                                <div class="spec-label">{{formatText(group?.hits[0]?.document?.term)}}</div>
                                <div class="spec-value">${{(group?.hits[0]?.document?.upfront_amount_usd)?.toFixed(2)}}
                                </div>
                            </div>
                            <div class="price-item">
                                <div class="spec-label">Monthly</div>
                                <div class="spec-value">${{(group?.hits[0]?.document?.monthly_rate_usd)?.toFixed(2)}}
                                </div>
                            </div>
                            <div class="price-item">
                                <div class="payment-option">{{formatText(group?.hits[0]?.document?.payment_plan)}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="features-section">
            <div class="feature-card" *ngIf="resourceDetails?.RECOMMENDED_USE_CASES as RECOMMENDED_USE_CASES">
                <div class="feature-title">Recommended Use Cases</div>
                <div class="feature-row" *ngFor="let ruc of RECOMMENDED_USE_CASES || []">
                    <!-- <img src="assets/images/dev-env.png" alt="Development Environments"> -->
                    <!-- <img src="assets/images/web-app.png" alt="Small Web Applications"> -->
                    <!-- <img src="assets/images/micro-service.png" alt="Microservices"> -->
                    <!-- <img src="assets/images/pipeline.png" alt="CI/CD Pipelines"> -->
                    <div class="feature-head">
                        <div class="title">- {{ruc?.HEADING}}</div>
                        <div class="desc">{{ruc?.DETAIL}}</div>
                    </div>
                </div>
            </div>
            <div class="feature-card" *ngIf="resourceDetails?.SECURITY_COMPLIANCE as SECURITY_COMPLIANCE">
                <div class="feature-title">Security & Compliance</div>
                <div class="feature-row" *ngFor="let sc of SECURITY_COMPLIANCE || []">
                    <!-- <img src="assets/images/security-feature.png" alt="Security Features"> -->
                    <!-- <img src="assets/images/compliance.png" alt="Compliance Certifications"> -->
                    <!-- <img src="assets/images/audit.png" alt="Audit & Monitoring"> -->
                    <!-- <img src="assets/images/identity-mngt.png" alt="Identity & Access Management"> -->
                    <div class="feature-head">
                        <div class="title">- {{sc?.HEADING}}</div>
                        <div class="desc">{{sc?.DETAIL}}</div>
                    </div>
                </div>
            </div>
        </section>
        <!-- <section class="support-section">
            <div class="support-title">Documentation & Support</div>
            <div class="support-row">
                <div class="support-card">
                    <img src="assets/images/documentation.png" alt="Documentation">
                    <div class="support-head">Documentation</div>
                    <div class="support-desc">Comprehensive guides, tutorials, and API references for AWS EC2 instances.
                    </div>
                </div>
                <div class="support-card">
                    <img src="assets/images/knowledge.png" alt="Knowledge Base">
                    <div class="support-head">Knowledge Base</div>
                    <div class="support-desc">Troubleshooting guides, best practices, and answers to common questions.
                    </div>
                </div>
                <div class="support-card">
                    <img src="assets/images/support.png" alt="Support Plans">
                    <div class="support-head">Support Plans</div>
                    <div class="support-desc">Choose from Basic, Developer, Business, and Enterprise support plans.
                    </div>
                </div>
            </div>
        </section> -->
    </div>
    <div class="footer-row">
        <div class="title-row">
            <div class="footer-start">
                <img class="provider-logo"
                    [src]="'assets/images/' + providerIconMap[instanceDetails?.service_provider | lowercase]">
                <div class="footer-details">
                    <div class="foot-name">{{instanceDetails?.product_name}} {{instanceDetails?.vm_name}}</div>
                    <div class="foot-region"><span
                            *ngIf="(instanceDetails?.payment_option | lowercase) == 'partial upfront' || (instanceDetails?.payment_option | lowercase) == 'all upfront'">
                            {{formatText(instanceDetails?.payment_option)}} - {{formatText(instanceDetails?.term)}} -
                            ${{(instanceDetails?.upfront_amount_usd)?.toFixed(2)}}</span>
                        • Monthly - ${{(instanceDetails?.monthly_rate_usd)?.toFixed(2)}}/month •
                        {{instanceDetails?.cs_global_region}} - {{instanceDetails?.cs_geo_region}} -
                        {{instanceDetails?.cs_region}} - {{instanceDetails?.cs_city}}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <app-mobile-footer *ngIf="mobile"></app-mobile-footer>
</div>