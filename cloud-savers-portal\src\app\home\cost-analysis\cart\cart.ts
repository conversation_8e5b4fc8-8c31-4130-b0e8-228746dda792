import { CommonModule, isPlatformBrowser } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, EventEmitter, HostListener, Output, Inject, PLATFORM_ID } from '@angular/core';
import { MaterialModule } from '../../../material-module';
import { CartService } from '../../../services/cart-service';
import { NavigationExtras, Router } from '@angular/router';
import { Notification } from '../../../services/notification';
import { colorObj } from '../../../shared/color-object';
import { Auth } from '../../../services/auth';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { PROVIDER_ICON_MAP, PROVIDER_NAME_MAP } from '../../../shared/filter-headers.constants';

@Component({
  selector: 'app-cart',
  imports: [
    CommonModule,
    MaterialModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './cart.html',
  styleUrl: './cart.scss'
})
export class Cart {

  cartItems = [];
  get providerIconMap() {
    return PROVIDER_ICON_MAP;
  }
  get providerNameMap() {
    return PROVIDER_NAME_MAP;
  }
  @Output() loadingChange = new EventEmitter<boolean>();
  private destroy$ = new Subject<void>();
  public monthlyHours = environment.HOURS_IN_A_MONTH;
  mobile: boolean = false;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenWidth();
  }
  offers = [
    {
      name: 'AWS Startup Credits',
      desc: 'Get up to $100,000 in AWS credits',
      icon: 'assets/images/aws-offer.png'
    },
    {
      name: 'Azure Free Tier',
      desc: '12 months of free services',
      icon: 'assets/images/azure-offer.png'
    },
    {
      name: 'GCP Training',
      desc: 'Free cloud training & certification',
      icon: 'assets/images/gcp-offer.png'
    }
  ];
  limitedOffers = [
    {
      name: 'Multi-Cloud Bundle',
      desc: 'Get 30% off when you combine services from any two cloud providers'
    }
  ];

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private cartService: CartService,
    private router: Router,
    private notify: Notification,
    private authService: Auth
  ) { }

  ngOnInit(): void {
    this.checkScreenWidth();
    this.cartService.cartUpdated$.pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (this.authService.isLoggedIn()) {
          this.getCart();
        }
      });
    if (this.authService.isLoggedIn()) {
      this.getCart();
    }
  }

  private checkScreenWidth() {
    if (isPlatformBrowser(this.platformId)) {
      if (window.innerWidth < 1250) {
        this.mobile = true;
      } else {
        this.mobile = false;
      }
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getCart() {
    this.loadingChange.emit(true);
    this.cartService.getCartItems().subscribe({
      next: (data) => {
        this.cartItems = data.object;
        this.loadingChange.emit(false);
      },
      error: (e) => {
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        )
        this.loadingChange.emit(false);
      }
    })
  }

  updateItemQuantity(element: any, itemQuantity: number) {
    this.loadingChange.emit(true);
    let obj = {
      "RESOURCE": [{
        "RESOURCE_TYPE": element.RESOURCE_TYPE,
        "RESOURCE_ID": element.RESOURCE_ID,
        "QUANTITY": itemQuantity,
        "SERVICE_DETAILS": element.SERVICE_DETAILS
      }]
    }
    this.cartService.addItemToCart(obj).subscribe({
      next: (resp) => {
        if (resp.status === 200 && resp.object.length > 0) {
          this.getCart();
        }
      },
      error: (e) => {
        this.loadingChange.emit(false);
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        );
      }
    });
  }

  increaseQuantity(element: any) {
    if (element.QUANTITY != null) {
      let newQuantity = element.QUANTITY + 1;
      this.updateItemQuantity(element, newQuantity);
    }
  }

  decreaseQuantity(element: any) {
    if (element.QUANTITY > 1) {
      let newQuantity = element.QUANTITY - 1;
      this.updateItemQuantity(element, newQuantity);
    } else {
      this.removeItemFromCart(element.ID);
    }
  }

  removeItemFromCart(instanceId: string | number) {
    this.loadingChange.emit(true);
    let obj = {
      "ID": [instanceId]
    };
    this.cartService.removeCartItem(obj).subscribe({
      next: (resp) => {
        if (resp && resp.status === 200) {
          this.getCart();
        }
      },
      error: (e) => {
        this.loadingChange.emit(false);
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        );
      }
    });
  }


  get itemCount(): number {
    return this.cartItems.length;
  }

  get HrSubtotal(): number {
    return this.cartItems.reduce((sum, cartItem) => sum + (cartItem?.SERVICE_DETAILS?.hourly_rate_usd * cartItem?.QUANTITY), 0);
  }

  get MnSubtotal(): number {
    return this.cartItems.reduce((sum, cartItem) => sum + ((cartItem?.RESOURCE_TYPE
      === 'external' ? this.getExternalPrice(cartItem) : cartItem?.SERVICE_DETAILS?.selection?.cost) * cartItem?.QUANTITY), 0);
  }

  getExternalPrice(item): number {
    if (item?.SERVICE_DETAILS?.payment_option?.toLowerCase() === 'partial upfront') {
      return (item?.SERVICE_DETAILS?.upfront_amount_usd + item?.SERVICE_DETAILS?.monthly_rate_usd);
    } else if (item?.SERVICE_DETAILS?.payment_option?.toLowerCase() === 'all upfront') {
      return item?.SERVICE_DETAILS?.upfront_amount_usd;
    } else {
      return item?.SERVICE_DETAILS?.monthly_rate_usd;
    }
  }

  get formattedHrSubtotal(): string {
    return this.HrSubtotal?.toFixed(2);
  }

  get formattedMnSubtotal(): string {
    return this.MnSubtotal?.toFixed(2);
  }

  formatText(input: string) {
    return input?.replace(/-/g, ' ')?.replace(/\b\w/g, char => char?.toUpperCase());
  }

  getMonthlyPrice(hourlyRate: number, provider: string, quantity: number): string {
    const monthlyHours = this.monthlyHours[!!provider ? provider.toLowerCase() : ''];
    return (hourlyRate * monthlyHours * quantity)?.toFixed(2);
  }
  // viewCart() {
  //   this.router.navigate(['/order-billing']);
  // }

  proceedToCheckout() {
    // if (this.cartItems.length > 0) {
    // this.router.navigate(['/order-billing'], { fragment: 'billingContent' });
    // }
    const navigationExtras: NavigationExtras = {
      fragment: 'billingContent'
    };
    this.router.navigate(['/order-billing'], navigationExtras);
  }

}