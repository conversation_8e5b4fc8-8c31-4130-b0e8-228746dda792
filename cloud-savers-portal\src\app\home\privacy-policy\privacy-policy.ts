import { CommonModule, isPlatformBrowser } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, HostListener, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { MaterialModule } from '../../material-module';
import { Header } from '../header/header';
import { Router } from '@angular/router';
import { Footer } from '../footer/footer';
import { MobileFooter } from '../mobile-footer/mobile-footer';

@Component({
  selector: 'app-privacy-policy',
  imports: [CommonModule, MaterialModule, Header, Footer, MobileFooter],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './privacy-policy.html',
  styleUrl: './privacy-policy.scss'
})
export class PrivacyPolicy {

  mobile: boolean;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenWidth();
  }

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private router: Router) {
  }

  ngOnInit() {
    this.checkScreenWidth();
    this.scrollToTop();
  }

  private scrollToTop() {
    // Scroll to top after a small delay to ensure content is loaded
    setTimeout(() => {
      if (isPlatformBrowser(this.platformId)) {
        window.scrollTo(0, 0);
        document.documentElement.scrollTop = 0;
        document.body.scrollTop = 0;
      }
    }, 50);
  }

  private checkScreenWidth() {
    if (isPlatformBrowser(this.platformId)) {
      if (window.innerWidth <= 1250) {
        this.mobile = true;
      } else {
        this.mobile = false;
      }
    }
  }

  backToHome() {
    this.router.navigate(['/']);
  }
}