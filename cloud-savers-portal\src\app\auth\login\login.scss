.close-row {
    display: flex;
    justify-content: end;
    padding: 15px 10px 0px 0px;

    .close-icon {
        border-radius: 50%;
        background: #f3f3f3;
        color: #373737 !important;
        padding: 6px;
        height: 30px;
        width: 30px;
    }
}

.add-user {
    display: flex;
    flex-direction: column;
    padding: 20px 50px 40px 50px;
    gap: 20px;

    .header-row {
        display: flex;
        justify-content: center;

        .header {
            font-size: 26px;
            color: #000000;
            font-weight: 600;
        }
    }

    form {
        display: flex;
        flex-direction: column;
        height: auto;
        max-height: 400px;
        overflow-y: scroll;
        overflow-x: hidden;
        -ms-overflow-style: none;
        scrollbar-width: none;
        gap: 25px;
    }

    form ::-webkit-scrollbar {
        display: none;
    }

    .mat-mdc-dialog-content {
        overflow: visible;
        padding: 0 !important;
    }

    .content {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .mat-mdc-dialog-container .mat-mdc-dialog-content {
            padding: 0 !important;
        }

        .content-row {
            display: flex;
            justify-content: space-between;
            gap: 30px;

            .box-container {
                display: flex;
                flex-direction: column;
                width: 100%;
                gap: 7px;
                min-height: 70px;
                margin-bottom: 5px;

                .box-name {
                    font-size: 16px;
                    color: #7C838A;
                    font-weight: 500;
                }

                .text-box {
                    background: #B0BAC366;
                    border: 1px solid transparent;
                    outline: none;
                    border-radius: 20px;
                    height: 40px;
                    width: 100% !important;
                    padding: 5px;
                    padding-left: 10px;
                }

                .text-box:focus {
                    border: 1px solid #B0BAC366 !important;
                }

                .password-input {
                    position: relative;
                    width: 100%;
                    display: flex;
                    align-items: center;

                    .password {
                        width: 100% !important;
                        padding-right: 40px;
                    }

                    .hide-icon {
                        position: absolute;
                        right: 0;
                        top: 50%;
                        transform: translateY(-50%);
                        z-index: 1;
                        margin: 0;
                        height: 40px;
                        width: 50px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                }

                .error {
                    font-size: 12px;
                    color: #f44336;
                    margin-top: 4px;
                    min-height: 16px;
                }

                .theme-input-num {
                    width: 100%;
                    height: 40px;
                    border-radius: 0 20px 20px 0;
                    padding: 5px;
                    padding-left: 10px;
                    box-sizing: border-box;
                    outline: none;
                    border: none;
                    background: #B0BAC366;
                    border: 1px solid transparent;
                }

                .phone-input {
                    display: flex;
                    align-items: center;

                    .theme-input-phcode {
                        background: #B0BAC366;
                        border: 1px solid transparent;
                        border-right: 1px solid #B0BAC366;
                        outline: none;
                        height: 40px;
                        width: 25% !important;
                        font-size: 13px;
                        padding: 6px;
                        padding-left: 10px;
                        border-radius: 20px 0 0 20px;
                        box-sizing: border-box;
                        cursor: auto;
                    }
                }
            }
        }
    }

    .password-rules {
        display: flex;
        flex-direction: column;
        gap: 10px;
        padding: 10px;
        background-color: #DEE3E7;
        border-radius: 14px;
        border: 1px solid #E5E7EB;

        .rules {
            display: flex;
            flex-direction: row;
            gap: 8px;
            align-items: center;
            color: #7C838A;
            font-size: 13px;
            font-weight: 400;
            line-height: 27px;

            img {
                height: 14px;
                width: 14px;
            }
        }
    }

    .btn-container {
        display: flex;
        flex-direction: column;
        gap: 20px;
        // margin-top: auto;

        .create-btn {
            display: flex;
            justify-content: center;

            .btn-create-account {
                font-weight: 500;
                font-size: 20px;
                width: 50%;
                padding: 20px 0;
                background-color: #35A5DB;
                color: #FFFFFF;
                border: none;
                border-radius: 8px;
                align-items: center;

                &:disabled {
                    opacity: 0.5;
                }
            }
        }

        .login-link {
            font-size: 14px;
            color: #7C838A;
            text-align: start;

            span {
                color: #35A5DB;
                cursor: pointer;
            }
        }
    }

    .accept-text {
        font-size: 14px;
        color: #7C838A;
        text-align: start;

        span {
            color: #35A5DB;
            cursor: pointer;
            text-decoration: underline;
        }
    }

    .divider {
        text-align: center;
        color: #B0BAC3;
        font-weight: 500;
        font-size: 20px;
    }

    .social-buttons {
        display: flex;
        justify-content: space-around;
        gap: 10px;

        .btn-google {
            border: 1px solid #7C838A;
            color: #7C838A;
            font-size: 14px;
            font-weight: 500;
            padding: 0 15px;
            border-radius: 15px;
            height: 55px;
            align-items: center;

            .btn-txt {
                display: flex;
                align-items: center;
                gap: 8px;
            }
        }
    }
}

@media only screen and (max-width: 1250px) {
    .add-user {
        padding: 20px;

        .social-buttons {
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
        }

        .header {
            text-align: center;
        }
    }
}