<div class="custom-overlay" *ngIf="loading">
    <div class="custom-overlay__inner">
        <div class="custom-loader">
            <svg class="progress-circle" viewBox="0 0 50 50">
                <circle class="progress-bg" cx="25" cy="25" r="20" />
                <circle class="progress-bar" cx="25" cy="25" r="20" />
            </svg>
            <div class="loader-title">Loading data</div>
            <div class="loader-desc">Please wait...</div>
        </div>
    </div>
</div>
<div>
    <app-header></app-header>
    <div class="head-row">
        <div (click)="backToHome()" class="back-arrow">
            <iconify-icon icon="material-symbols:arrow-back-rounded"></iconify-icon>Back to Home
        </div>
        <div class="pricing-head">Your Order History</div>
    </div>
    <div class="history-container">
        <table mat-table [dataSource]="dataSource" class="mat-elevation-z8">
            <ng-container matColumnDef="orderId">
                <th mat-header-cell *matHeaderCellDef class="table-header">Order ID</th>
                <td mat-cell *matCellDef="let element" class="table-cell">#{{element?.ID}}</td>
            </ng-container>
            <ng-container matColumnDef="date">
                <th mat-header-cell *matHeaderCellDef class="table-header">Date</th>
                <td mat-cell *matCellDef="let element" class="table-cell date-column">
                    <div class="date-column">{{element?.CREATED_DATE | date:'MMMM dd, yyyy'}}</div>
                </td>
            </ng-container>
            <ng-container matColumnDef="services">
                <th mat-header-cell *matHeaderCellDef class="table-header services-column">Services</th>
                <td mat-cell *matCellDef="let element" class="table-cell services-column">
                    <div class="service-items">
                        <div class="service-item" *ngFor="let service of element?.ORDER_ITEMS || []">
                            <img class="provider-logo"
                                [src]="'assets/images/' + providerIconMap[service?.RESOURCE_TYPE === 'external' ? (service?.SERVICE_DETAILS?.service_provider | lowercase) : (service?.SERVICE_DETAILS?.selection?.service_provider | lowercase)]">
                            <div class="service-name">{{(service?.RESOURCE_TYPE === 'external' ?
                                service?.SERVICE_DETAILS?.service_provider :
                                service?.SERVICE_DETAILS?.selection?.service_provider) | uppercase}}
                                <span class="date-column">-
                                    {{service?.RESOURCE_TYPE === 'external' ? service?.SERVICE_DETAILS?.product_name :
                                    service?.SERVICE_DETAILS?.title}}</span>
                            </div>
                        </div>
                    </div>
                </td>
            </ng-container>
            <ng-container matColumnDef="totalPrice">
                <th mat-header-cell *matHeaderCellDef class="table-header">Total</th>
                <td mat-cell *matCellDef="let element" class="table-cell services-column">
                    ${{getTotalPrice(element)?.toFixed(2)}}</td>
            </ng-container>
            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let element; columns: displayedColumns;"></tr>
        </table>
    </div>
    <mat-paginator #paginator [pageSize]="10" [pageIndex]="0" [hidePageSize]="true"></mat-paginator>
</div>