<div class="cloud-accounts">
    <div class="account-head">
        <div class="head-row">
            <div class="title">Cloud Accounts</div>
            <button mat-button mat-dialog-close class="close-btn">
                <mat-icon class="close-icon">
                    close
                </mat-icon>
            </button>
        </div>
        <div class="desc">We will start gathering your cost data from all the providers you have connected.</div>
    </div>
    <div class="accounts-grid">
        <div class="account-card" *ngFor="let p of cloudProviders || []">
            <div class="provider">
                <img [src]="p.logo" [alt]="p.name" />
                <div class="account-info">
                    <div class="provider-name">{{p.name}}</div>
                    <div class="accounts-connected">{{p.accountsConnected}} account connected</div>
                </div>
            </div>
            <div class="action">
                <button mat-button (click)="connect(p)" class="connect-btn">
                    <mat-icon>add</mat-icon>Connect now</button>
                <div class="create-link">Create a new account</div>
            </div>
        </div>
    </div>
</div>