import { CommonModule } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, ChangeDetectorRef, OnDestroy, OnInit, ViewChild, Output, EventEmitter, Inject } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { CartService } from '../../services/cart-service';
import { MaterialModule } from '../../material-module';
import { Subject, takeUntil } from 'rxjs';
import { Cart } from '../cost-analysis/cart/cart';
import { MatDialog } from '@angular/material/dialog';
import { Auth } from '../../services/auth';
import { Login } from '../../auth/login/login';
import { SearchStateService } from '../../services/search-state.service';

@Component({
  selector: 'app-mobile-footer',
  standalone: true,
  imports: [CommonModule, MaterialModule, Cart],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './mobile-footer.html',
  styleUrls: ['./mobile-footer.scss']
})
export class MobileFooter implements OnInit, OnDestroy {
  cartItemCount = 0;
  showCartPanel = false;
  showFilterPanel = false;
  isCartLoading = false;
  isFilterLoading = false;
  isCostAnalysisPage = false;
  loading: boolean = false;
  loggedIn: boolean = false;
  private destroy$ = new Subject<void>();
  private cartCountSubscription: any;
  @ViewChild('cartComponent') cartComponent: Cart;
  // @ViewChild('filterComponent') filterComponent: any;
  @Output() scrollToPricing = new EventEmitter<void>();

  constructor(
    private router: Router,
    private cartService: CartService,
    private authService: Auth,
    private dialog: MatDialog,
    private cdr: ChangeDetectorRef,
    private searchState: SearchStateService,
    @Inject(SearchStateService) private searchStateService: SearchStateService
  ) { }

  ngOnInit(): void {
    this.isCartLoading = false;
    this.loggedIn = this.authService.isLoggedIn();

    this.cartService.loginSuccess$.pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.loggedIn = true;
      this.isCartLoading = true;
      this.cdr.detectChanges();
      this.subscribeToCartUpdates();
    });

    // Subscribe to cart count updates
    this.cartService.cartItemCount$.pipe(takeUntil(this.destroy$)).subscribe({
      next: (count) => {
        this.cartItemCount = count;
        this.isCartLoading = false;
        this.cdr.detectChanges();
      },
      error: (err) => {
        this.cartItemCount = 0;
        this.isCartLoading = false;
        this.cdr.detectChanges();
      }
    });

    // Subscribe to cart updates
    this.cartService.cartUpdated$.pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        if (this.loggedIn) {
          this.isCartLoading = true;
          this.cdr.detectChanges();
          this.refreshCart();
        } else {
          this.cartItemCount = 0;
          this.isCartLoading = false;
          this.cdr.detectChanges();
        }
      },
      error: (err) => {
        this.isCartLoading = false;
        this.cdr.detectChanges();
      }
    });

    if (this.loggedIn) {
      this.isCartLoading = true;
      this.refreshCart();
    }
    this.cartService.cartUpdated$.pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (this.authService.isLoggedIn()) {
          this.isCartLoading = true;
          this.subscribeToCartUpdates();
        } else {
          this.cartItemCount = 0;
          this.isCartLoading = false;
        }
      });
    this.searchState.filterSidebarVisible$.subscribe(show => {
      this.showFilterPanel = show;
      this.cdr.detectChanges();
    });
    this.searchState.cartSidebarVisible$.subscribe(show => {
      this.showCartPanel = show;
      this.cdr.detectChanges();
    });
    this.updateIsCostAnalysisPage(this.router.url);
    this.router.events.pipe(
      takeUntil(this.destroy$)
    ).subscribe(event => {
      if (event instanceof NavigationEnd) {
        this.updateIsCostAnalysisPage(event.url);
      }
    });
  }

  private updateIsCostAnalysisPage(url: string): void {
    const wasCostAnalysisPage = this.isCostAnalysisPage;
    this.isCostAnalysisPage = url.startsWith('/skyscanner4cloud') ||
      url?.includes('/skyscanner4cloud?') ||
      url === '/skyscanner4cloud';
    if (wasCostAnalysisPage !== this.isCostAnalysisPage) {
      this.cdr.detectChanges();
    }
  }

  private subscribeToCartUpdates(): void {
    // Always get the latest login state
    this.loggedIn = this.authService.isLoggedIn();

    if (this.cartCountSubscription) {
      this.cartCountSubscription.unsubscribe();
    }

    if (!this.loggedIn) {
      this.cartItemCount = 0;
      this.isCartLoading = false;
      this.cdr.detectChanges();
      return;
    }

    // Initial cart load
    this.refreshCart();

    // Subscribe to cart count updates
    this.cartCountSubscription = this.cartService.cartItemCount$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (count) => {
          this.cartItemCount = count;
          this.cdr.detectChanges();
        },
        error: (err) => {
          this.cartItemCount = 0;
          this.isCartLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  private refreshCart(): void {
    this.isCartLoading = true;
    this.cdr.detectChanges();
    // Get the latest cart data
    this.cartService.getCartItems().subscribe({
      next: (response) => {
        this.isCartLoading = false;
        this.cdr.detectChanges();
      },
      error: (error) => {
        this.isCartLoading = false;
        this.cartItemCount = 0;
        this.cdr.detectChanges();
      }
    });
  }

  backToHome(): void {
    this.router.navigate(['/']);
  }

  goToCart(): void {
    const isUserLoggedIn = this.authService.isLoggedIn();
    this.loggedIn = isUserLoggedIn;
    if (!isUserLoggedIn) {
      const dialogRef = this.dialog.open(Login, {
        width: '558px',
        height: 'auto',
        disableClose: true
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result === true) {
          this.loggedIn = true;
          this.cartService.notifyLoginSuccess();
          this.showCartPanel = true;
          this.searchState.showCartSidebar();
          this.isCartLoading = true;
          this.cdr.detectChanges();

          this.cartService.getCartItems().subscribe({
            next: (response) => {
              this.isCartLoading = false;
              this.cdr.detectChanges();
            },
            error: (error) => {
              this.isCartLoading = false;
              this.cartItemCount = 0;
              this.cdr.detectChanges();
            }
          });
        }
      });
      return;
    }

    this.showCartPanel = !this.showCartPanel;
    if (this.showCartPanel) {
      this.searchState.showCartSidebar();
      this.isCartLoading = true;
      this.cdr.detectChanges();
      this.cartService.getCartItems().subscribe({
        next: () => {
          this.isCartLoading = false;
          this.cdr.detectChanges();
        },
        error: (error) => {
          this.isCartLoading = false;
          this.cartItemCount = 0;
          this.cdr.detectChanges();
        }
      });
    }
  }

  goToFilter(): void {
    if (!this.isCostAnalysisPage) {
      this.router.navigate(['/skyscanner4cloud']).then(() => {
        this.searchState.showFilterSidebar();
      });
    } else {
      this.searchState.showFilterSidebar();
    }
  }

  ngOnDestroy(): void {
    if (this.cartCountSubscription) {
      this.cartCountSubscription.unsubscribe();
    }
    this.destroy$.next();
    this.destroy$.complete();
  }

  closeCartPanel(): void {
    this.showCartPanel = false;
    this.isCartLoading = false;
    this.searchState.hideCartSidebar();
    this.cdr.detectChanges();
  }

  onCartLoading(isLoading: boolean): void {
    this.isCartLoading = isLoading;
    this.cdr.detectChanges();
  }

}