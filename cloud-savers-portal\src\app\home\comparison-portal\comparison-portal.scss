:host ::ng-deep .mat-mdc-form-field-flex>.mat-mdc-form-field-infix {
    padding: 5px 0px 0.4em 0px !important;
    min-height: 0px !important;
}

:host ::ng-deep label.ng-star-inserted {
    transform: translateY(-0.59375em) scale(.75) !important;
}

:host ::ng-deep .mat-mdc-floating-label {
    top: 22px !important;
}

::ng-deep .mat-mdc-form-field-subscript-wrapper {
    box-sizing: border-box;
    width: 100%;
    position: relative !important;
}

.container {
    width: 100%;
    height: auto;
    background-image: url('../../../assets/images/background_img.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.detail-content {
    display: flex;
    flex-direction: column;
    max-width: 100%;
    overflow-x: hidden;

    .security-header {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;

        .security-head {
            font-size: 30px;
            font-weight: 700;
            color: #000000;
            text-align: center;
        }

        .security-desc {
            font-size: 16px;
            color: #4B5563;
            text-align: center;
            line-height: 24px;
            width: 50%;
        }
    }

    .security-section {
        background-color: #FFFFFF;
        padding: 40px 50px;
        display: flex;
        flex-direction: column;
        gap: 50px;

        .security-banner {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 20px;

            .security-card {
                width: 294px;
                background: white;
                border-radius: 8px;
                padding: 25px;
                box-shadow: 0px 1px 2px 0px #0000000D;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                align-items: start;
                gap: 25px;
                border: 1px solid #F3F4F6;

                .card-title {
                    font-size: 20px;
                    font-weight: 600;
                    color: #000000;
                }

                .card-desc {
                    font-size: 16px;
                    color: #4B5563;
                    line-height: 24px;
                }
            }
        }
    }

    .featured-desc {
        font-size: 16px;
        color: #4B5563;
        text-align: center;
        line-height: 24px;
    }

    .premium-section {
        background-color: #FFFFFF;
        padding: 40px 50px;
        display: flex;
        flex-direction: column;
        gap: 30px;

        .premium-header {
            display: flex;
            flex-direction: column;
            align-items: start;
            gap: 12px;
        }

        .premium-head {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 24px;
            font-weight: 700;
            color: #111827;
        }

        .premium-cards {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 20px;

            .premium-card {
                width: 30%;
                background: linear-gradient(135deg, #EFF6FF 0%, #FAF5FF 100%);
                border-radius: 8px;
                border: 1px solid #BFDBFE;
                box-shadow: 0px 10px 15px -3px #0000001A;
                padding: 30px 20px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                gap: 20px;
            }
        }
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: start;
    }

    .provider-head {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .provider-desc {
        font-size: 14px;
        color: #4B5563;
    }

    .discount {
        background-color: #EF4444;
        color: #FFFFFF;
        font-size: 12px;
        font-weight: 700;
        padding: 5px 8px;
        border-radius: 50px;
    }

    .premium-desc {
        font-size: 16px;
        color: #111827;
        font-weight: 700;
    }

    .premium-timer {
        display: flex;
        flex-direction: column;
        gap: 5px;
        justify-content: end;
        font-size: 12px;
    }

    .expiry {
        font-weight: 500;
        color: #DC2626;
    }

    .slots {
        color: #6B7280;
    }

    .detail-btn {
        width: 100%;
    }

    .provider-name {
        font-size: 20px;
        font-weight: 700;
        color: #111827;
    }

    .service-offers {
        display: flex;
        flex-direction: column;
        gap: 15px;
        font-size: 16px;
        color: #4B5563;
        line-height: 24px;

        .features-list {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .feature-item {
                display: flex;
                gap: 5px;
                align-items: center;

                .content {
                    font-size: 16px;
                    font-weight: 400;
                    color: #2C2C2C;
                }

                .plan-features {
                    color: #374151;
                }
            }
        }
    }

    .premium-offers {
        font-size: 14px;
    }

    .pricing {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .pricing-container {
            display: flex;
            flex-direction: column;
            gap: 5px;

            .price-time {
                font-size: 14px;
                color: #6B7280;
            }
        }

        .price {
            font-size: 24px;
            font-weight: 700;
            color: #111827;
        }
    }

    .view-btn {
        background-color: #35A5DB;
        color: #FFFFFF;
        font-size: 16px;
        padding: 10px 20px;
        border-radius: 8px;
    }

    .offer-btn {
        display: flex;
        justify-content: center;
        color: #3B82F6;
        font-weight: 500;
        font-size: 16px;
        cursor: pointer;
    }

    .business-help {
        padding: 40px 50px;
        display: flex;
        flex-direction: column;
        gap: 30px;
        background-color: #35A5DB;

        .business-head {
            font-size: 48px;
            font-weight: 700;
            color: #FFFFFF;
            text-align: center;
        }

        .help-cards {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 20px;

            .help-card {
                width: 294px;
                background: white;
                border-radius: 8px;
                box-shadow: 0px 1px 2px 0px #0000000D;
                padding: 15px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                align-items: start;
                gap: 10px;

                p {
                    font-size: 16px;
                    color: #4B5563;
                    line-height: 24px;
                }
            }
        }
    }
}

.atlerpilot-container {
    text-align: center;
    padding: 40px 50px;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .section-title {
        font-size: 36px;
        line-height: 40px;
        font-weight: bold;
        color: #000000;
    }

    .atlerpilot-desc {
        font-size: 20px;
        line-height: 28px;
        font-weight: 400;
        color: #4B5563;
    }

    .atler-button {
        font-size: 16px;
        line-height: 24px;
        font-weight: 500;
        color: #FFFFFF;
        background-color: #35A5DB;
        border-radius: 8px;
        padding: 10px 20px;
        width: 203px;
        align-self: center;
    }

    .atlerpilot-sub-desc {
        font-size: 14px;
        line-height: 20px;
        font-weight: 400;
        color: #6B7280;
    }
}

.info-container {
    padding: 40px 50px;
    display: flex;
    justify-content: space-between;
    gap: 40px;

    .info-content {
        background: #FFFFFF;
        padding: 30px 25px;
        border-radius: 16px;
        box-shadow: 0px 1px 2px 0px #0000000D;

        .image {
            margin-bottom: 10px;
        }

        h3 {
            font-size: 20px;
            line-height: 28px;
            color: #000000;
            margin-bottom: 12px;
        }

        p {
            color: #4B5563;
            line-height: 24px;
            font-size: 16px;
        }
    }

}

.pilot-container {
    padding: 40px 50px;

    .pilot-content {
        background-color: #FFFFFF;
        border-radius: 16px;
        border: 1px solid #E5E7EB;
    }

    .atler-row {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: 10px;
    }

    .atler-content {
        padding: 38px;
    }

    .section-title {
        font-size: 36px;
        line-height: 40px;
        font-weight: bold;
        color: #000000;
        margin-bottom: 20px;
    }

    .atler-column {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .atler-title {
        font-size: 14px;
        line-height: 20px;
        font-weight: 500;
        color: #35A5DB;
    }

    .atler-sub-title {
        font-size: 30px;
        line-height: 36px;
        font-weight: bold;
        color: #000000;
    }

    .atler-sub-desc {
        font-size: 18px;
        line-height: 28px;
        font-weight: 400;
        color: #4B5563;
    }

    .atler-sub-row {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-bottom: 20px;
    }

    .atler-sub-content {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .icon-content {
        content: url(https://api.iconify.design/charm/tick.svg);
        font-weight: 700;
        background: #DCFCE7;
        border-radius: 100px;
        padding: 4px;
        font-size: 14px;
        margin: 5px;
        color: #16A34A;
    }

    .atler-sub-item {
        font-size: 16px;
        line-height: 24px;
        font-weight: 400;
        color: #374151;
    }

    .atler-button {
        font-size: 16px;
        line-height: 24px;
        font-weight: 500;
        color: #FFFFFF;
        background-color: #35A5DB;
        border-radius: 8px;
        padding: 10px 20px;
        width: 203px;
        margin-top: 40px;

    }

    .atler-dashboard {
        width: 576px;
        border-top-right-radius: 16px;
        border-bottom-right-radius: 16px;
    }
}

@media only screen and (max-width: 1250px) {
    .detail-content {
        padding-bottom: 60px;

        .security-head {
            font-size: 24px !important;
        }

        .premium-head {
            align-self: center;
        }

        .featured-desc {
            align-self: center;
        }

        .featured-head {
            align-self: center;
            font-size: 28px !important;
        }

        .feature-item {
            align-items: flex-start !important;
        }
    }

    .security-section {
        padding: 20px 10px;

        .security-banner {
            gap: 2px !important;
            justify-content: center !important;
        }
    }

    .security-desc {
        width: 100% !important;
    }

    .filter-select {
        width: 100% !important;
    }

    .business-head {
        font-size: 30px !important;
    }

    .help-cards {
        justify-content: center !important;
    }

    .premium-section {
        padding: 20px 10px;

        .premium-cards {
            align-items: center !important;
            justify-content: center !important;

            .premium-card {
                width: 386px !important;
                height: -webkit-fill-available;
                gap: 10px !important;
            }
        }
    }

    .pricing {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 10px !important;
    }

    .atlerpilot-container {
        padding: 20px !important;
    }

    .info-container {
        padding: 20px !important;
        justify-content: center !important;
        gap: 20px !important;
        flex-wrap: wrap !important;
    }

    .info-content {
        width: 300px;
    }

    .section-title {
        font-size: 24px !important;
    }

    .atlerpilot-desc {
        font-size: 16px !important;
    }

    .pilot-container {
        padding: 20px !important;
    }

    .atler-row {
        padding: 20px !important;
        gap: 20px !important;
    }

    .atler-content {
        padding: 0px !important;
    }

    .atler-dashboard {
        width: 100% !important;
    }
}