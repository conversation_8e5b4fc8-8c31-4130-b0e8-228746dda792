import { CommonModule } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { Router, NavigationEnd } from '@angular/router';
import { MaterialModule } from '../../material-module';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { CookieSettings } from '../cookie-settings/cookie-settings';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-cookie-policy',
  imports: [CommonModule, MaterialModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './cookie-policy.html',
  styleUrl: './cookie-policy.scss'
})
export class CookiePolicy {
  private routerSubscription: Subscription;
  constructor(
    public dialogRef: MatDialogRef<CookiePolicy>,
    private dialog: MatDialog,
    private router: Router,
    @Inject(PLATFORM_ID) private platformId: Object
  ) { }


  ngOnInit() {
    if (isPlatformBrowser(this.platformId)) {
      this.routerSubscription = this.router.events.subscribe(event => {
        if (event instanceof NavigationEnd) {
          if (event.url?.includes('legal/cookie-policy')) {
            this.dialogRef.close();
          }
        }
      });
    }
  }

  cookieSettings() {
    const dialogRef = this.dialog.open(CookieSettings, {
      width: '600px',
      height: '500px',
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result && result.categories) {
        this.dialogRef.close({ 'categories': result.categories });
      }
    });
  }

  acceptCookies() {
    this.dialogRef.close({
      'categories': [
        "essential",
        "analytics",
        "functional",
        "marketing",
        "location"
      ]
    });
  }
  ngOnDestroy() {
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }
}