<div class="custom-overlay" *ngIf="loading">
    <div class="custom-overlay__inner">
        <div class="custom-loader">
            <svg class="progress-circle" viewBox="0 0 50 50">
                <circle class="progress-bg" cx="25" cy="25" r="20" />
                <circle class="progress-bar" cx="25" cy="25" r="20" />
            </svg>
            <div class="loader-title">Loading data</div>
            <div class="loader-desc">Please wait...</div>
        </div>
    </div>
</div>
<div>
    <app-header></app-header>
    <div class="container">
        <app-search-panel></app-search-panel>
    </div>
    <div *ngIf="!showBilling" id="cartContent">
        <div class="head-row">
            <div (click)="backToInstances()" class="back-arrow">
                <iconify-icon icon="material-symbols:arrow-back-rounded"></iconify-icon> Back to Instance
            </div>
            <div class="pricing-head">Your Cart</div>
        </div>
        <div class="cart-content">
            <div class="cart-items">
                <div *ngFor="let item of cartItems || []" class="cart-item">
                    <div class="image-container">
                        <div class="item-image">
                            <img *ngIf="item?.RESOURCE_TYPE === 'external'"
                                [src]="'assets/images/' + providerIconMap[item?.SERVICE_DETAILS?.service_provider | lowercase]" />
                            <img *ngIf="item?.RESOURCE_TYPE === 'internal'"
                                [src]="'assets/images/' + providerIconMap[item?.SERVICE_DETAILS?.selection?.service_provider | lowercase]" />
                        </div>
                        <div class="reve-image">
                            <img src="assets/images/reve-logo.jpg" />
                        </div>
                    </div>
                    <div class="item-details">
                        <div class="item-header">
                            <div class="item-name" *ngIf="item?.RESOURCE_TYPE === 'external'">
                                {{item?.SERVICE_DETAILS?.product_name}}</div>
                            <div class="item-name" *ngIf="item?.RESOURCE_TYPE === 'internal'">
                                {{item?.SERVICE_DETAILS?.title}}</div>
                        </div>
                        <div class="item-provider" *ngIf="item?.RESOURCE_TYPE === 'external'">
                            {{providerNameMap[item?.SERVICE_DETAILS?.service_provider | lowercase]}}
                        </div>
                        <div class="item-provider" *ngIf="item?.RESOURCE_TYPE === 'internal'">
                            {{providerNameMap[item?.SERVICE_DETAILS?.selection?.service_provider | lowercase]}}
                        </div>
                        <div class="item-tags-row">
                            <span class="tag" *ngIf="item?.SERVICE_DETAILS?.payment_plan">
                                {{formatText(item?.SERVICE_DETAILS?.payment_plan)}}</span>
                            <span class="tag" *ngIf="item?.SERVICE_DETAILS?.vm_name">
                                {{item?.SERVICE_DETAILS?.vm_name}}</span>
                            <span class="tag" *ngIf="item?.SERVICE_DETAILS?.ram_gb">
                                {{item?.SERVICE_DETAILS?.ram_gb}} GB</span>
                            <span class="tag" *ngIf="item?.SERVICE_DETAILS?.vcpu_cores">
                                {{item?.SERVICE_DETAILS?.vcpu_cores}} vCPUs</span>
                            <span class="tag" *ngIf="item?.SERVICE_DETAILS?.storage_spec">
                                {{item?.SERVICE_DETAILS?.storage_spec}}</span>
                        </div>
                        <div class="item-controls-row">
                            <div class="item-qty-row">
                                <button mat-button color="basic" class="qty-btn-minus"
                                    (click)="decreaseQuantity(item)">−</button>
                                <span class="item-qty">{{item?.QUANTITY}}</span>
                                <button mat-button color="basic" class="qty-btn-plus"
                                    (click)="increaseQuantity(item)">+</button>
                            </div>
                        </div>
                    </div>
                    <div class="item-actions">
                        <div class="item-price-details" *ngIf="item?.RESOURCE_TYPE === 'external'">
                            <div class="payment-option"
                                *ngIf="(item?.SERVICE_DETAILS?.payment_option | lowercase) == 'partial upfront' || (item?.SERVICE_DETAILS?.payment_option | lowercase) == 'all upfront'">
                                {{formatText(item?.SERVICE_DETAILS?.payment_option)}}
                            </div>
                            <div class="price-item"
                                *ngIf="(item?.SERVICE_DETAILS?.payment_option | lowercase) == 'partial upfront' || (item?.SERVICE_DETAILS?.payment_option | lowercase) == 'all upfront'">
                                {{formatText(item?.SERVICE_DETAILS?.term)}} -
                                <span>${{(item?.SERVICE_DETAILS?.upfront_amount_usd *
                                    item?.QUANTITY)?.toFixed(2)}}</span>
                            </div>
                            <div class="price-item">Monthly -
                                <span>${{(item?.SERVICE_DETAILS?.monthly_rate_usd * item?.QUANTITY)?.toFixed(2)}}</span>
                            </div>
                            <div class="item-price">
                                <span class="price-period">Avg.</span>
                                ${{getMonthlyPrice(item?.SERVICE_DETAILS?.hourly_rate_usd,
                                item?.SERVICE_DETAILS?.service_provider, item?.QUANTITY)}}
                                <span class="price-period">/mo</span>
                            </div>
                        </div>
                        <div class="item-price" *ngIf="item?.RESOURCE_TYPE === 'internal'">
                            ${{((item?.SERVICE_DETAILS?.selection?.cost) *
                            item?.QUANTITY)?.toFixed(2)}}</div>
                        <div class="item-action-btns item-action-btns-opposite">
                            <button mat-button class="view-btn"
                                (click)="viewInstanceDetails(item?.RESOURCE_TYPE, item?.SERVICE_DETAILS)">
                                View Details</button>
                            <button mat-button class="remove-btn" (click)="removeItemFromCart(item?.ID)">Remove</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="order-summary">
                <h3>Order Summary</h3>
                <!-- <div class="summary-row">
                    <span>Subtotal ({{itemCount}} {{itemCount === 1 ? 'item' : 'items'}})</span>
                </div> -->
                <!-- <div class="summary-row">
                    <span>$/Hr</span>
                    <span>${{formattedHrSubtotal}}</span>
                </div> -->
                <!-- <div class="summary-row">
                    <span>$/Mn</span>
                    <span>${{formattedMnSubtotal}}</span>
                </div> -->
                <div class="summary-row total">
                    <span>Total</span>
                    <span>${{formattedMnSubtotal}}</span>
                </div>
                <span class="order-summary-note">Billed monthly. Prices may vary based on actual usage.</span>
                <!-- <div class="promo-code">
                    <label for="promoCode">Promo Code</label>
                    <div class="promo-code-row">
                        <input type="text" [(ngModel)]="promoCode" [ngModelOptions]="{standalone: true}"
                            placeholder="Promo Code" class="promo-input" />
                        <button mat-button (click)="applyPromoCode()" [disabled]="!promoCode">Apply</button>
                    </div>
                </div> -->
                <button class="btn-purchase" (click)="proceedToCheckout()" [disabled]="cartItems.length === 0">
                    Proceed to Checkout
                </button>
                <!-- <div class="order-summary-actions">
                    <button mat-button class="export-btn">
                        <iconify-icon icon="mdi:file-excel-outline" width="16" height="16"></iconify-icon>
                        Export Excel
                    </button>
                    <button mat-button class="export-btn">
                        <iconify-icon icon="mdi:headset" width="16" height="16"></iconify-icon>
                        Contact Sales
                    </button>
                </div> -->
                <!-- <div class="payment-methods">
                    <div class="payment-icons">
                        <iconify-icon icon="mage:visa" width="24" height="24" class="payment-icon"></iconify-icon>
                        <iconify-icon icon="streamline-logos:mastercard-logo" width="24" height="24"></iconify-icon>
                        <iconify-icon icon="streamline-logos:paypal-logo" width="24" height="24"></iconify-icon>
                        <iconify-icon icon="streamline-logos:apple-pay-logo" width="24" height="24"></iconify-icon>
                    </div>
                </div>
                <div class="need-help">
                    <div class="need-help-title">Need Help?</div>
                    <div class="help-links">
                        <a href="#" class="help-link">
                            <iconify-icon icon="mdi:headset" class="help-icon"></iconify-icon>
                            Contact Support
                        </a>
                        <a href="#" class="help-link">
                            <iconify-icon icon="mdi:currency-usd" class="help-icon"></iconify-icon>
                            Pricing Details
                        </a>
                        <a href="#" class="help-link">
                            <iconify-icon icon="mdi:help-circle" class="help-icon"></iconify-icon>
                            FAQs
                        </a>
                    </div>
                </div> -->
            </div>
        </div>

        <!-- <div class="special-offers-container">
            <h2>Special Offers</h2>
            <div class="special-offers-list">
                <div *ngFor="let offer of specialOffers" class="offer-card">
                    <div class="offer-header">
                        <img [src]="offer.providerIcon" [alt]="offer.provider + ' logo'" class="offer-icon" />
                        <div class="offer-title-section">
                            <div class="offer-title">{{ offer.title }}</div>
                            <div class="offer-validity">Valid until {{ offer.validUntil | date:'longDate' }}</div>
                        </div>
                    </div>
                    <div class="offer-description">{{ offer.description }}</div>
                    <div class="offer-amount">{{ offer.amount }}</div>
                    <button mat-button>
                        Apply Offer
                    </button>
                </div>
            </div>
        </div> -->

        <!-- <div class="you-might-also-like">
            <h2>You might also like</h2>
            <div class="cards-container">
                <div *ngFor="let item of recommendedItems" class="card">
                    <div class="card-header">
                        <img class="card-icon" [src]="item.icon" [alt]="item.title + ' icon'" width="40" height="40" />
                        <div class="card-title-container">
                            <h3 class="card-title" title="{{item.title}}">{{ item.title }}</h3>
                            <p class="card-subtitle">{{ item.subtitle }}</p>
                        </div>
                    </div>
                    <p class="card-description">{{ item.description }}</p>
                    <div class="card-price-container">
                        <p class="card-price">{{ item.price }}</p>
                        <button class="add-to-cart-btn">
                            Add to Cart
                        </button>
                    </div>
                </div>
            </div>
        </div> -->

        <!-- <div class="featured-cloud-solutions">
            <h2>Featured Cloud Solutions</h2>
            <p class="description">Discover premium offers from leading cloud providers</p>
            <div class="solutions-grid">
                <div class="solution-card" *ngFor="let solution of cloudSolutions">
                    <div class="image-container">
                        <img [src]="solution.image" [alt]="solution.title" />
                        <span *ngIf="solution.tag" class="tag" [ngClass]="solution.tagClass">{{ solution.tag }}</span>
                    </div>
                    <h3>{{ solution.title }}</h3>
                    <p class="subtitle">{{ solution.subtitle }}</p>
                    <div class="price-container">
                        <p class="price">From <span>{{ solution.price }}</span></p>
                        <a href="#" class="learn-more">Learn More →</a>
                    </div>
                </div>
            </div>
        </div> -->
    </div>

    <div *ngIf="showBilling" id="billingContent">
        <div class="head-row">
            <div (click)="backToCart()" class="back-arrow" *ngIf="!mobile">
                <iconify-icon icon="material-symbols:arrow-back-rounded"></iconify-icon>Back to Cart
            </div>
            <div (click)="backToInstances()" class="back-arrow" *ngIf="mobile">
                <iconify-icon icon="material-symbols:arrow-back-rounded"></iconify-icon>
            </div>
            <div class="pricing-head">Checkout Your Order</div>
        </div>
        <div class="detail-content">
            <div class="billing-wrapper">
                <form [formGroup]="billingForm" autocomplete="off">
                    <h2>Billing Information</h2>
                    <div class="form-row">
                        <label for="fullName">Full Name<span class="required">*</span></label>
                        <input id="fullName" type="text" formControlName="fullName" required readonly />
                        <mat-error class="error"
                            *ngIf="billingForm.controls['fullName']?.touched && billingForm.get('fullName')?.hasError('required')">
                            Required
                        </mat-error>
                    </div>
                    <div class="form-row two-cols">
                        <div class="form-column">
                            <label for="email">Email Address<span class="required">*</span></label>
                            <input id="email" type="email" formControlName="email" required readonly />
                            <mat-error class="error"
                                *ngIf="billingForm.controls['email']?.touched && billingForm.get('email')?.hasError('required')">
                                Required
                            </mat-error>
                        </div>
                        <div class="form-column">
                            <label for="phone">Phone Number</label>
                            <input id="phone" type="tel" formControlName="phone" readonly />
                        </div>
                    </div>
                    <div class="form-row">
                        <label for="company">Company Name</label>
                        <input id="company" type="text" formControlName="company" />
                    </div>
                    <div class="billing-address">
                        <div class="billing-address-header">Billing Address</div>
                        <div class="form-row two-cols">
                            <div class="form-column">
                                <input placeholder="Street address" type="text" formControlName="street" />
                            </div>
                            <div class="form-column">
                                <input placeholder="Area" type="text" formControlName="area" />
                            </div>
                        </div>
                        <div class="form-row two-cols">
                            <div class="form-column">
                                <input placeholder="City" type="text" formControlName="city" />
                            </div>
                            <div class="form-column">
                                <input placeholder="State/Province" type="text" formControlName="state" />
                            </div>
                        </div>
                        <div class="form-row two-cols">
                            <div class="form-column">
                                <input placeholder="Postal/ZIP code" type="number" formControlName="postalCode" />
                            </div>
                            <div class="form-column">
                                <mat-select placeholder="Select Country *" panelClass="codeClass"
                                    formControlName="country" required>
                                    <mat-option>
                                        <ngx-mat-select-search placeholderLabel="Search" [formControl]="filterCountry"
                                            noEntriesFoundLabel="No Matching Found">
                                            <mat-icon ngxMatSelectSearchClear class="close-icon">close</mat-icon>
                                        </ngx-mat-select-search>
                                    </mat-option>
                                    <mat-option *ngFor="let country of filterList || []" [value]="country.ID">
                                        {{country.NAME}}
                                    </mat-option>
                                </mat-select>
                                <mat-error class="error"
                                    *ngIf="billingForm.controls['country']?.touched && billingForm.get('country')?.hasError('required')">
                                    Required
                                </mat-error>
                            </div>
                        </div>
                    </div>
                </form>
                <button mat-button *ngIf="mobile" class="btn-purchase" [disabled]="!billingForm.valid"
                    (click)="completePurchase()">Complete Purchase</button>
            </div>

            <div class="order-wrapper" *ngIf="!mobile">
                <h2>Order Summary</h2>
                <div class="order-items">
                    <div class="order-item" *ngFor="let item of cartItems || []">
                        <div class="image-container">
                            <img class="provider-icon" *ngIf="item?.RESOURCE_TYPE === 'external'"
                                [src]="'assets/images/' + providerIconMap[item?.SERVICE_DETAILS?.service_provider | lowercase]" />
                            <img class="provider-icon" *ngIf="item?.RESOURCE_TYPE === 'internal'"
                                [src]="'assets/images/' + providerIconMap[item?.SERVICE_DETAILS?.selection?.service_provider | lowercase]" />
                            <img class="provider-icon" src="assets/images/reve-logo.jpg" />
                        </div>
                        <div class="order-item-details">
                            <div class="item-title-row" *ngIf="item?.RESOURCE_TYPE === 'external'">
                                <div class="item-title">
                                    {{item?.SERVICE_DETAILS?.service_provider | uppercase}} -
                                    {{item?.SERVICE_DETAILS?.vm_name}}</div>
                            </div>
                            <div class="item-title-row" *ngIf="item?.RESOURCE_TYPE === 'internal'">
                                <div class="item-title">
                                    {{item?.SERVICE_DETAILS?.selection?.service_provider | uppercase}}
                                    {{item?.SERVICE_DETAILS?.title}}</div>
                                <div class="item-price">${{((item?.SERVICE_DETAILS?.selection?.cost) *
                                    item?.QUANTITY)?.toFixed(2)}}
                                </div>
                            </div>
                            <div class="item-provider" *ngIf="item?.RESOURCE_TYPE === 'external'">
                                {{providerNameMap[item?.SERVICE_DETAILS?.service_provider | lowercase]}}
                            </div>
                            <div class="item-provider" *ngIf="item?.RESOURCE_TYPE === 'internal'">
                                {{providerNameMap[item?.SERVICE_DETAILS?.selection?.service_provider | lowercase]}}
                            </div>
                            <div class="item-tags">
                                <span class="tag" *ngIf="item?.SERVICE_DETAILS?.payment_plan">
                                    {{formatText(item?.SERVICE_DETAILS?.payment_plan)}}</span>
                                <span class="tag" *ngIf="item?.SERVICE_DETAILS?.ram_gb">
                                    {{item?.SERVICE_DETAILS?.ram_gb}} GB</span>
                                <span class="tag" *ngIf="item?.SERVICE_DETAILS?.vcpu_cores">
                                    {{item?.SERVICE_DETAILS?.vcpu_cores}} vCPUs</span>
                                <span class="tag" *ngIf="item?.SERVICE_DETAILS?.storage_spec">
                                    {{item?.SERVICE_DETAILS?.storage_spec}}</span>
                            </div>
                            <div class="order-price-details" *ngIf="item?.RESOURCE_TYPE === 'external'">
                                <div class="payment-option"
                                    *ngIf="(item?.SERVICE_DETAILS?.payment_option | lowercase) == 'partial upfront' || (item?.SERVICE_DETAILS?.payment_option | lowercase) == 'all upfront'">
                                    {{formatText(item?.SERVICE_DETAILS?.payment_option)}}
                                </div>
                                <div class="price-item"
                                    *ngIf="(item?.SERVICE_DETAILS?.payment_option | lowercase) == 'partial upfront' || (item?.SERVICE_DETAILS?.payment_option | lowercase) == 'all upfront'">
                                    {{formatText(item?.SERVICE_DETAILS?.term)}} -
                                    <span>${{(item?.SERVICE_DETAILS?.upfront_amount_usd *
                                        item?.QUANTITY)?.toFixed(2)}}</span>
                                </div>
                                <div class="price-item">Monthly -
                                    <span>${{(item?.SERVICE_DETAILS?.monthly_rate_usd *
                                        item?.QUANTITY)?.toFixed(2)}}</span>
                                </div>
                                <div class="item-price">
                                    <span class="price-period">Avg.</span>
                                    ${{getMonthlyPrice(item?.SERVICE_DETAILS?.hourly_rate_usd,
                                    item?.SERVICE_DETAILS?.service_provider, item?.QUANTITY)}}
                                    <span class="price-period">/mo</span>
                                </div>
                            </div>
                            <div class="item-activation-row">
                                <div class="item-qty">Qty: {{item?.QUANTITY}}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="order-totals"> -->
                <!-- <div>
                        <span>Subtotal ({{itemCount}} {{itemCount === 1 ? 'item' : 'items'}})</span>
                    </div> -->
                <!-- <div>
                        <span>$/Hr</span>
                        <span>${{formattedHrSubtotal}}</span>
                    </div> -->
                <!-- <div>
                        <span>$/Mn</span>
                        <span>${{formattedMnSubtotal}}</span>
                    </div> -->
                <!-- </div> -->
                <!-- <div class="promo-code">
                    <label for="promoCode">Promo Code</label>
                    <div class="promo-code-row">
                        <input id="promoCode" type="text" [(ngModel)]="promoCode" [ngModelOptions]="{standalone: true}"
                            placeholder="Enter code" />
                        <button mat-button (click)="applyPromoCode()" [disabled]="!promoCode">Apply</button>
                    </div>
                </div> -->
                <div class="total">
                    <span>Total</span>
                    <span>${{formattedMnSubtotal}}</span>
                </div>
                <span class="total-text">Billed monthly. Prices may vary based on actual usage.</span>
                <button mat-button class="btn-purchase" [disabled]="!billingForm.valid"
                    (click)="completePurchase()">Complete Purchase</button>
            </div>
        </div>
    </div>
</div>