import { HttpHandlerFn, HttpInterceptorFn, HttpRequest } from '@angular/common/http';
import { BYPASS_LOG } from './services/auth';
import { isPlatformBrowser } from '@angular/common';
import { inject, PLATFORM_ID } from '@angular/core';

export const configInterceptor: HttpInterceptorFn = (req: HttpRequest<unknown>, next: HttpHandlerFn) => {
  const platformId = inject(PLATFORM_ID);
  if (isPlatformBrowser(platformId)) {
    const userData = window.localStorage.getItem('userInfo') || '{}';
    const userInfo = userData ? JSON.parse(userData) : null;
    if (req.context.get(BYPASS_LOG) === true) {
      return next(req);
    } else {
      return next(req.clone({
        setHeaders: {
          'x-access-token': userInfo?.access_token,
          'id-token': userInfo?.id_token,
        }
      }));
    }
  }
  return next(req);
};