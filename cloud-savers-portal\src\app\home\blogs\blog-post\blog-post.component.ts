import { CommonModule, isPlatform<PERSON>rowser } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, HostListener, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { MaterialModule } from '../../../material-module';
import { Header } from '../../header/header';
import { Footer } from '../../footer/footer';
import { MobileFooter } from '../../mobile-footer/mobile-footer';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { HttpClientModule } from '@angular/common/http';
import { MetaService } from '../../../services/meta.service';
import { environment } from '../../../../environments/environment';
import { Auth } from '../../../services/auth';
import { richTextResolver } from '@storyblok/richtext';

@Component({
  selector: 'app-blog-post',
  standalone: true,
  imports: [
    CommonModule,
    MaterialModule,
    Header,
    Footer,
    MobileFooter,
    RouterModule,
    HttpClientModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './blog-post.component.html',
  styleUrl: './blog-post.component.scss'
})
export class BlogPostComponent implements OnInit {
  mobile: boolean = false;
  blogPost: any = {};
  loading: boolean = false;
  error: string | null = null;
  blogUrl: string = '';
  public websiteUrl = `${environment.PORTAL_URL}`;

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenWidth();
  }

  constructor(@Inject(PLATFORM_ID)
    private platformId: Object,
    private route: ActivatedRoute,
    private router: Router,
    private metaService: MetaService,
    private authService: Auth
  ) { }

  ngOnInit(): void {
    this.scrollToTop();
    this.checkScreenWidth();
    this.route.paramMap.subscribe(params => {
      this.blogUrl = params.get('id');
      this.loadBlogPost(this.blogUrl);
    });
  }

  private updateMetaTags(blogPost: any): void {
    if (!blogPost?.content) return;
    let hreflangs = [
      { "lang": "en-us" },
      { "lang": "x-default" },
      { "lang": "en" }
    ]
    const { meta_title, meta_description, canonicalUrl } = blogPost.content;
    this.metaService.updateTitle(meta_title);
    this.metaService.updateDescription(meta_description);
    this.metaService.updateOgTitle(meta_title);
    this.metaService.updateOgDescription(meta_description);
    const url = canonicalUrl || this.router.url;
    this.metaService.updateCanonicalUrl(this.websiteUrl, url);
    this.metaService.updateHreflangTags(hreflangs.map(({ lang }) => ({
      hreflang: lang,
      href: url
    })), this.websiteUrl);
  }

  async loadBlogPost(blogUrl: string) {
    this.error = null;
    const res = await this.authService.getBlog(blogUrl!);
    this.blogPost = res?.data?.story;

    const { render } = richTextResolver();
    const html = render(this.blogPost?.content?.body);
    if (isPlatformBrowser(this.platformId)) {
      document.querySelector<HTMLDivElement>('#sb_body_content')!.innerHTML = `${html}`;
    }
    
    this.updateMetaTags(this.blogPost);
  }

  private checkScreenWidth() {
    if (isPlatformBrowser(this.platformId)) {
      if (window.innerWidth <= 1250) {
        this.mobile = true;
      } else {
        this.mobile = false;
      }
    }
  }

  private scrollToTop(): void {
    if (isPlatformBrowser(this.platformId)) {
      window.scrollTo(0, 0);
    }
  }

  backToBlog() {
    this.router.navigate(['/blogs']);
  }

  navigateToContact() {
    this.router.navigate(['/']);
  }

}