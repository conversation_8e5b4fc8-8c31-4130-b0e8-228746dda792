.head-row {
    display: flex;
    align-items: center;
    padding: 20px 50px;
    background-color: #FFFFFF;
    border-bottom: 1px solid #E5E7EB;

    .back-arrow {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 16px;
        color: #374151;
        cursor: pointer;
        text-align: start;
        width: 13%;
    }

    .pricing-head {
        font-size: 20px;
        font-weight: 600;
        color: #000000;
        text-align: center;
        width: 87%;
    }
}

.history-container {
    padding: 20px 50px;
}

table {
    table-layout: fixed;
    width: 100%;
    border: none;
    border-bottom: 1px solid #E5E7EB;
    box-shadow: none;
}

table th,
td {
    text-align: start !important;
}

.mat-mdc-header-row {
    background: #F9FAFB;
    border: none;
}

.table-header {
    font-size: 14px;
    font-weight: 500;
    color: #000000;
    width: 20%;
}

.table-header.services-column {
    width: 40%;
}

table td {
    font-size: 13px;
    color: #000000;
    font-weight: 500;
    line-height: 20px;
}

.table-cell {
    width: 9%;
}

.table-cell.date-column {
    width: 8%;
}

.table-cell.services-column {
    width: 14%;
}

.mdc-data-table__cell,
.mdc-data-table__header-cell {
    padding: 10px 20px;
}

th.mat-header-cell,
td.mat-cell {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 10px;
    text-align: start !important;
}

.service-items {
    display: flex;
    flex-direction: column;
    align-items: start;
    gap: 5px;
}

.service-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-column {
    font-weight: 400;
    color: #6B7280;
}

.provider-logo {
    width: 30px;
    height: auto;
}

.service-name {
    font-size: 13px;
    line-height: 20px;
    color: #111827;
}

@media screen and (max-width: 1250px) {
    .head-row {
        padding: 15px 20px;

        .back-arrow {
            width: auto;
            font-size: 14px;
        }

        .pricing-head {
            font-size: 18px;
        }
    }

    .history-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        padding: 5px;
        margin: 10px 20px;
        position: relative;
    }

    table {
        min-width: 750px;

        th,
        td {
            padding: 10px;
            font-size: 13px;


            &:first-child {
                position: sticky;
                left: -20px;
                z-index: 3;
                box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
                padding: 16px;
            }
        }

        thead {
            position: sticky;
            top: 0;
            background: white;
            z-index: 4;

            th {
                background: white;
                position: relative;
                z-index: 5;

                &:first-child {
                    z-index: 6;
                }
            }
        }
    }

}