.container {
    width: 100%;
    height: auto;
    background-image: url('../../../assets/images/background_img.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding-bottom: 20px;
}

.head-row {
    display: flex;
    align-items: center;
    padding: 20px 50px;
    background-color: #FFFFFF;
    border-bottom: 1px solid #E5E7EB;

    .back-arrow {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 16px;
        color: #374151;
        cursor: pointer;
        text-align: start;
        width: 10%;
    }

    .pricing-head {
        font-size: 20px;
        font-weight: 600;
        color: #000000;
        text-align: center;
        width: 90%;
    }
}

.products-tab {
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: center;
    padding: 0px 50px;
    padding-top: 20px;
    gap: 20px;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;

    .tab {
        padding: 10px 20px;
        background: transparent;
        cursor: pointer;
        font-size: 16px;
        font-weight: 400;
        color: #2C2C2C;
    }

    .tab.selected {
        padding: 10px 20px;
        background: #2C2C2C;
        color: #FFFFFF;
        cursor: pointer;
        font-size: 16px;
        font-weight: 400;
        border-radius: 8px;
    }

    .tab.disabled {
        pointer-events: none;
        cursor: not-allowed;
    }
}

.pricing-content {
    padding: 30px 50px;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .main-content {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        gap: 30px;

        .filter-wrapper {
            width: 20%;
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .results-wrapper {
            width: 80%;
        }

        // .cart-wrapper {
        //     width: 25%;
        //     display: flex;
        //     flex-direction: column;
        //     gap: 25px;
        // }
    }
}

.filter-overlay,
.cart-overlay {
    display: none;
}

.bottom-cart-panel {
    position: sticky;
    left: 0;
    right: 0;
    bottom: 0;
    background: #FFFFFF;
    box-shadow: 0px -4px 4.2px 0px #00000040;
    border-radius: 50px 50px 0 0;
    padding: 16px 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    z-index: 100;

    .cart-items-list {
        display: flex;
        flex: 1;
        align-items: center;
        gap: 30px;
        overflow-x: auto;

        .cart-item {
            display: flex;
            align-items: center;
            gap: 10px;
            background: #f7f8fa;
            border-radius: 8px;
            padding: 8px 16px;
            min-width: 180px;

            .provider-logo {
                width: 32px;
                height: 32px;
                object-fit: contain;
            }

            .cart-item-details {
                display: flex;
                flex-direction: column;
                gap: 5px;
                align-items: center;

                .cart-item-title {
                    font-weight: 500;
                    font-size: 16px;
                    color: #111827;
                }

                .cart-item-type {
                    font-weight: 700;
                    font-size: 14px;
                    color: #6B7280;
                }
            }

            .remove-btn {
                mat-icon {
                    font-size: 15px;
                    color: #9CA3AF;
                    cursor: pointer;
                    width: 20px;
                    height: 18px;
                }
            }
        }
    }

    .go-to-comparison-btn {
        background-color: #35A5DB;
        color: #FFFFFF;
        font-size: 17px;
        padding: 15px 20px;
        border-radius: 8px;
        border: none;

        &:disabled {
            opacity: 0.5;
        }
    }
}

@media only screen and (max-width: 1250px) {

    .container {
        padding-bottom: 0;
    }

    .cart-sidebar {
        position: fixed;
        top: 0;
        right: 0;
        height: 100vh;
        width: 90%;
        max-width: 400px;
        background: white;
        z-index: 1;
        display: flex;
        flex-direction: column;
        transform: translateX(100%);
        transition: transform 0.3s ease-in-out;
        box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
        padding-bottom: 60px;

        &.mobile-visible {
            transform: translateX(0);
        }

        .cart-header {
            flex-shrink: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #e0e0e0;

            h3 {
                margin: 0;
                font-size: 18px;
                font-weight: 500;
                color: #333;
            }

            button {
                color: #666;
            }
        }

        .cart-content {
            flex: 1;
            min-height: 0;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 0 16px;
        }
    }

    .head-row {
        padding: 12px 16px 12px 56px;
        position: relative;

        .pricing-head {
            font-size: 16px;
            text-align: center;
            width: 100%;
            padding: 0 10px;
        }

        .date-display {
            display: none;
        }
    }

    .products-tab {
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
        display: flex;
        padding: 0 50px 5px;
        border-bottom: 1px solid #e0e0e0;
        position: relative;

        &::-webkit-scrollbar {
            display: none;
        }

        .tab {
            font-size: 14px;
            white-space: nowrap;
        }

        .tab.selected {
            color: #35A5DB !important;
            font-weight: 500 !important;
            background: transparent !important;
            border: none !important;
        }

        div {
            padding: 4px 0;
        }
    }

    .pricing-content {
        padding: 15px;

        .main-content {
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding-bottom: 60px;

            .filter-wrapper,
            .results-wrapper,
            .cart-wrapper {
                width: 90% !important;
            }

            .filter-wrapper:not(.mobile-visible) {
                display: none;
            }
        }
    }

    .filter-overlay,
    .cart-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1099;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;

        &.visible {
            opacity: 1;
            visibility: visible;
        }
    }

    .filter-wrapper {
        position: fixed;
        top: 0;
        right: 0;
        height: 100vh;
        width: 90%;
        max-width: 400px;
        background: white;
        z-index: 1000;
        display: flex;
        flex-direction: column;
        right: -100%;
        transition: right 0.3s ease-in-out;
        box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
        padding-bottom: 60px;

        &.mobile-visible {
            right: 0;
        }

        .filter-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #e0e0e0;

            h3 {
                margin: 0;
                font-size: 18px;
                font-weight: 500;
            }

            button {
                min-width: auto;
                padding: 0;
                color: #333;
                background: none;
                border: none;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .filter-content {
            flex: 1;
            overflow-y: auto;
            padding: 16px 16px 0 16px;
        }

        .filter-actions {
            padding: 16px;
            border-top: 1px solid #e0e0e0;
            background: white;

            button {
                width: 100%;
                padding: 12px;
                border-radius: 4px;
                font-weight: 500;
                background: #35A5DB;
                color: white;
                border: none;
                cursor: pointer;
                font-size: 16px;

                &:disabled {
                    background: #cccccc;
                    cursor: not-allowed;
                }
            }
        }
    }

    // .filter-wrapper {
    //     right: -100%;
    //     transform: translateX(100%);
    // }

    // .cart-wrapper {
    //     left: -100%;
    //     transform: translateX(-100%);
    // }

    body.filter-open,
    body.cart-open {
        overflow: hidden;
        position: fixed;
        width: 100%;
        height: 100%;
    }
}