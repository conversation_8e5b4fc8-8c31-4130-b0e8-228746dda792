.cookie-policy {
    display: flex;
    flex-direction: column;
    gap: 30px;
    padding: 30px;
    height: 100vh;
    overflow-y: auto;
    -ms-overflow-style: none;
    scrollbar-width: none;

    &::-webkit-scrollbar {
        display: none;
    }
}

.policy-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #E5E7EB;
    padding-bottom: 10px;
}

.title {
    font-size: 20px;
    font-weight: 600;
    color: #35A5DB;
}

.close-icon {
    border-radius: 50%;
    background: #f3f3f3;
    color: #373737 !important;
    padding: 6px;
    height: 30px;
    width: 30px;
}

.policy-desc {
    font-size: 14px;
    color: #4B5563;
    line-height: 23px;
    font-weight: 400;
}

.policy-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.cookies {
    display: flex;
    flex-direction: column;
    gap: 10px;
    border: 1px solid #E5E7EB;
    border-radius: 16px;
    padding: 15px;
}

.cookie-content {
    display: flex;
    justify-content: space-between;
    // align-items: center;
    gap: 10px;
}

.cookie-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.cookie-title {
    font-size: 16px;
    font-weight: 500;
    color: #111827;
}

.cookie-desc {
    font-size: 14px;
    color: #4B5563;
    line-height: 20px;
}

mat-checkbox {
    display: block;
    --mdc-checkbox-selected-icon-color: #35A5DB !important;
    --mdc-checkbox-selected-hover-icon-color: #35A5DB !important;
    --mdc-checkbox-selected-pressed-icon-color: #35A5DB !important;
    --mdc-checkbox-selected-focused-icon-color: #35A5DB !important;
}

::ng-deep mat-slide-toggle {
    .mdc-switch {

        /* Force the track color for enabled state */
        .mdc-switch__track::after {
            background-color: #35A5DB !important;
        }

        /* Fix disabled unchecked state */
        &:not(.mdc-switch--selected).mdc-switch--disabled {
            .mdc-switch__track {
                background-color: #E0E0E0 !important;
                // opacity: 1 !important;
            }

            .mdc-switch__handle::after {
                background-color: #FAFAFA !important;
                border-color: #E0E0E0 !important;
            }
        }
    }

    /* Checked state - enabled */
    .mdc-switch--selected:not(.mdc-switch--disabled) .mdc-switch__track {
        background-color: #35A5DB !important;
        opacity: 1 !important;
    }

    .mdc-switch--selected:not(.mdc-switch--disabled) .mdc-switch__handle::after {
        background-color: #FFFFFF !important;
        border-color: #35A5DB !important;
    }

    /* Checked state - disabled */
    .mdc-switch--selected.mdc-switch--disabled .mdc-switch__track,
    .mdc-switch--selected.mdc-switch--disabled .mdc-switch__track::before,
    .mdc-switch--selected.mdc-switch--disabled .mdc-switch__track::after {
        background-color: #35A5DB !important;
        opacity: 0.5 !important;
    }

    .mdc-switch--selected.mdc-switch--disabled .mdc-switch__handle::after {
        background-color: #FFFFFF !important;
        border-color: #35A5DB !important;
        opacity: 0.7 !important;
    }

    /* Disabled and checked state */
    .mdc-switch--selected.mdc-switch--disabled .mdc-switch__track::before,
    .mdc-switch--selected.mdc-switch--disabled .mdc-switch__track::after {
        background-color: #35A5DB !important;
        opacity: 0.5 !important;
    }

    .mdc-switch--selected.mdc-switch--disabled .mdc-switch__handle::after {
        background-color: #FFFFFF !important;
        border-color: #35A5DB !important;
        opacity: 0.7 !important;
    }

    .mdc-switch--unselected .mdc-switch__icon--off {
        opacity: 0 !important;
    }
}

.cookie-action {
    display: flex;
    justify-content: end;
    gap: 10px;
}

.view-btn {
    background-color: #FFFFFF;
    color: #4B5563;
    font-size: 14px;
    padding: 10px 20px;
    border: none;
    font-weight: 500;
}

.reject-btn {
    background-color: #FFFFFF;
    color: #35A5DB;
    font-size: 16px;
    padding: 10px 20px;
    border: 1px solid #35A5DB;
    border-radius: 8px;
    font-weight: 500;
}

.accept-btn {
    background-color: #35A5DB;
    color: #FFFFFF;
    font-size: 16px;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
}

@media only screen and (max-width: 1250px) {

    .cookie-action {
        flex-wrap: wrap;
        justify-content: center;
    }
}