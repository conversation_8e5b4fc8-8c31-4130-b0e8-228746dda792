import { CommonModule, isPlatformBrowser } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, Inject, PLATFORM_ID } from '@angular/core';
import { MaterialModule } from '../../material-module';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';

@Component({
  selector: 'app-footer',
  imports: [CommonModule, MaterialModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './footer.html',
  styleUrl: './footer.scss'
})
export class Footer {

  email = new FormControl('');

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private dialog: MatDialog,
    private router: Router
  ) { }

  ngOnInit() { }

  viewPrivacyPolicy() {
    if (isPlatformBrowser(this.platformId)) {
      window.open('/legal/privacy-policy', '_blank');
    }
  }

  viewServiceTerms() {
    if (isPlatformBrowser(this.platformId)) {
      window.open('/legal/terms-services', '_blank');
    }
  }

  viewCookiePolicy() {
    window.open('/legal/cookie-policy', '_blank');
  }

}