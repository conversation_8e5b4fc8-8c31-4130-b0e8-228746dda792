import { ChangeDetectorRef, Component, CUSTOM_ELEMENTS_SCHEMA, EventEmitter, HostListener, Output, Inject, PLATFORM_ID } from '@angular/core';
import { MaterialModule } from '../../../material-module';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { CartService } from '../../../services/cart-service';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { Auth } from '../../../services/auth';
import { Login } from '../../../auth/login/login';
import { Notification } from '../../../services/notification';
import { colorObj } from '../../../shared/color-object';
import { CostAnalysis } from '../cost-analysis';
import { Subject, takeUntil } from 'rxjs';
import { FormControl } from '@angular/forms';
import { environment } from '../../../../environments/environment';
import { PROVIDER_ICON_MAP } from '../../../shared/filter-headers.constants';

@Component({
  selector: 'app-cost-compare-results',
  imports: [
    CommonModule,
    MaterialModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './cost-compare-results.html',
  styleUrl: './cost-compare-results.scss'
})
export class CostCompareResults {

  priceFilters: any = [];
  priceFilter = new FormControl(1);
  pricingData: any[] = [];
  currentPage = 1;
  totalPages = 1;
  totalRecords = 0;
  matchingRecords = 0;
  APIResponseTime = 0;
  searchResponseTime = 0;
  pageLimit = environment.PER_PAGE;
  maxGroupedLimit = environment.MAX_GROUPED_LIMIT;
  @Output() loadingChange = new EventEmitter<boolean>();
  noResults: boolean = true;
  get providerIconMap() {
    return PROVIDER_ICON_MAP;
  }
  selectedCartIds: string[] = [];
  selectedCartQuantities: { [record_id: string]: number } = {};
  resourceIdToCartId: { [resourceId: string]: string } = {};
  private destroy$ = new Subject<void>();
  public monthlyHours = environment.HOURS_IN_A_MONTH;
  prodEnvironment = environment.production;
  compareInstances: any[] = [];
  mobile: boolean = false;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenWidth();
  }

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private cartService: CartService,
    private router: Router,
    private dialog: MatDialog,
    private authService: Auth,
    private notify: Notification,
    private costAnalysis: CostAnalysis,
    public cdr: ChangeDetectorRef
  ) {
    this.cartService.cartUpdated$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.refreshCartState();
      });
    this.cartService.cartUpdated$.next();
  }

  ngOnInit() {
    this.checkScreenWidth();
    if (isPlatformBrowser(this.platformId)) {
      const filters = window.localStorage.getItem('SORTING_FILTERS') || '[]';
      this.priceFilters = filters ? JSON.parse(filters) : [];
    }
    this.loadCompareInstances();
  }

  private checkScreenWidth() {
    if (isPlatformBrowser(this.platformId)) {
      if (window.innerWidth <= 1250) {
        this.mobile = true;
      } else {
        this.mobile = false;
      }
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngAfterViewInit() {
    this.checkScreenWidth();
  }

  resetPagination() {
    this.currentPage = 1;
    this.totalPages = 1;
    this.totalRecords = 0;
    this.APIResponseTime = 0;
    this.searchResponseTime = 0;
    this.matchingRecords = 0;
    this.noResults = true;
    this.pricingData = [];
  }

  onPriceFilterChange(value) {
    this.costAnalysis.onSortFilterChange(value);
    this.resetPagination();
  }

  setCurrentPage(page: number) {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      // Notify parent to handle page change
      this.costAnalysis.onPageChange(page);
    }
  }

  private refreshCartState() {
    if (!this.authService.isLoggedIn()) {
      return;
    }
    this.cartService.getCartItems().subscribe({
      next: (itemsObj) => {
        this.selectedCartIds = [];
        this.resourceIdToCartId = {};
        this.selectedCartQuantities = {};
        const arr = Array.isArray(itemsObj?.object) ? itemsObj.object : [];
        if (arr.length > 0) {
          arr.forEach((item: any) => {
            const resourceId = String(item?.RESOURCE_ID);
            const cartId = String(item?.ID);
            this.selectedCartIds.push(resourceId);
            this.selectedCartQuantities[resourceId] = item?.QUANTITY;
            this.resourceIdToCartId[resourceId] = cartId;
          });
        }
        this.selectedCartIds = [...this.selectedCartIds];
        this.cdr.detectChanges();
      },
      error: (e) => {
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        );
      }
    });
  }

  setPricingData(resp: any, page: number, APIResponseTime: number) {
    this.currentPage = page;
    let documentsObj = resp?.RESULT[0] || {};
    let pricingDataArray = [];
    let groupedHitsArray = [];
    // this.pricingData = resp?.results || [];
    // if (!!this.pricingData && this.pricingData.length !== 0) {
    //   for (let provider of this.pricingData) {
    //     this.matchingRecords = provider?.found || 0;
    //   }
    // }
    let endIndex = (this.currentPage - 1) * this.pageLimit;
    if (!!documentsObj.grouped_hits) {
      for (let groupedHits of documentsObj.grouped_hits) {
        groupedHitsArray = groupedHits.hits;
        groupedHitsArray.splice(0, endIndex);
        pricingDataArray.push(...groupedHitsArray);
      }
      this.matchingRecords = documentsObj?.found_docs || 0;
    }
    else {
      pricingDataArray.push(...documentsObj.hits);
      this.matchingRecords = documentsObj?.found || 0;
    }
    // Sort pricingDataArray by monthly price (ascending)
    // pricingDataArray.sort((a, b) => {
    //   const aPrice = parseFloat(this.getMonthlyPrice(a.document?.hourly_rate_usd, a.document?.service_provider));
    //   const bPrice = parseFloat(this.getMonthlyPrice(b.document?.hourly_rate_usd, b.document?.service_provider));
    //   return aPrice - bPrice;
    // });
    this.pricingData = pricingDataArray;
    this.totalRecords = documentsObj?.out_of || 0;
    this.APIResponseTime = APIResponseTime / 1000;
    this.searchResponseTime = (documentsObj?.search_time_ms || 0) / 1000;
    if (!!documentsObj.grouped_hits) {
      this.totalPages = this.matchingRecords > 0 ? Math.ceil(this.maxGroupedLimit / this.pageLimit) : 1;
    }
    else {
      // Calculate total pages based on total records and page limit
      this.totalPages = this.matchingRecords > 0 ? Math.ceil(this.matchingRecords / this.pageLimit) : 1;
    }
    // Update no results logic based on total records, not current page data
    this.noResults = this.matchingRecords === 0;
  }

  hasNextPage(): boolean {
    return this.currentPage < this.totalPages;
  }

  getRecordRangeText(): string {
    if (this.matchingRecords === 0) {
      return "No results found";
    }
    else {
      if (this.prodEnvironment === true) {
        return `Showing ${this.matchingRecords} results out of ${this.totalRecords} in total ${this.APIResponseTime} seconds`;
      }
      else {
        return `Showing ${this.matchingRecords} results out of ${this.totalRecords} in total ${this.APIResponseTime} seconds (TS Time - ${this.searchResponseTime} seconds)`;
      }
    }
  }

  viewInstanceDetails(instance: any) {
    const serviceProvider = this.slugify(instance.service_provider);
    const productName = this.slugify(instance.product_name);
    const slug = serviceProvider + '-' + productName + '--' + instance.record_id;
    this.router.navigate(['/service', slug]);
  }

  slugify(text: string): string {
    return text
      ?.toString()
      ?.toLowerCase()
      ?.trim()
      ?.replace(/[\/\s]+/g, '-')
      ?.replace(/[^\w-]+/g, '')
      ?.replace(/-+/g, '-')
      ?.replace(/^-+|-+$/g, '');
  }

  addToCart(instance: any) {
    const id = String(instance?.record_id);
    if (!this.authService.isLoggedIn()) {
      const dialogRef = this.dialog.open(Login, {
        width: '558px',
        height: 'auto',
        disableClose: true
      });
      dialogRef.afterClosed().subscribe(result => {
        if (result === true) {
          this.loadingChange.emit(true);
          this.selectedCartQuantities[id] = 1;
          if (!this.selectedCartIds?.includes(id)) {
            this.selectedCartIds.push(id);
            this.selectedCartIds = [...this.selectedCartIds];
          }
          this.updateItemQuantity(instance, 1);
        }
        this.loadingChange.emit(false);
      });
    }
    else {
      this.loadingChange.emit(true);
      this.selectedCartQuantities[instance?.record_id] = 1;
      if (!this.selectedCartIds?.includes(id)) {
        this.selectedCartIds.push(id);
        this.selectedCartIds = [...this.selectedCartIds];
      }
      this.updateItemQuantity(instance, 1);
    }
  }

  updateItemQuantity(element: any, itemQuantity: number) {
    let obj = {
      "RESOURCE": [{
        "RESOURCE_TYPE": "external",
        "RESOURCE_ID": element?.record_id,
        "QUANTITY": itemQuantity,
        "SERVICE_DETAILS": element
      }]
    }
    this.cartService.addItemToCart(obj).subscribe({
      next: (resp) => {
        if (resp.status === 200 && Array.isArray(resp.object) && resp.object.length > 0) {
          this.selectedCartQuantities[element?.record_id] = resp.object[0]?.QUANTITY;
          this.cartService.cartUpdated$.next();
          this.cartService.notifyLoginSuccess();
          // Refresh current page after cart update
          // this.costAnalysis.refreshCurrentPage();
        }
        this.loadingChange.emit(false);
      },
      error: (e) => {
        this.loadingChange.emit(false);
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        );
      }
    });
  }

  increaseQuantity(instanceId: string | number, element: any) {
    this.loadingChange.emit(true);
    const id = String(instanceId);
    if (this.selectedCartQuantities[id] != null) {
      let newQuantity = this.selectedCartQuantities[id] + 1;
      this.updateItemQuantity(element, newQuantity);
    }
  }

  decreaseQuantity(instanceId: string | number, element: any) {
    this.loadingChange.emit(true);
    const id = String(instanceId);
    if (this.selectedCartQuantities[id] > 1) {
      let newQuantity = this.selectedCartQuantities[id] - 1;
      this.updateItemQuantity(element, newQuantity);
    } else {
      const cartItemId = this.resourceIdToCartId[id];
      if (cartItemId) {
        this.removeItemFromCart(cartItemId);
      }
    }
  }

  removeItemFromCart(instanceId: string | number) {
    let obj = {
      "ID": [instanceId]
    };
    this.cartService.removeCartItem(obj).subscribe({
      next: (resp) => {
        if (resp && resp.status === 200 && Array.isArray(resp.object)) {
          this.cartService.cartUpdated$.next();
          // Refresh current page after cart update
          // this.costAnalysis.refreshCurrentPage();
        }
        this.loadingChange.emit(false);
      },
      error: (e) => {
        this.loadingChange.emit(false);
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        );
      }
    });
  }

  formatText(input: string) {
    return input?.replace(/-/g, ' ')?.replace(/\b\w/g, char => char?.toUpperCase());
  }

  getMonthlyPrice(hourlyRate: number, provider: string): string {
    const monthlyHours = this.monthlyHours[!!provider ? provider.toLowerCase() : '']
    return (hourlyRate * monthlyHours)?.toFixed(2);
  }

  loadCompareInstances() {
    if (isPlatformBrowser(this.platformId)) {
      const compArray = window.localStorage.getItem('COMPARE_INSTANCES') || '[]';
      const arr = compArray ? JSON.parse(compArray) : [];
      this.compareInstances = Array.isArray(arr) ? [...arr] : [];
    }
    this.cdr.detectChanges();
  }

  addToCompare(instance: any) {
    this.loadCompareInstances();
    if (this.compareInstances.length >= 4) return;
    // Prevent duplicates by record_id
    if (!this.compareInstances.some(i => i.RECORD_ID === instance.record_id)) {
      this.compareInstances.push({
        "RECORD_ID": instance.record_id,
        "DOCUMENT": instance
      });
      if (isPlatformBrowser(this.platformId)) {
        window.localStorage.setItem('COMPARE_INSTANCES', JSON.stringify(this.compareInstances));
      }
      this.loadCompareInstances();
      // Notify parent to update panel
      if (this.costAnalysis && typeof this.costAnalysis.refreshComparePanel === 'function') {
        this.costAnalysis.refreshComparePanel();
      }
    }
  }

}