export const FILTER_HEADERS = {
  service_provider: "Service Provider",
  service_type: "Service Type",
  product_name: "Product Name",
  instance_family: "Instance Family",
  vcpu_cores: "VCPU",
  ram_gb: "Memory",
  cs_region: "Region",
  cs_city: "City",
  host_type: "Host Type",
  os_type: "OS Type",
  payment_plan: "Payment Plans",
  payment_option: "Payment Options",
  access_tier: "Access Tier",
  access_tier_option: "Access Tier Option",
  redundancy: "Redundancy",
  storage_option: "Storage Option",
  term: "Term",
  storage_type: "Storage Type"
};

export const PROVIDER_ICON_MAP: { [key: string]: string } = {
  'aws': 'aws-provider.png',
  'azure': 'azure-provider.png',
  'gcp': 'gcp-provider.png',
  'oracle': 'oracle-provider.png',
  'digital ocean': 'digitalocean-provider.png'
};

export const PROVIDER_NAME_MAP: { [key: string]: string } = {
  'aws': 'Amazon Web Service',
  'azure': 'Microsoft Azure',
  'gcp': 'Google Cloud Platform',
  'oracle': 'Oracle',
  'digital ocean': 'Digital Ocean'
};