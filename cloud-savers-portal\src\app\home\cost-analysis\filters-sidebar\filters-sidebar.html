<form [formGroup]="filterForm" class="filters-sidebar" *ngFor="let provider of pricingData || []">
    <!-- <div class="filter-section">
        <div class="filter-head">Price Range ($/month)</div>
        <div class="price-range-container">
            <div class="price-inputs">
                <input type="number" placeholder="Min" formControlName="minPrice" [min]="0.01" class="price-input"
                    (blur)="validatePriceInputs()">
                <span class="separator">-</span>
                <input type="number" placeholder="Max" formControlName="maxPrice" class="price-input"
                    (blur)="validatePriceInputs()">
            </div>
        </div>
        <button mat-button class="apply-price-btn" (click)="applyPriceFilter()"
            [disabled]="!filterForm.get('minPrice')?.value || !filterForm.get('maxPrice')?.value">Apply</button>
    </div> -->
    <ng-container *ngFor="let instance of provider?.facet_counts || []">
        <div class="filter-section" *ngIf="instance?.counts.length > 0">
            <div class="search-container" *ngIf="isSearchableField(instance?.field_name)">
                <div class="search-input-wrapper">
                    <mat-icon class="search-icon">search</mat-icon>
                    <input type="text" class="search-input"
                        [placeholder]="getSearchPlaceholder(instance?.field_name)"
                        [value]="searchInputs[instance?.field_name] || ''"
                        (input)="onSearchInput(instance?.field_name, $event)">
                    <mat-icon class="clear-icon" *ngIf="searchInputs[instance?.field_name]"
                        (click)="clearSearch(instance?.field_name)">close</mat-icon>
                </div>
            </div>
            <div class="filter-head">{{filterHeaders[instance?.field_name] || formatTitle(instance?.field_name)}}</div>
            <div class="filter-body">
                <mat-checkbox *ngFor="let filter of getFilteredCounts(instance?.field_name, instance?.counts) || []"
                    [checked]="isFilterSelected(instance?.field_name, filter?.value)"
                    (change)="changeFilter($event, instance?.field_name, filter?.value)">
                    <span class="filter-option">
                        {{formatText(filter?.value, instance?.field_name)}}
                    </span>
                </mat-checkbox>
            </div>
        </div>
    </ng-container>
    <!-- <div class="filter-section" *ngIf="instanceFamilies.length > 0">
        <div class="filter-head">Instance Family</div>
        <div class="filter-body">
            <mat-checkbox *ngFor="let type of instanceFamilies;" [checked]="requiredInstances.includes(type)"
                (change)="selectInstanceTypes($event, type)">
                <span class="filter-option">
                    {{type}}
                </span>
            </mat-checkbox>
        </div>
    </div>
    <div class="filter-section">
        <div class="filter-head">
            Resource Specs
        </div>
        <div class="quantity-selector">
            <div class="qty-group">
                <div class="qty-label">vCPUs</div>
                <mat-form-field appearance="outline" class="filter-select">
                    <mat-select formControlName="vCPU" multiple (selectionChange)="selectvCPU($event.value)">
                        <mat-option *ngFor="let cpu of vCPUList" [value]="cpu">
                            {{cpu}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div class="qty-group">
                <div class="qty-label">RAM</div>
                <mat-form-field appearance="outline" class="filter-select">
                    <mat-select formControlName="RAM" multiple (selectionChange)="selectRAM($event.value)">
                        <mat-option *ngFor="let ram of RAMList" [value]="ram">
                            {{ram}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
        </div>
    </div>
    <div class="filter-section">
        <div class="filter-head">Region</div>
        <mat-form-field appearance="outline" class="filter-select">
            <mat-select formControlName="selectedRegion" panelWidth="350px" multiple
                (selectionChange)="selectRegions($event.value)">
                <mat-select-trigger>{{getSelectedRegionNames()}}</mat-select-trigger>
                <mat-option>
                    <ngx-mat-select-search placeholderLabel="Search" [formControl]="regionCode"
                        noEntriesFoundLabel="No Matching Found">
                        <mat-icon ngxMatSelectSearchClear class="close-icon">close</mat-icon>
                    </ngx-mat-select-search>
                </mat-option>
                <mat-optgroup *ngFor="let region of filteredRegions | keyvalue" [label]="region?.key">
                    <mat-option *ngFor="let option of region?.value" [value]="option?.CS_CLOUD_SAVER_CODE">
                        <div class="region-option">
                            <div class="r-name">{{option?.CS_CITY}}</div>
                            <div class="r-code">{{option?.CS_CLOUD_SAVER_CODE}}</div>
                        </div>
                    </mat-option>
                </mat-optgroup>
            </mat-select>
        </mat-form-field>
    </div>
    <div class="filter-section">
        <div class="filter-head">Operating System</div>
        <mat-form-field appearance="outline" class="filter-select">
            <mat-select formControlName="selectedOS" multiple (selectionChange)="selectOperatingSystems($event.value)">
                <mat-optgroup *ngFor="let os of operatingSystems | keyvalue" [label]="os?.key">
                    <mat-option *ngFor="let option of os?.value" [value]="option">
                        <div class="region-option">
                            <div class="os-name">{{option}}</div>
                        </div>
                    </mat-option>
                </mat-optgroup>
            </mat-select>
        </mat-form-field>
    </div>
    <div class="filter-section" *ngIf="hostTypes.length > 0">
        <div class="filter-head">Host Type</div>
        <div class="filter-body">
            <mat-checkbox *ngFor="let type of hostTypes;" [checked]="requiredHostTypes.includes(type)"
                (change)="selectHostTypes($event, type)">
                <span class="filter-option">
                    {{type}}
                </span>
            </mat-checkbox>
        </div>
    </div> -->
    <div class="action-section">
        <button mat-button (click)="emitFilters()" class="apply-btn" [disabled]="selectedFilters.length === 0">Apply</button>
        <button mat-button (click)="clearFilters()" class="clear-btn">Clear</button>
    </div>
</form>