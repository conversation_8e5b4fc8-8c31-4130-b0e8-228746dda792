.header {
    background-color: #0a2342;
    color: #FFFFFF;
    height: auto;
    display: flex;
    flex-direction: column;
    gap: 20px;
    justify-content: space-between;
    padding: 20px 50px;
    box-sizing: border-box;
    font-weight: 600;
    font-size: 14px;

    .logo-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    .nav-left {
        display: flex;
        gap: 15px;
        align-items: center;
        cursor: pointer;

        .btn-portal {
            display: flex;
            align-items: center;
            background-color: #35A5DB;
            color: #FEFEFE;
            font-weight: 600;
            gap: 6px;
            padding: 10px 15px;
            border-radius: 8px;

            .btn-txt {
                display: flex;
                align-items: center;
                gap: 8px;
            }
        }

        .btn-dashboard {
            border-radius: 8px;
            padding: 10px 15px;
            font-weight: 600;
            color: #212121;
            background: #EBF6FB;
        }
    }

    .nav-right {
        display: flex;
        align-items: center;
        gap: 8px;

        button {
            color: white;
        }

        .username {
            margin-right: 5px;
            font-size: 16px;
            cursor: pointer;
        }

        .badge {
            position: absolute;
            top: 0;
            left: 60%;
            font-size: 11px;
            height: 20px;
            width: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: #ff0000;
            color: #FFFFFF;
        }
    }
}

.option-container {
    width: 180px !important;
}

.menu-btn {
    min-height: 40px !important;
}

.menu-icon {
    margin-bottom: -4px;
    margin-right: 5px;
    font-size: 20px;
    color: #9CA3AF;
}

.menu-item {
    font-size: 16px;
    color: #374151;
}

@media screen and (max-width: 1250px) {

    .header {
        padding: 20px;

        .nav-left {
            gap: 5px;
        }

        .nav-right {
            gap: 1px;
        }
    }

    .nav-left-mobile {
        display: flex;
        gap: 15px;
        align-items: center;
        flex-wrap: wrap;
        justify-content: center;

        .btn-portal-mobile {
            display: flex;
            align-items: center;
            background-color: #35A5DB;
            color: #FEFEFE;
            font-weight: 600;
            gap: 6px;
            padding: 10px 15px;
            border-radius: 8px;

            .btn-txt-mobile {
                display: flex;
                align-items: center;
                gap: 8px;
            }
        }

        .btn-dashboard {
            border-radius: 8px;
            padding: 10px 15px;
            font-weight: 600;
            color: #212121;
            background: #EBF6FB;
        }
    }
}