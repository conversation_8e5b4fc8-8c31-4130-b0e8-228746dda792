<div class="custom-overlay" *ngIf="loading">
    <div class="custom-overlay__inner">
        <div class="custom-loader">
            <svg class="progress-circle" viewBox="0 0 50 50">
                <circle class="progress-bg" cx="25" cy="25" r="20" />
                <circle class="progress-bar" cx="25" cy="25" r="20" />
            </svg>
            <div class="loader-title">Loading data</div>
            <div class="loader-desc">Please wait...</div>
        </div>
    </div>
</div>
<div>
    <app-header></app-header>
    <!-- <div class="container">
        <app-search-panel></app-search-panel>
    </div> -->
    <div class="head-row" id="blogContent">
        <div (click)="backToHome()" class="back-arrow">
            <iconify-icon icon="material-symbols:arrow-back-rounded"></iconify-icon>Back
        </div>
        <div class="pricing-head">Blogs</div>
    </div>

    <!-- Side Panel Overlay -->
    <div class="side-panel-overlay" [class.active]="isSidePanelOpen" (click)="closeSidePanel()"></div>

    <!-- Mobile Side Panel -->
    <div class="mobile-side-panel" [class.active]="isSidePanelOpen && mobile">
        <div class="mobile-sidebar-content">
            <button class="close-sidebar" (click)="closeSidePanel()">
                <iconify-icon icon="material-symbols:close"></iconify-icon>
            </button>
            <h3 class="sidebar-title">Categories</h3>
            <ul class="category-list">
                <li *ngFor="let category of categories || []" class="category-item">
                    <div class="category-link">
                        <span class="category-name">{{ category.name }}</span>
                        <span class="category-count">{{ category.count }}</span>
                    </div>
                </li>
            </ul>
            <!-- <h3 class="sidebar-title">Popular Tags</h3>
            <div class="tags-container">
                <span *ngFor="let tag of popularTags || []" class="tag">{{ tag }}</span>
            </div> -->
        </div>
    </div>

    <div class="blog-container">
        <aside class="sidebar-left" *ngIf="!mobile">
            <h3 class="sidebar-title">Categories</h3>
            <ul class="category-list">
                <li *ngFor="let category of categories || []" class="category-item">
                    <div class="category-link">
                        <span class="category-name">{{ category.name }}</span>
                        <span class="category-count">{{ category.count }}</span>
                    </div>
                </li>
            </ul>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="content-header">
                <h1 class="page-title">Blogs</h1>
                <button class="menu-button" (click)="toggleSidePanel()" *ngIf="mobile">
                    <iconify-icon icon="mdi:filter" width="24" height="24"></iconify-icon>
                </button>
            </div>

            <!-- Blog Posts -->
            <div class="blog-posts">
                <div *ngIf="blogPosts && blogPosts.length > 0" class="blog-grid">
                    <div class="search-container" *ngIf="mobile">
                        <input type="text" placeholder="Search articles..." class="search-input"
                            [(ngModel)]="searchTerm" (input)="onSearch()">
                        <iconify-icon icon="mynaui:search" width="24" height="24" class="search-icon"></iconify-icon>
                    </div>

                    <div class="blog-posts">
                        <article *ngFor="let post of blogPosts || []" class="blog-card" (click)="blogDetails(post)">
                            <div class="blog-image">
                                <img [src]="post?.content?.banner?.filename || 'assets/images/placeholder-blog.jpg'"
                                    [alt]="post?.content?.banner?.alt"
                                    (error)="post.content.banner.filename = 'assets/images/placeholder-blog.jpg'">
                                <span class="blog-category" [style.background]="post?.content?.category_color">
                                    {{ post?.content?.category }}
                                </span>
                            </div>

                            <div class="blog-content">
                                <h2 class="blog-title">{{ post?.content?.heading }}</h2>
                                <p class="blog-description">{{ post?.content?.short_description }}</p>

                                <div class="blog-footer">
                                    <div class="author-info">
                                        <img [src]="post?.content?.author_image?.filename || 'assets/images/author-img.jpg'"
                                            [alt]="post?.content?.author_image?.alt" class="author-avatar"
                                            (error)="post.content.author_image.filename = 'assets/images/author-img.jpg'">
                                        <div class="author-details">
                                            <span class="author-name">{{ post?.content?.author_name }}</span>
                                            <div class="post-meta">
                                                <span class="post-date">{{ post?.content?.post_date | date:'MMM dd,
                                                    yyyy' }} • {{ post?.content?.read_time }} min read</span>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="read-more">
                                        Read More
                                    </button>
                                </div>
                            </div>
                        </article>
                    </div>
                </div>
            </div>
        </main>

        <!-- Right Sidebar - Popular Tags -->
        <aside class="sidebar-right" *ngIf="!mobile">
            <div class="search-content">
                <h3 class="sidebar-title">Search</h3>
                <div class="search-container">
                    <input type="text" placeholder="Search articles..." class="search-input" [(ngModel)]="searchTerm"
                        (input)="onSearch()">
                    <iconify-icon icon="mynaui:search" width="24" height="24" class="search-icon"></iconify-icon>
                </div>
            </div>
            <!-- <div class="search-content">
                <h3 class="sidebar-title">Popular Tags</h3>
                <div class="tags-container">
                    <span *ngFor="let tag of popularTags || []" class="tag">{{ tag }}</span>
                </div>
            </div> -->
        </aside>
    </div>
    <app-footer *ngIf="!mobile"></app-footer>
    <app-mobile-footer *ngIf="mobile"></app-mobile-footer>
</div>