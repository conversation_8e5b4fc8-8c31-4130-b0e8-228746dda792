import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Header } from '../header/header';
import { Footer } from '../footer/footer';

@Component({
  selector: 'app-page-not-found',
  imports: [CommonModule, Header, Footer],
  templateUrl: './page-not-found.html',
  styleUrl: './page-not-found.scss'
})
export class PageNotFound {
  loading: boolean = false;

  constructor(
    private router: Router
  ) { }

  backToHome() {
    this.loading = true;
    setTimeout(() => {
      this.router.navigate(['/']).then(() => {
        this.loading = false;
      });
    }, 200);
  }
}