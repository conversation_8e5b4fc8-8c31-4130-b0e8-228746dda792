import { CommonModule, isPlatformBrowser } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, HostListener, Inject, PLATFORM_ID } from '@angular/core';
import { MaterialModule } from '../../material-module';
import { Header } from '../header/header';
import { Router } from '@angular/router';
import { Footer } from '../footer/footer';
import { MobileFooter } from '../mobile-footer/mobile-footer';

@Component({
  selector: 'app-terms-services',
  standalone: true,
  imports: [CommonModule, MaterialModule, Header, Footer, MobileFooter],
  templateUrl: './terms-services.html',
  styleUrls: ['./terms-services.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class TermsServices {
  mobile: boolean;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenWidth();
  }

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private router: Router
  ) { }

  ngOnInit() {
    this.checkScreenWidth();
    this.scrollToTop();
  }

  private scrollToTop() {
    // Scroll to top after a small delay to ensure content is loaded
    setTimeout(() => {
      if (isPlatformBrowser(this.platformId)) {
        window.scrollTo(0, 0);
        document.documentElement.scrollTop = 0;
        document.body.scrollTop = 0;
      }
    }, 50);
  }

  private checkScreenWidth() {
    if (isPlatformBrowser(this.platformId)) {
      if (window.innerWidth <= 1250) {
        this.mobile = true;
      } else {
        this.mobile = false;
      }
    }
  }

  // scrollToSection(sectionId: string): void {
  //   const element = document.getElementById(sectionId);
  //   if (element) {
  //     // Scroll to element without changing URL
  //     element.scrollIntoView({
  //       behavior: 'smooth',
  //       block: 'start',
  //       inline: 'nearest'
  //     });
  //   }
  // }

  backToHome() {
    this.router.navigate(['/']).then(() => {
    });
  }
}