<div class="custom-overlay" *ngIf="loading">
    <div class="custom-overlay__inner">
        <div class="custom-loader">
            <svg class="progress-circle" viewBox="0 0 50 50">
                <circle class="progress-bg" cx="25" cy="25" r="20" />
                <circle class="progress-bar" cx="25" cy="25" r="20" />
            </svg>
            <div class="loader-title">Getting best deals for you</div>
            <div class="loader-desc">Hold on while we extract the best pricing from multiple service providers...</div>
        </div>
    </div>
</div>
<div>
    <app-header></app-header>
    <div class="container">
        <app-search-panel (scrollToPricing)="scrollToPricingContent()"></app-search-panel>
    </div>
    <div class="head-row" id="pricingContent">
        <div (click)="backToHome()" class="back-arrow">
            <iconify-icon icon="material-symbols:arrow-back-rounded"></iconify-icon>Back
        </div>
        <div class="pricing-head">Cloud Comparison</div>
    </div>
    <!-- <div class="products-tab">
        <div class="tab" *ngFor="let product of productsSelected; let i = index"
            [class.selected]="i === selectedTabIndex" [class.disabled]="i === selectedTabIndex"
            (click)="i !== selectedTabIndex && onTabSelect(i)">
            <div>{{product}}</div>
        </div>
    </div> -->
    <div class="pricing-content">
        <div class="filter-overlay" [class.visible]="showFilterSidebar" (click)="toggleFilterSidebar()"></div>
        <!-- <div class="cart-overlay" [class.visible]="showCartSidebar" (click)="toggleCartSidebar()"></div> -->
        <div class="main-content" [class.filter-open]="showFilterSidebar" [class.cart-open]="showCartSidebar">
            <div class="filter-wrapper" *ngIf="!mobile">
                <app-filters-sidebar (filtersChanged)="onFiltersChanged($event)"
                    (loadingChange)="onLoadingChange($event)">
                </app-filters-sidebar>
            </div>
            <div class="filter-wrapper" [class.mobile-visible]="showFilterSidebar" *ngIf="mobile">
                <div class="filter-header">
                    <h3>Filters</h3>
                    <button mat-icon-button (click)="closeFilterSidebar()" aria-label="Close filters">
                        <mat-icon>close</mat-icon>
                    </button>
                </div>
                <div class="filter-content">
                    <app-filters-sidebar (filtersChanged)="onFiltersChanged($event)"
                        (loadingChange)="onLoadingChange($event)">
                    </app-filters-sidebar>
                </div>
            </div>
            <div class="results-wrapper">
                <app-cost-compare-results (loadingChange)="onLoadingChange($event)"></app-cost-compare-results>
            </div>
            <!-- <div class="cart-wrapper" *ngIf="!mobile">
                <app-cart (loadingChange)="onCartLoadingChange($event)"></app-cart>
                <app-recommendations></app-recommendations>
            </div> -->
        </div>
    </div>
    <div class="bottom-cart-panel" *ngIf="compareInstances && compareInstances.length > 0 && !mobile">
        <div class="cart-items-list">
            <div class="cart-item" *ngFor="let item of compareInstances || []">
                <img class="provider-logo"
                    [src]="'assets/images/' + providerIconMap[item?.DOCUMENT?.service_provider | lowercase]">
                <div class="cart-item-details">
                    <div class="cart-item-title">{{item?.DOCUMENT?.product_name}}</div>
                    <div class="cart-item-type">{{item?.DOCUMENT?.vm_name}}</div>
                </div>
                <button mat-icon-button class="remove-btn" (click)="removeFromCompare(item)">
                    <mat-icon>close</mat-icon>
                </button>
            </div>
        </div>
        <button mat-raised-button color="primary" class="go-to-comparison-btn" [disabled]="compareInstances.length < 2"
            (click)="goToComparison()">
            Go to Comparison
        </button>
    </div>
    <app-mobile-footer *ngIf="mobile"></app-mobile-footer>
</div>