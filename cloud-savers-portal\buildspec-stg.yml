version: 0.2

env:
    variables:
        S3_BUCKET: aws-cloud-savers-fe-portal-build-stg
        CLOUDFRONT: E1KIUTT3YMLLWS
phases:
  install:
    runtime-versions:
        nodejs:22
    commands:
    - echo $CODEBUILD_SRC_DIR
    - npm install -y npm@latest
    - npm install -g @angular/cli
    - rm package-lock.json
  pre_build:
    commands:
    - cd cloud-savers-portal
    - npm install
  build:
    commands:
    - echo build started on `date`
    - export NODE_OPTIONS=--max-old-space-size=8192
    - node --max_old_space_size=4000 ./node_modules/@angular/cli/bin/ng build --aot --output-hashing=none --configuration=stg
    #- ng build --configuration=stg
    - ls -l -F
  post_build:
    commands:
    - aws s3 rm s3://${S3_BUCKET} --recursive
    - echo S3 bucket is cleared.
    - aws s3 cp dist/cloud-savers-portal/browser s3://${S3_BUCKET} --recursive
    - aws cloudfront create-invalidation --distribution-id=${CLOUDFRONT} --paths '/*'
    - echo Build completed on `date`
# artifacts:
#     files:
#       - dist/**/*
#     base-directory: cloud-savers-portal