import { APP_BASE_HREF } from '@angular/common';
import { CommonEngine } from '@angular/ssr/node';
import express from 'express';
import { fileURLToPath } from 'node:url';
import { dirname, join, resolve } from 'node:path';
import bootstrap from './src/main.server';
import { existsSync, readFileSync } from 'fs';
import { routes } from './src/app/app.routes';
import StoryblokClient from 'storyblok-js-client';
import offersData from './src/assets/shared/internal-offers/internal-offers.json';

const storyblok = new StoryblokClient({
  accessToken: '************************',
});

// The Express app is exported so that it can be used by serverless Functions.
export function app(): express.Express {
  const server = express();
  const serverDistFolder = dirname(fileURLToPath(import.meta.url));
  const browserDistFolder = resolve(serverDistFolder, '../browser');
  const indexHtml = join(serverDistFolder, 'index.server.html');

  const commonEngine = new CommonEngine();

  server.set('view engine', 'html');
  server.set('views', browserDistFolder);

  // Example Express Rest API endpoints
  // server.get('/api/**', (req, res) => { });
  // Serve static files from /browser
  server.use(express.static(browserDistFolder, {
    maxAge: '1y',
    index: false // don’t serve index.html here, SSR should handle it
    // index: 'index.html',
  }));

  // before the catch-all SSR route
  server.get('/healthz', (req, res) => {
    res.status(200).send('OK');
  });

  // // Serve sitemap.xml
  // server.get('/sitemap.xml', (req, res) => {
  //   const sitemap = readFileSync(join(browserDistFolder, 'sitemap.xml'), 'utf8');
  //   res.header('Content-Type', 'application/xml');
  //   res.send(sitemap);
  // });

  server.get('/sitemap.xml', async (req, res) => {
    const baseUrl = `https://cloudatler.com`;

    // Static routes
    const staticRoutes = extractRoutes(routes);

    // Dynamic routes
    const blogRoutes = await getBlogRoutes();
    const offerRoutes = await getOfferRoutes(baseUrl);
    const dynamicRoutes = [];

    const allRoutes = [...new Set([...staticRoutes.filter(r => !r.includes(':')), ...offerRoutes, ...blogRoutes, ...dynamicRoutes])];

    const urls = allRoutes
      .map(route => `
      <url>
        <loc>${baseUrl}${route}</loc>
        <lastmod>${new Date().toISOString()}</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
      </url>
      `).join('');

    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
    <urlset
      xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
            http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
      ${urls}
    </urlset>`;

    res.header('Content-Type', 'application/xml');
    res.send(sitemap);
  });

  // Serve robots.txt
  server.get('/robots.txt', (req, res) => {
    const robots = readFileSync(join(browserDistFolder, 'robots.txt'), 'utf8');
    res.header('Content-Type', 'text/plain');
    res.send(robots);
  });

  // All regular routes use the Angular engine
  server.get(/.*/, (req, res, next) => {
    const { protocol, originalUrl, baseUrl, headers } = req;

    commonEngine
      .render({
        bootstrap,
        documentFilePath: indexHtml,
        url: `${protocol}://${headers.host}${originalUrl}`,
        publicPath: browserDistFolder,
        providers: [{ provide: APP_BASE_HREF, useValue: baseUrl }],
      })
      .then((html) => {
        // Check if the rendered HTML is your 404 page
        if (html.includes('404 - Page Not Found')) {
          res.status(404).send(html); // return Angular’s SSR 404 page with correct status
        } else {
          res.send(html); // normal flow
        }
      })
      .catch((err) => next(err));
  });

  return server;
}


function getOfferRoutes(baseUrl) {
  const offerPaths: string[] = [];
  let fullPath = '';
  for (let offer in offersData) {
    for (let od_i = 0; od_i < offersData[offer].length; od_i++) {
      fullPath = '/offers/' + (offersData[offer][od_i]['slug'] || '');
      offerPaths.push(fullPath.replace(/\/+/g, '/')); // clean slashes
    }
  }
  return offerPaths;
}

async function getBlogRoutes(): Promise<string[]> {
  const perPage = 100;
  let page = 1;
  let allStories: any[] = [];

  while (true) {
    const { data } = await storyblok.get('cdn/stories', {
      starts_with: 'blogs/',
      version: 'published',
      per_page: perPage,
      page,
    });

    allStories = [...allStories, ...data.stories];
    if (data.stories.length < perPage) {
      break;
    }
    page++;
  }

  return allStories.map((s: any) => '/' + s.full_slug);
}

// Utility to flatten routes into a list of paths
function extractRoutes(routes: any[], parentPath = ''): string[] {
  const paths: string[] = [];
  for (const route of routes) {
    const fullPath = parentPath + '/' + (route.path || '');
    paths.push(fullPath.replace(/\/+/g, '/')); // clean slashes

    if (route.children) {
      paths.push(...extractRoutes(route.children, fullPath));
    }
  }
  return paths;
}

function run(): void {
  const port = process.env['PORT'];

  // Start up the Node server
  const server = app();
  server.listen(port, () => {
    console.log(`Node Express server listening on http://localhost:${port}`);
  });
}

run();