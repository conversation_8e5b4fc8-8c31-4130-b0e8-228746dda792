// .privacy-policy {
//     display: flex;
//     flex-direction: column;
// }

// .policy-head {
//     padding: 30px;
//     display: flex;
//     justify-content: space-between;
//     align-items: center;
// }

// .title {
//     font-size: 20px;
//     font-weight: 700;
//     color: #111827;
// }

// .close-icon {
//     border-radius: 50%;
//     background: #f3f3f3;
//     color: #373737 !important;
//     padding: 6px;
//     height: 30px;
//     width: 30px;
// }

// .policy-content {
//     display: flex;
//     flex-direction: column;
//     gap: 10px;
//     padding: 30px;
//     font-size: 16px;
//     line-height: 24px;
//     color: #000000;
//     overflow-y: scroll;
//     overflow-x: hidden;
//     -ms-overflow-style: none;
//     scrollbar-width: none;
//     height: auto;
//     max-height: 350px;

//     .policy-title {
//         font-weight: 700;
//     }
// }

// .policy-content ::-webkit-scrollbar {
//     display: none;
// }

.back-arrow-div {
    display: flex;
    align-items: baseline;
    padding: 20px 50px;
}

.back-arrow {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 16px;
    color: #374151;
    cursor: pointer;
    text-align: start;
    width: 10%;
}

.terms-container {
    padding: 0 20px;
    background-color: #FFFFFF;
}

.terms-header {
    text-align: center;
    padding: 40px 0;
    margin-bottom: 40px;
}

.container-header {
    width: 90%;
}

.terms-title {
    font-size: 34px;
    font-weight: 600;
    margin: 0 0 12px 0;
    color: #111827;
}

.terms-subtitle {
    font-size: 20px;
    color: #4B5563;
    line-height: 28px;
    margin: 0 0 16px 0;
    font-weight: 400;
}

.last-updated {
    font-size: 14px;
    color: #888;
    font-weight: 400;
}

.terms-content {
    padding: 20px 18%;
}

.table-of-contents {
    flex: 0 0 300px;
    background-color: #FFFFFF;
    padding: 24px;
    border-radius: 8px;
    // position: sticky;
    top: 20px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    margin: 0 10px;
}

.table-of-contents h2 {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 20px 0;
    color: #1a1a1a;
}

.toc-columns {
    display: flex;
    padding: 0px 20px;
    gap: 55px;
}

.toc-header {
    display: flex;
    align-items: center;
    gap: 8px;
}

.toc-icon {
    color: #007bff;
    margin-bottom: 20px;
}

.toc-list {
    padding: 0;
    margin: 0;
    color: #007bff;

    :hover {
        color: #0056b3;
    }
}

.toc-list li {
    margin-bottom: 8px;
    cursor: pointer;
}

.toc-list a {
    color: #007bff;
    text-decoration: none;
    font-size: 14px;
    line-height: 1.4;
    display: block;
    padding: 4px 0;
    transition: color 0.2s ease;
}

.toc-list a:hover {
    text-decoration: underline;
}

// .terms-main {
//     flex: 1;
//     min-width: 0;
//     background: #FFFFFF;
//     padding: 50px 50px;
//     margin: 30px 10px;
//     border-radius: 8px;
//     box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
// }

.terms-section {
    margin-bottom: 40px;
    padding-bottom: 32px;
    border-bottom: 1px solid #f0f0f0;
    padding-top: 80px;
    margin-top: -60px;

    ul {
        margin: 12px 0;
        padding-left: 20px;

        li {
            font-size: 14px;
            color: #4B5563;
            margin-bottom: 8px;
            line-height: 1.5;
        }
    }
}

.terms-section:last-child {
    border-bottom: none;
}

.terms-section h2 {
    font-size: 24px;
    font-weight: 700;
    margin: 0 0 16px 0;
    color: #111827;
    display: flex;
    align-items: center;
    gap: 8px;
    line-height: 32px;
}

.section-number {
    font-weight: 700;
}

.terms-text {
    background: #EFF6FF;
    padding: 16px;
    border-radius: 16px;

}

.terms-section p {
    font-size: 16px;
    color: #374151;
    margin: 0 0 16px 0;
    line-height: 1.6;
    font-weight: 400;
}

.requirement-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
    padding: 12px 16px;
    background-color: #F9FAFB;
    border-radius: 16px;
    // border-left: 3px solid #007bff;
}

.requirement-item.sub-item {
    background-color: #EFF6FF;
}

.requirement-item.accurate {
    background-color: #FEFCE8;
}

.requirement-icon {
    color: #007bff;
    margin-right: 12px;
}

.requirement-warning-icon {
    color: #EF4444;
    margin-right: 12px;
}

.requirement-item.warning {
    background-color: #FEF2F2;
    // border-left-color: #ffc107;
}

.bullet {
    color: #007bff;
    margin-right: 12px;
    font-weight: 600;
    flex-shrink: 0;
    margin-top: 2px;
}

.requirement-item.warning .bullet {
    color: #ffc107;
}

.requirement-item span:last-child {
    font-size: 16px;
    color: #374151;
    line-height: 1.5;
}

.contact-details {
    font-weight: 700 !important;
}

.email-content {
    color: #111827 !important;
}

.email-link {
    color: #111827;
    text-decoration: none;
}

.email-link:hover {
    text-decoration: underline;
}


@media only screen and (max-width: 1250px) {

    .terms-main {
        padding: 20px;
        margin: 0px;
        box-shadow: none;
        order: 1;
    }

    .terms-content {
        padding: 0 20px;
        background: #FFFFFF;
    }

    .container-header {
        width: 100%;
    }

    .terms-header {
        padding: 20px 0;
        margin-bottom: 0;
    }

    .terms-container {
        padding: 0 16px;
    }

    .terms-title {
        font-size: 24px;
    }

    .table-of-contents {
        flex: none;
        position: static;
        order: 2;
    }

    .toc-columns {
        flex-direction: row;
        gap: 24px;
    }

    .toc-column {
        flex: 1;
    }

    .terms-section h2 {
        align-items: flex-start;
    }

    .email-link {
        color: #007bff;
        font-weight: 500;
    }

    .back-arrow-div {
        padding: 20px;
        gap: 20px;
    }

}