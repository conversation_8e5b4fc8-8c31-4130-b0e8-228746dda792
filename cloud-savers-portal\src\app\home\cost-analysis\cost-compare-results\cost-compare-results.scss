:host ::ng-deep .mat-mdc-form-field-flex>.mat-mdc-form-field-infix {
    padding: 5px 0px 0.4em 0px !important;
    min-height: 0px !important;
}

::ng-deep .mat-mdc-form-field-subscript-wrapper {
    box-sizing: border-box;
    width: 100%;
    position: relative !important;
}

:host ::ng-deep .mat-mdc-select-value-text {
    font-size: 15px !important;
}

.results {
    width: 100%;
    background: transparent;
    display: flex;
    flex-direction: column;
    gap: 45px;

    .results-header {
        background: white;
        border-radius: 8px;
        box-shadow: 0 3px 10px rgb(0 0 0 / 0.07);
        padding: 20px;
        display: flex;
        justify-content: start;
        align-items: center;
        gap: 20px;

        .price-filter {
            font-size: 16px;
            color: #374151;
        }

        .filter-select {
            width: 25%;
            height: 35px;
            border-radius: 8px;
            border: none;
            outline: none;
        }

        // .result-title {
        //     display: flex;
        //     flex-direction: column;
        //     gap: 5px;
        //     font-size: 14px;
        //     color: #6B7280;
        //     line-height: 20px;

        //     .title {
        //         font-size: 18px;
        //         font-weight: 600;
        //         color: #000000;
        //         line-height: 28px;
        //     }
        // }

        // .result-sort {
        //     display: flex;
        //     flex-direction: row;
        //     align-items: center;
        //     gap: 10px;
        //     font-size: 14px;
        //     color: #4B5563;

        //     .icon-button {
        //         display: flex;
        //         align-items: center;
        //         gap: 5px;
        //         cursor: pointer;
        //     }
        // }
    }

    .calculate-savings {
        background-color: #EBF6FB;
        border: 1px solid #C0E3F4;
        border-radius: 8px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap: 15px;

        .saving-head {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 10px;

            .saving-title {
                display: flex;
                align-items: center;
                gap: 10px;

                .saving-text {
                    display: flex;
                    flex-direction: column;
                    gap: 5px;

                    .title {
                        font-size: 18px;
                        font-weight: 500;
                        color: #133A4D;
                        line-height: 28px;
                    }
                }
            }

            .calculate-btn {
                background-color: #35A5DB;
                color: #FFFFFF;
                font-size: 14px;
                padding: 10px 20px;
                min-width: auto;
                height: auto;
                border-radius: 8px;
            }
        }

        .saving-desc {
            font-size: 14px;
            color: #133A4D;
            line-height: 20px;
        }
    }

    .no-results-message {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        font-size: 14px;
        color: #133A4D;
        line-height: 20px;
    }

    .instance-wrapper {
        display: flex;
        flex-direction: column;
        gap: 30px;
    }

    .desktop-view {
        display: flex;
        flex-direction: column;
        gap: 25px;
    }

    .instance-cards {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 25px;

        .instance-card-container {
            display: flex;
            flex-direction: column;
            flex: 1 1 calc(50% - 15px); // 2 cards per row, minus gap
            max-width: calc(50% - 10px);
            border-radius: 16px;

            &:hover {
                transform: scale(1.02);
            }
        }

        .instance-card {
            display: flex;
            flex-direction: column;
            border-radius: 16px;
            border: 2px solid #FFFFFF;
            overflow: hidden;
            background-color: #FFFFFF;
            gap: 15px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            z-index: 2;
        }

        .instance-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 20px 0 20px;

            .provider-type {
                display: flex;
                align-items: center;
                gap: 15px;

                .provider-logo {
                    width: 40px;
                    height: 40px;
                }

                .provider-details {
                    display: flex;
                    flex-direction: column;
                    gap: 5px;

                    .provider-name {
                        font-size: 14px;
                        font-weight: 500;
                        color: #1F2937;

                        span {
                            font-size: 13px;
                        }
                    }

                    .instance-details {
                        font-size: 12px;
                        font-weight: 400;
                        color: #6B7280;
                    }
                }
            }

            .instance-family {
                font-size: 11px;
                color: #047857;
                background-color: #D1FAE5;
                padding: 5px 10px;
                border-radius: 20px;
                text-align: center;
            }

            .instance-price {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                gap: 7px;

                .price {
                    display: flex;
                    align-items: center;
                    gap: 3px;
                    font-size: 15px;
                    font-weight: 700;
                    color: #1F2937;
                }

                .price-period-icon {
                    font-size: 14px;
                    color: #6B7280;
                }

                .price-period {
                    font-size: 12px;
                    font-weight: 400;
                    color: #6B7280;
                }
            }
        }

        .instance-specs {
            display: flex;
            padding: 0px 20px;
            justify-content: space-between;
            align-items: start;

            .spec-item {
                display: flex;
                flex-direction: column;
                gap: 10px;
                max-width: 25%;
            }

            .price-item {
                display: flex;
                flex-direction: column;
                gap: 7px;
            }

            .spec-label {
                font-size: 12px;
                color: #6B7280;
            }

            .spec-value {
                font-size: 12px;
                font-weight: 500;
                color: #1F2937;
            }
        }

        .instance-card-price {
            flex-direction: row;
            align-items: center;
            padding: 30px 20px 10px 20px;
            top: -20px;
            position: relative;
            z-index: 1;
        }

        .payment-option {
            font-size: 11px;
            color: #1F2937;
            background-color: #dcdcdc;
            padding: 5px 10px;
            border-radius: 20px;
            text-align: center;
        }

        .instance-action {
            display: flex;
            justify-content: space-between;
            gap: 5px;
            align-items: center;
            padding: 0 20px 20px 20px;

            .location {
                display: flex;
                gap: 10px;
                width: 50%;

                .location-div {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-size: 12px;
                    color: #6B7280;
                }
            }

            .action-buttons {
                display: flex;
                justify-content: end;
                align-items: center;
                width: 45%;
            }

            .compare-btn {
                background-color: #FFFFFF;
                color: #35A5DB;
                font-size: 13px;
                height: 27px;
                padding: 7px;
                border-radius: 8px;
                border: 1px solid #35A5DB;

                &:disabled {
                    opacity: 0.5;
                }
            }

            .action-icons {
                color: #35A5DB;

                &:disabled {
                    opacity: 0.5;
                }
            }

            .quantity-selector {
                display: flex;
                justify-content: center;

                .quantity-controls {
                    display: flex;
                    align-items: center;
                    border: 1px solid #E5E7EB;
                    border-radius: 8px;
                    overflow: hidden;
                    width: auto;
                    height: 30px;

                    .qty-btn {
                        width: 30px;
                        height: 30px;
                        background: #F9FAFB;
                        border: none;
                        font-size: 16px;
                        cursor: pointer;
                        padding: 0;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #6B7280;

                        &:hover {
                            background: #F3F4F6;
                        }

                        &:active {
                            background: #E5E7EB;
                        }
                    }

                    .qty-input {
                        width: 34px;
                        height: 30px;
                        border: none;
                        text-align: center;
                        font-size: 14px;
                        outline: none;
                        padding: 0;
                        background: white;
                    }
                }
            }
        }
    }

    .record-range {
        font-size: 14px;
        font-weight: 500;
        color: #4B5563;
    }

    .pagination {
        display: flex;
        // justify-content: space-between;
        justify-content: end;
        align-items: center;
        gap: 20px;
        flex-wrap: wrap;

        .page-info {
            font-size: 12px;
            color: #6B7280;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 10px;

            button {
                width: 40px;
                height: 40px;
                border-radius: 12px;
                border: 1px solid #D1D5DB;
                background: #FFFFFF;
                color: #6B7280;
                display: flex;
                align-items: center;
                justify-content: center;

                &:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }

                iconify-icon {
                    font-size: 20px;
                }
            }

            .current-page {
                font-size: 15px;
                color: #4B5563;
                font-weight: 500;
                width: 40px;
                height: 40px;
                border-radius: 12px;
                border: 1px solid #35A5DB;
                background: #FFFFFF;
                color: #35A5DB;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    }
}

@media only screen and (max-width: 1250px) {

    .filter-select {
        width: 50% !important;
    }

    .instance-cards {
        .instance-card-container {
            flex: 1 1 100% !important;
            max-width: 100% !important;
        }
    }

    .instance-header {
        flex-wrap: wrap;
        gap: 20px;
        justify-content: center;
        align-items: center;
    }

    .instance-specs {
        flex-wrap: wrap;
        gap: 20px;

        .spec-item {
            max-width: 100% !important;
            flex-direction: row;
            gap: 25px;
            justify-content: space-between;
        }
    }

    .instance-action {
        flex-wrap: wrap;
        gap: 20px;

        .location {
            width: fit-content !important;
            flex-wrap: wrap;
        }

        .action-buttons {
            width: fit-content !important;
        }
    }
}