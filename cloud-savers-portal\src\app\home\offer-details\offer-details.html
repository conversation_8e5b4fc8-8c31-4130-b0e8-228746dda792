<div class="custom-overlay" *ngIf="loading">
    <div class="custom-overlay__inner">
        <div class="custom-loader">
            <svg class="progress-circle" viewBox="0 0 50 50">
                <circle class="progress-bg" cx="25" cy="25" r="20" />
                <circle class="progress-bar" cx="25" cy="25" r="20" />
            </svg>
            <div class="loader-title">Loading data</div>
            <div class="loader-desc">Please wait...</div>
        </div>
    </div>
</div>
<div id="pageContent">
    <app-header></app-header>
    <div class="head-row">
        <div (click)="backToHome()" class="back-arrow">
            <iconify-icon icon="material-symbols:arrow-back-rounded"></iconify-icon>Back
        </div>
        <div class="pricing-head">Offer View</div>
    </div>
    <div class="detail-container" *ngIf="selectedOffer">
        <div class="provider-name">{{getProviderWithLowestPrice() | uppercase}}</div>
        <div class="offer-title">{{selectedOffer.title}} – {{selectedOffer.title_details}}</div>
        <div class="instance-details">
            <li *ngFor="let detail of selectedOffer.details || []">{{detail}}</li>
            <div class="instance-price">${{getLowestPriceForDefaultRegion()}}/mo</div>
        </div>
        <div class="btn-row">
            <button class="cart-btn" (click)="addToCart(selectedOffer)">Add to Cart</button>
            <button class="compare-btn">Contact Sales</button>
            <div>
                <mat-form-field appearance="outline" class="region-select">
                    <mat-select [formControl]="selectedRegion" (selectionChange)="onRegionChange($event.value)">
                        <mat-option *ngFor="let region of selectedOffer.region || []"
                            [value]="region.name">{{region.name}}</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
        </div>
    </div>
    <div class="price-container" *ngIf="selectedOffer">
        <div class="price-head">Regional Price Comparison</div>
        <div class="price-desc">Compare {{selectedOffer.title}} pricing with major cloud providers</div>
        <div class="price-table">
            <div class="table-header">
                <div class="region-div">Region</div>
                <div class="price-div">AWS Price</div>
                <div class="price-div">Azure Price</div>
            </div>
            <mat-divider *ngIf="!mobile"></mat-divider>
            <div class="table-content" *ngFor="let region of selectedOffer.region || []; let i = index">
                <div class="table-row">
                    <div class="region region-div" *ngIf="!mobile">
                        <img src="assets/images/region-icon.png">
                        <div class="region-details">
                            <div>{{region.name}}</div>
                            <div class="region-type">{{region.type}}</div>
                        </div>
                    </div>
                    <div class="region region-div" data-label="Region" *ngIf="mobile">
                        <div class="region-details">
                            <div class="region-name">
                                <span><img src="assets/images/region-icon.png" class="region-icon">
                                    {{region.name}}
                                </span>
                            </div>
                            <div class="region-type">{{region.type}}</div>
                        </div>
                    </div>
                    <div class="price-div" data-label="AWS Price">${{region.aws}}/mo</div>
                    <div class="price-div" data-label="Azure Price">${{region.azure}}/mo</div>
                </div>
                <mat-divider *ngIf="i < selectedOffer.region.length - 1"></mat-divider>
            </div>
        </div>
    </div>
    <app-footer *ngIf="!mobile"></app-footer>
    <app-mobile-footer *ngIf="mobile"></app-mobile-footer>
</div>