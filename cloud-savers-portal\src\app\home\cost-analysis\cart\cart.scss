.right-top-section {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 25px;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.section {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.cart {
    background: white;
    border-radius: 8px;
    padding: 10px;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .header-row {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-bottom: 16px;

        .cart-title {
            color: #000000;
            font-size: 20px;
            font-weight: 600;
            margin: 0;
        }

        .items-no {
            color: #6B7280;
            font-size: 14px;
            font-weight: 400;

        }
    }

    .cart-content {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .cart-items {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .cart-item {
        display: flex;
        gap: 16px;
        padding: 16px;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        position: relative;
    }

    .provider-image {
        width: 40px;
        height: 40px;
        object-fit: contain;
        border-radius: 4px;
    }

    .provider-icon {
        width: 100%;
    }

    .item-details {
        flex: 1;
        min-width: 0;
        margin-right: auto;
    }

    .remove-icon {
        position: absolute;
        top: 18px;
        right: 0;
        cursor: pointer;
        color: #9CA3AF;
        font-size: 14px;
    }

    .item-controls-row {
        display: flex;
        align-items: center;
        gap: 24px;
        margin-top: 12px;

        .item-qty-row {
            display: flex;
            align-items: center;

            .qty-btn-minus {
                min-width: 36px;
                height: 36px;
                border-top-left-radius: 6px;
                border-bottom-left-radius: 6px;
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
                font-size: 18px;
                font-weight: 500;
                background: #F9FAFB;
                color: #374151;
                border: 1px solid #D1D5DB;
                box-shadow: none;
                transition: border-color 0.2s;

                &:hover {
                    border-color: #35A5DB;
                }
            }

            .qty-btn-plus {
                min-width: 36px;
                height: 36px;
                border-top-right-radius: 6px;
                border-bottom-right-radius: 6px;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
                font-size: 18px;
                font-weight: 500;
                background: #F9FAFB;
                color: #374151;
                border: 1px solid #D1D5DB;
                box-shadow: none;
                transition: border-color 0.2s;

                &:hover {
                    border-color: #35A5DB;
                }
            }

            .item-qty {
                font-size: 15px;
                min-width: 48px;
                text-align: center;
                background: #fff;
                border: 1px solid #E5E7EB;
                height: 36px;
                line-height: 36px;
                display: inline-block;
                border-left: none;
                border-right: none;
            }
        }

    }

    .item-header {
        margin-bottom: 4px;
    }

    .item-name {
        font-size: 15px;
        font-weight: 500;
        color: #111827;
        margin: 0;
        display: -webkit-box;
        // -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .item-provider {
        font-size: 13px;
        color: #6b7280;
        margin-bottom: 8px;
    }

    .item-tags-row {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        margin-bottom: 12px;

        .tag {
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            padding: 4px 8px;
            border-radius: 20px;
            background-color: #EFF6FF;
            color: #1D4ED8;
            text-transform: capitalize;
        }
    }

    .item-content {
        flex: 1;
    }

    .item-price-details {
        margin-top: 12px;
        // border-top: 1px solid #e5e7eb;
        padding-top: 12px;
        display: flex;
        flex-direction: column;
        gap: 8px;

        .price-item {
            font-size: 13px;
            color: #6b7280;

            span {
                font-weight: 600;
                color: #1f2937;
            }
        }
    }

    .item-activation-row {
        display: flex;
        justify-content: end;
        margin-top: 12px;
    }
}

.cart-summary {
    display: flex;
    flex-direction: column;
    gap: 15px;

    .item {
        display: flex;
        justify-content: space-between;

        .price-label {
            color: #000000;
            font-size: 16px;
            font-weight: 500;
            line-height: 24px;
        }

        .price-value {
            font-size: 18px;
            font-weight: 700;
            line-height: 28px;
            color: #000000;
        }
    }
}


.buy-btn {
    width: 100%;
    background-color: #1F2937;
    color: #FFFFFF;
    font-size: 16px;
    font-weight: 400;
    border-radius: 8px;
    padding: 7px 15px;
    height: auto;

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
}



.view-details {
    color: #35A5DB;
    font-size: 12px;
    font-weight: 400;
    cursor: pointer;
    text-align: center;
}

.offers {
    background: #EFF6FF;
    border-radius: 8px;
    padding: 20px;
    width: 100%;

    .header-row {
        display: flex;
        justify-content: start;
        gap: 10px;
        align-items: center;

        .cart-title {
            color: #000000;
            font-size: 16px;
            font-weight: 500;
        }
    }

    .cart-items {
        display: flex;
        flex-direction: column;
        gap: 15px;

        .item {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            gap: 15px;
            background-color: #FFFFFF;
            border-radius: 8px;
            padding: 15px;

            .item-head {
                display: flex;
                justify-content: start;
                align-items: start;
                gap: 7px;

                .offer-details {
                    display: flex;
                    flex-direction: column;
                    gap: 5px;

                    .offer-name {
                        font-size: 14px;
                        font-weight: 500;
                        color: #000000;
                    }

                    .offer-type {
                        font-size: 12px;
                        font-weight: 400;
                        color: #6B7280;
                    }
                }
            }
        }
    }
}

.deals {
    background: #9233EA0F;
    border-radius: 8px;
    padding: 20px;
    width: 100%;

    .header-row {
        display: flex;
        justify-content: space-between;
        gap: 10px;
        align-items: center;

        .deal-head {
            display: flex;
            flex-direction: row;
            gap: 8px;
            align-items: center;

            .cart-title {
                color: #000000;
                font-size: 16px;
                font-weight: 500;
            }
        }

        .timer {
            color: #9233EA;
            font-size: 14px;
            font-weight: 400;
        }
    }

    .cart-items {
        display: flex;
        flex-direction: column;
        gap: 15px;

        .item {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            gap: 15px;
            background-color: #FFFFFF;
            border-radius: 8px;
            padding: 15px;

            .offer-head {
                font-size: 14px;
                font-weight: 500;
                color: #000000;
            }

            .offer-details {
                font-size: 12px;
                font-weight: 400;
                color: #4B5563;
            }

            .claim-btn {
                width: 100%;
                background-color: #9233EA;
                color: #FFFFFF;
                font-size: 16px;
                font-weight: 400;
                border-radius: 8px;
                padding: 7px 15px;
                height: auto;
            }
        }
    }
}


@media only screen and (max-width: 1250px) {
    .right-top-section {
        height: unset;
    }

}