import { Component, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { Router, RouterModule, RouterOutlet, NavigationEnd, ActivatedRoute, Data } from '@angular/router';
import { filter, map, mergeMap, tap } from 'rxjs/operators';
import { environment } from '../environments/environment';
import { MetaService } from './services/meta.service';
import { MaterialModule } from './material-module';

declare let dataLayer: any;
declare global {
  interface Window {
    dataLayer: any[];
  }
}

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    RouterOutlet,
    MaterialModule
  ],
  templateUrl: './app.html',
  styleUrls: ['./app.scss']
})

export class App {
  title = 'cloud-atler-portal';
  public websiteUrl = `${environment.PORTAL_URL}`;

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private metaService: MetaService,
  ) {
    if (isPlatformBrowser(this.platformId)) {
      this.router.events.subscribe(event => {
        if (event instanceof NavigationEnd) {
          window.dataLayer = window.dataLayer || [];
          window.dataLayer.push({
            event: 'pageview',
            pagePath: event.urlAfterRedirects,
            pageTitle: document.title
          });
        }
      });
    }
  }

  ngOnInit() {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        map(() => this.activatedRoute),
        map((route) => {
          while (route.firstChild) {
            route = route.firstChild;
          }
          return route;
        }),
        filter((route) => route.outlet === 'primary'),
        mergeMap((route) => route.data),
        tap(({ title, description, ogTitle, ogDescription, canonicalUrl, hreflangs }: Data) => {
          this.metaService.updateTitle(title);
          this.metaService.updateDescription(description);
          this.metaService.updateOgTitle(ogTitle);
          this.metaService.updateOgDescription(ogDescription);
          const url = canonicalUrl || this.router.url;
          this.metaService.updateCanonicalUrl(this.websiteUrl, url);
          if (!!hreflangs && Array.isArray(hreflangs)) {
            this.metaService.updateHreflangTags(hreflangs.map(({ lang, path }) => ({
              hreflang: lang,
              href: path
            })), this.websiteUrl);
          }
        })

      )
      .subscribe();
  }
}