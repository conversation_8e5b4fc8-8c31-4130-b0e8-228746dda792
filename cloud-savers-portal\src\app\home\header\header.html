<header class="header">
    <div class="logo-container">
        <div class="nav-left" (click)="goToHome()">
            <img src="/assets/icons/LogoImage.png">
            <img src="/assets/icons/Logo.png">
        </div>
        <div class="nav-right">
            <button mat-icon-button aria-label="Cart" *ngIf="loggedIn && !mobile" (click)="viewCart()">
                <mat-icon>shopping_cart</mat-icon>
                <span class="badge">{{cartCount}}</span>
            </button>
            <!-- <button mat-icon-button aria-label="Language" *ngIf="!mobile">
                <mat-icon>language</mat-icon>
            </button>
            <button mat-icon-button aria-label="Favorites">
                <mat-icon>favorite</mat-icon>
            </button> -->
            <button mat-icon-button aria-label="User Profile">
                <mat-icon>person</mat-icon>
            </button>
            <span *ngIf="!loggedIn" class="username" (click)="login()">Login</span>
            <span *ngIf="loggedIn" class="username">{{userInfo.firstName}} {{userInfo.lastName}}</span>
            <button mat-icon-button [matMenuTriggerFor]="menu" *ngIf="loggedIn">
                <mat-icon>menu</mat-icon>
            </button>
            <mat-menu #menu="matMenu" xPosition="before">
                <div class="option-container">
                    <button mat-menu-item class="menu-btn" (click)="orderHistory()">
                        <iconify-icon icon="material-symbols:history" class="menu-icon"></iconify-icon>
                        <span class="menu-item">Order History</span>
                    </button>
                    <mat-divider></mat-divider>
                    <button mat-menu-item class="menu-btn" (click)="logout()">
                        <iconify-icon icon="material-symbols:logout" class="menu-icon"></iconify-icon>
                        <span class="menu-item">Logout</span>
                    </button>
                </div>
            </mat-menu>
        </div>
    </div>
    <div class="nav-left" *ngIf="!mobile">
        <button mat-button class="btn-portal" (click)="goToHome()">
            <div class="btn-txt">
                <img src="assets/images/cloud-symbol.png" alt="Cloud" />
                <span>Cloud Comparison Portal</span>
            </div>
        </button>
        <button mat-button class="btn-dashboard" (click)="getAdminPortal()">Dashboard</button>
        <button mat-button class="btn-dashboard" (click)="getSpecialOffers()">Special Offers</button>
        <button mat-button class="btn-dashboard" (click)="getBlogs()">Blog</button>
    </div>
    <div class="nav-left-mobile" *ngIf="mobile">
        <button mat-button class="btn-portal-mobile" (click)="goToHome()">
            <div class="btn-txt-mobile">
                <img src="assets/images/cloud-symbol.png" alt="Cloud" />
                <span>Cloud Comparison Portal</span>
            </div>
        </button>
        <button mat-button class="btn-dashboard" (click)="getAdminPortal()">Dashboard</button>
        <button mat-button class="btn-dashboard" (click)="getSpecialOffers()">Special Offers</button>
        <button mat-button class="btn-dashboard" (click)="getBlogs()">Blog</button>
    </div>
</header>