:host {
    ::ng-deep {
        .search-panel-field {
            .mat-mdc-form-field-subscript-wrapper {
                box-sizing: border-box;
                width: 100%;
                position: relative !important;
            }

            label.ng-star-inserted {
                transform: translateY(-0.59375em) scale(.75) !important;
            }

            .mat-mdc-floating-label {
                top: 25px !important;
            }

            .mat-mdc-form-field-flex>.mat-mdc-form-field-infix {
                padding: 15px 0px 0.4em 0px !important;
                min-height: 55px !important;
            }
        }
    }
}

.search-panel {
    padding: 50px;
    display: flex;
    flex-direction: column;
    gap: 25px;
    justify-content: space-between;
    align-items: center;

    .search-head {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 25px;
        // flex-direction: row;
        // justify-content: space-between;
        align-items: center;
        width: 100%;

        .search-title {
            font-weight: 700;
            font-size: 32px;
            color: #111827;
            line-height: 40px;
            text-align: center;
        }

        .search-desc {
            font-size: 18px;
            color: #4B5563;
            line-height: 28px;
            text-align: center;
        }

        .btn-connect {
            background: #2C2C2C;
            border-radius: 8px;
            color: #FFFFFF;
            font-weight: 400;
            font-size: 14px;
            padding: 10px 15px;
            border: none;
            display: flex;
            align-items: end;
            gap: 5px;
        }
    }

    .search-box-container {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        width: 85%;
    }

    .search-box {
        border: 1px solid #E5E7EB;
        border-right: none;
        background-color: #FFFFFF;
        height: 50px;
        border-radius: 16px 0px 0px 16px;
        padding-left: 10px;
        box-shadow: 0 3px 8px rgb(0 0 0 / 0.11);
        outline: none;
        width: 100%;
    }

    .search-box::placeholder {
        text-shadow: 0 3px 8px rgb(0 0 0 / 0.11);
        font-weight: 500;
        font-size: 15px;
        color: #9CA3AF;
    }

    .filter-btn {
        display: flex;
        gap: 5px;
        align-items: center;
        justify-content: center;
        background: #FFFFFF;
        color: #133A4D;
        font-size: 15px;
        border-top: 1px solid #E5E7EB;
        border-bottom: 1px solid #E5E7EB;
        height: 50px;
        width: 200px;
        cursor: pointer;

        iconify-icon {
            font-size: 20px;
        }
    }

    .search-btn {
        background: #133A4D;
        height: 48px;
        width: 50px;
        border-radius: 0px 16px 16px 0px;

        mat-icon {
            color: white;
            font-size: 22px;
        }
    }

    .show-filter {
        font-size: 25px;
        color: #133A4D;
    }

    .suggestions {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .suggestions-title {
            font-size: 14px;
            color: #133A4D;
            font-weight: 500;
        }

        mat-chip-list {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            gap: 10px;
        }

        mat-chip {
            background-color: #1897C2;
            color: #ffffff;
            font-size: 13px;
            border-radius: 20px;
            padding: 10px;
            cursor: pointer;
            font-weight: 500;
        }
    }

    .params-card {
        background: white;
        border-radius: 8px;
        padding: 20px 25px;
        box-shadow: 0 3px 8px rgb(0 0 0 / 0.11);
        display: flex;
        flex-direction: column;
        gap: 20px;

        .header-row {
            display: flex;
            justify-content: start;
            align-items: center;
            color: #374151;
            font-size: 16px;
            gap: 10px;

            mat-icon {
                font-size: 16px;
                margin-right: 5px;
                padding-top: 8px;
            }

            label {
                font-weight: 500;
            }
        }

        .array-box {
            display: flex;
            flex-direction: column;
            gap: 15px;

            .params-form {
                display: flex;
                flex-direction: column;
                gap: 25px;
                border: 1px solid #E5E7EB;
                padding: 20px;
                border-radius: 8px;

                .params-row {
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    align-items: start;
                    gap: 15px;
                }

                .product-row {
                    display: flex;
                    flex-direction: row;
                    flex-wrap: wrap;
                    gap: 25px;
                    align-items: start;
                }

                .form-field {
                    width: 200px;
                    height: 55px;

                    &.service-field {
                        width: 170px;
                    }

                    &.number-field {
                        width: 117px;
                    }
                }

                .actions-row {
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    align-items: center;
                }

                .remove-btn {
                    color: #2C2C2C;
                }

                .more-filters {
                    color: #35A5DB;
                    cursor: pointer;
                    font-size: 14px;
                    text-decoration: underline;
                }
            }
        }

        .params-actions {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: center;

            div {
                display: flex;
                flex-direction: row;
                width: 44%;
                align-items: start;
            }

            .btn-add-param {
                border: 1px solid #000000;
                border-radius: 8px;
                color: #2C2C2C;
                font-size: 14px;
                padding: 10px;
                min-width: auto;
            }
        }
    }

    .btn-compare {
        border-radius: 8px;
        background-color: #2C2C2C;
        color: #FFFFFF;
        font-size: 14px;
        padding: 10px 50px;
        align-items: end;
    }

    .btn-compare:disabled {
        opacity: 0.5;
    }
}

@media only screen and (max-width: 1250px) {
    .search-panel {
        padding: 25px 20px !important;

        .suggestions {
            width: 100%;
            align-items: center;

            mat-chip-list {
                align-items: center;
                justify-content: center;
            }
        }
    }
}