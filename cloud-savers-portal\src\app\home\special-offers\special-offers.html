<div id="pageContent">
    <app-header></app-header>
    <div class="head-row">
        <div (click)="backToHome()" class="back-arrow">
            <iconify-icon icon="material-symbols:arrow-back-rounded"></iconify-icon>Back
        </div>
    </div>
    <div class="detail-content">
        <section class="featured-section">
            <div class="featured-header">
                <h1 class="featured-head">All Cloud Offers</h1>
                <h2 class="featured-desc">Discover exclusive deals and promotions from leading cloud providers</h2>
            </div>
        </section>
        <section class="premium-section">
            <div class="premium-header">
                <div class="premium-head">
                    <img src="assets/images/premium-offers.png">
                    <div>Featured Premium Offers</div>
                </div>
                <div class="featured-desc">Find the perfect cloud solution with exclusive deals from leading providers
                </div>
            </div>
            <div class="premium-cards">
                <div class="premium-card" *ngFor="let offer of featuredOffers || []">
                    <div class="card-header">
                        <div class="provider-head">
                            <div class="provider-name">{{offer.title}}</div>
                            <div class="provider-desc">{{offer.title_details}}</div>
                        </div>
                        <!-- <div class="discount">Save 65%</div> -->
                    </div>
                    <div class="service-offers premium-offers">
                        <div class="premium-desc">{{offer.description}}</div>
                        <div class="features-list">
                            <div class="feature-item" *ngFor="let detail of offer.details || []">
                                <span class="content">✓</span>
                                <span class="plan-features">{{detail}}</span>
                            </div>
                        </div>
                    </div>
                    <div class="pricing">
                        <div class="price">${{getLowestPriceForDefaultRegion(offer)}}/mo</div>
                        <!-- <div class="premium-timer">
                            <div class="expiry">Expires in 3 days</div>
                            <div class="slots">128 left</div>
                        </div> -->
                    </div>
                    <button class="view-btn detail-btn" (click)="viewOfferDetails(offer)">View Details</button>
                </div>
            </div>
        </section>
        <section class="featured-offers">
            <div class="all-offer-head">
                All Cloud Offers
            </div>
            <div class="offer-cards">
                <div class="offer-card" *ngFor="let offer of internalOffers || []">
                    <div class="provider-logo">
                        <img src="assets/images/service-offer.png">
                        <div class="provider-name">{{offer.title}}</div>
                    </div>
                    <div class="service-offers">
                        <div>{{offer.description}}</div>
                        <div class="features-list">
                            <div class="feature-item" *ngFor="let detail of offer.details || []">
                                <span class="content">✓</span>
                                <span class="plan-features">{{detail}}</span>
                            </div>
                        </div>
                    </div>
                    <div class="pricing">
                        <div class="pricing-container">
                            <div class="price-time">Starting at</div>
                            <div class="price">${{getLowestPriceForDefaultRegion(offer)}}/mo</div>
                        </div>
                        <button class="view-btn" (click)="viewOfferDetails(offer)">View Offer</button>
                    </div>
                </div>
            </div>
        </section>
    </div>
    <app-footer *ngIf="!mobile"></app-footer>
    <app-mobile-footer *ngIf="mobile"></app-mobile-footer>
</div>