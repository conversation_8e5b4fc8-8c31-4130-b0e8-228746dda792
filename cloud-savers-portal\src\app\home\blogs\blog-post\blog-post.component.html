<div>
    <app-header></app-header>
    <div class="head-row" id="pricingContent">
        <div (click)="backToBlog()" class="back-arrow">
            <iconify-icon icon="material-symbols:arrow-back-rounded"></iconify-icon>Back to Blog Page
        </div>
        <div class="pricing-head">Blog</div>
    </div>

    <div class="article-container" *ngIf="blogPost; else loading">
        <header class="article-header">
            <h1 class="article-title">Article: {{ blogPost?.content?.heading }}</h1>
            <div class="author-info"
                *ngIf="blogPost?.content?.author_name || (blogPost?.content?.post_date && blogPost?.content?.read_time)">
                <div class="author-avatar">
                    <img [src]="blogPost?.content?.author_image?.filename || 'assets/images/author-img.jpg'"
                        alt="blogPost?.content?.author_image?.alt" class="author-img"
                        (error)="blogPost.content.author_image.filename = 'assets/images/author-img.jpg'" />
                </div>
                <div class="author-details">
                    <span class="author-name" *ngIf="blogPost?.content?.author_name">
                        {{ blogPost?.content?.author_name }}
                    </span>
                    <span *ngIf="blogPost?.content?.post_date && blogPost?.content?.read_time" class="company-name">
                        {{ blogPost?.content?.post_date | date:'MMM dd, yyyy' }} • {{ blogPost?.content?.read_time }}
                        min read
                    </span>
                </div>
            </div>
        </header>

        <section class="hero-section" *ngIf="blogPost?.content?.banner?.filename">
            <div class="hero-content">
                <div class="hero-illustration" *ngIf="blogPost?.content?.banner?.filename">
                    <img [src]="blogPost?.content?.banner?.filename" [alt]="blogPost?.content?.banner?.alt"
                        class="hero-image">
                </div>
            </div>
        </section>

        <main class="main-content" *ngIf="blogPost?.content?.body?.content.length != 0">
            <!-- <section *ngFor="let section of blogPost?.content?.body?.content || []">
                <h2 class="section-title" *ngIf="section.type === 'heading'">
                    <ng-container *ngFor="let paragraph of section?.content || []">
                        <p *ngIf="paragraph.type === 'text'">{{paragraph.text}}</p>
                    </ng-container>
                </h2>
                <div class="section-content" *ngIf="section.type === 'paragraph'">
                    <ng-container *ngFor="let paragraph of section?.content || []">
                        <p *ngIf="paragraph.type === 'text'" [innerHTML]="paragraph.text"></p>
                    </ng-container>
                </div>
            </section> -->
            <div class="storyblok-content" id="sb_body_content"></div>

            <div class="cta-box">
                <div class="cta-content">
                    <div class="cta-text">
                        <h3>Ready to Simplify Your Cloud Journey?</h3>
                        <p>Get instant access to our Cloud Atler Portal and compare top providers in minutes, not weeks.
                        </p>
                    </div>
                    <div>
                        <button (click)="navigateToContact()" class="cta-button">Try Cloud Atler</button>
                    </div>
                </div>
            </div>
        </main>

        <!-- <main class="main-content" *ngIf="blogPost.sections?.length">
            <ng-container *ngFor="let section of blogPost.sections || []">
                <section class="content-section" *ngIf="section.content?.length || section.benefits?.length">
                    <h2 *ngIf="!!section.title && section.title?.trim() !== ''" class="section-title">{{ section.title
                        }}
                    </h2>
                    <div class="section-content">
                        <p *ngFor="let paragraph of section.content || []" [innerHTML]="paragraph">
                        </p>

                        <div *ngIf="section.benefits?.length" class="benefits-container">
                            <ul class="benefits-list">
                                <li *ngFor="let benefit of section.benefits || []">
                                    <span class="benefit-text" [innerHTML]="benefit"></span>
                                </li>
                            </ul>
                        </div>

                        <p *ngFor="let paragraph of section.lastcontent || []" [innerHTML]="paragraph">
                        </p>

                        <div *ngIf="section.providers?.length" class="providers-grid">
                            <div>
                                <div *ngFor="let provider of section.providers || []" class="provider-item">
                                    <h4 *ngIf="provider.title" class="provider-title">{{ provider.title }}</h4>
                                    <p *ngIf="provider.description" class="provider-description"
                                        [innerHTML]="provider.description"></p>

                                    <div *ngIf="provider.benefits?.length" class="provider-benefits">
                                        <ul class="benefits-list">
                                            <li *ngFor="let benefit of provider.benefits || []">
                                                <span class="benefit-text" [innerHTML]="benefit"></span>
                                            </li>
                                        </ul>
                                    </div>
                                    <p *ngFor="let paragraph of provider.lastcontent || []" [innerHTML]="paragraph">
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </ng-container>

            <section class="content-section" *ngIf="blogPost.conclusion">
                <ng-container *ngIf="isConclusionObject(blogPost.conclusion)">
                    <div class="section-content">
                        <h2 *ngIf="blogPost.conclusion.title" class="provider-title">
                            {{ blogPost.conclusion.title }}
                        </h2>

                        <ng-container *ngIf="isArray(blogPost.conclusion.content)">
                            <p *ngFor="let para of blogPost.conclusion.content || []">{{ para }}</p>
                        </ng-container>
                        <p *ngIf="!isArray(blogPost.conclusion.content)">
                            {{ blogPost.conclusion.content }}
                        </p>

                        <div *ngIf="blogPost.conclusion.subsections?.length" class="subsections">
                            <div *ngFor="let sub of blogPost.conclusion.subsections || []" class="provider-item">
                                <h3 class="provider-title">{{ sub.title }}</h3>
                                <ng-container *ngIf="isArray(sub.content)">
                                    <p *ngFor="let para of sub.content || []">{{ para }}</p>
                                </ng-container>
                                <p *ngIf="!isArray(sub.content)">
                                    {{ sub.content }}
                                </p>
                            </div>
                        </div>
                    </div>
                </ng-container>
            </section>
        </main> -->
    </div>
</div>

<ng-template #loading>
    <div class="loading-container">
        <div class="spinner"></div>
        <p>Loading blog post...</p>
    </div>
</ng-template>

<app-footer *ngIf="!mobile"></app-footer>
<app-mobile-footer *ngIf="mobile"></app-mobile-footer>