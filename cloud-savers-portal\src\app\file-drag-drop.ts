import { Directive, EventEmitter, HostListener, Output } from '@angular/core';

@Directive({
  selector: '[appFileDragDrop]'
})
export class FileDragDrop {

  @Output() private filesChangeEmiter: EventEmitter<File[]> = new EventEmitter();

  constructor() { }

  // Dragover listener
  @HostListener('dragover', ['$event']) public onDragOver(evt: DragEvent) {
    evt.preventDefault();
    evt.stopPropagation();
  }

  // Dragleave listener
  @HostListener('dragleave', ['$event']) public onDragLeave(evt: DragEvent) {
    evt.preventDefault();
    evt.stopPropagation();
  }

  // Drop listener
  @HostListener('drop', ['$event']) public onDrop(evt: DragEvent) {
    evt.preventDefault();
    evt.stopPropagation();
    if (!evt.dataTransfer) {
      return;
    }
    let files = evt.dataTransfer.files;
    let valid_files: Array<File> = Array.from(files);
    this.filesChangeEmiter.emit(valid_files);
  }

}