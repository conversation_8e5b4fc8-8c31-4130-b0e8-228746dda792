.head-row {
    display: flex;
    align-items: center;
    padding: 20px 50px;
    background: transparent;

    .back-arrow {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 16px;
        color: #374151;
        cursor: pointer;
        text-align: start;
        width: 5%;
    }

    .pricing-head {
        font-size: 20px;
        font-weight: 600;
        color: #000000;
        text-align: center;
        width: 80%;
    }

    .comparison-count {
        font-size: 14px;
        color: #4B5563;
        text-align: end;
        width: 15%;
    }
}

.comparison-table-container {
    overflow-x: auto;
    padding: 20px 50px;
    background: #F9FAFB;
}

.comparison-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: #FFFFFF;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border-radius: 16px;
    overflow: hidden;

    th,
    td {
        padding: 16px;
        text-align: center;
        border-bottom: 1px solid #F3F4F6;
        vertical-align: middle;
    }

    .spec-column {
        background: #FFFFFF;
        font-weight: 500;
        color: #111827;
        text-align: left;
        min-width: 180px;
        position: sticky;
        left: 0;
        z-index: 10;
    }

    .instance-column {
        background: #FFFFFF;
        min-width: 200px;
        position: relative;
    }

    .instance-header {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        position: relative;

        .remove-instance-btn {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 24px;
            height: 24px;
            color: #9CA3AF;
            border-radius: 50%;
            z-index: 5;

            iconify-icon {
                font-size: 16px;
                position: absolute;
                right: 4px;
                bottom: 4px;
            }
        }

        .instnace-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .instance-text {
            display: flex;
            flex-direction: column;
            gap: 3px;
        }

        .instance-selection {
            position: relative;

            .instance-radio {
                position: absolute;
                opacity: 0;
                cursor: pointer;

                &:checked+.radio-label .radio-custom {
                    background-color: #35A5DB;
                    border-color: #35A5DB;

                    &::after {
                        opacity: 1;
                        transform: scale(1);
                    }
                }
            }

            .radio-label {
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;

                .radio-custom {
                    width: 20px;
                    height: 20px;
                    border: 2px solid #D1D5DB;
                    border-radius: 50%;
                    background: white;
                    position: relative;
                    transition: all 0.2s ease;

                    &::after {
                        content: '';
                        position: absolute;
                        top: 20%;
                        left: 20%;
                        width: 10px;
                        height: 10px;
                        background: white;
                        border-radius: 50%;
                        transform: translate(-50%, -50%) scale(0);
                        opacity: 0;
                        transition: all 0.2s ease;
                    }

                    &:hover {
                        border-color: #2563EB;
                    }
                }
            }
        }

        .provider-logo {
            width: 32px;
            height: 32px;
            object-fit: contain;
        }

        .instance-title {
            font-weight: 500;
            font-size: 16px;
            color: #111827;
            text-align: center;
        }

        .instance-subtitle {
            font-weight: 600;
            font-size: 16px;
            color: #6B7280;
        }

        .instance-type {
            font-weight: 700;
            font-size: 14px;
            color: #6B7280;
        }

        .instance-price {
            font-size: 18px;
            color: #111827;
            font-weight: 700;
        }
    }

    .spec-group-header {
        background-color: #F9FAFB;

        .group-title {
            display: flex;
            align-items: center;
            font-weight: 500;
            color: #111827;
            text-align: left;
            gap: 10px;

            iconify-icon {
                font-size: 20px;
                color: #111827;
            }
        }
    }

    .spec-label {
        background: #FFFFFF;
        font-size: 14px;
        color: #374151;
        text-align: left;
        position: sticky;
        left: 0;
        z-index: 5;
    }

    .spec-value {
        font-size: 14px;
        color: #000000;

        &.highlight-best {
            background-color: #DCFCE7;
            color: #166534;
        }

        &.feature-value {
            text-align: center;

            iconify-icon {
                font-size: 20px;
            }
        }

        &.feature-available,
        .feature-available {
            color: #16A34A;
        }

        &.feature-unavailable,
        .feature-unavailable {
            color: #DC2626;
        }

        &.feature-unknown,
        .feature-unknown {
            color: #6B7280;
        }

        &.highlight-worst {
            background-color: #FEF2F2;
            color: #DC2626;
        }
    }

    .reserved-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 3px;
    }

    // tr:hover {
    //     .spec-value:not(.highlight) {
    //         background: #F9FAFB;
    //     }
    // }
}

// Sticky Footer Panel
.selection-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #E5E7EB;
    box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transform: translateY(100%);
    transition: transform 0.3s ease-in-out;

    &.footer-visible {
        transform: translateY(0);
    }

    .footer-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px 50px;
        width: 100%;
        margin: 0 auto;

        .footer-left {
            display: flex;
            align-items: center;
            gap: 25px;

            .selected-instance-info {
                display: flex;
                align-items: center;
                gap: 15px;

                .footer-provider-logo {
                    width: 32px;
                    height: 32px;
                    object-fit: contain;
                }

                .instance-details {
                    display: flex;
                    flex-direction: column;
                    gap: 5px;

                    .instance-name {
                        font-weight: 500;
                        font-size: 16px;
                        color: #111827;
                    }

                    .instance-specs {
                        font-size: 14px;
                        color: #6B7280;
                    }
                }
            }

            .pricing-info {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;

                .monthly-cost {
                    display: flex;
                    gap: 10px;
                    align-items: center;
                }

                .cost-label {
                    font-size: 14px;
                    color: #4B5563;
                    font-weight: 500;
                }

                .cost-value {
                    font-size: 18px;
                    font-weight: 700;
                    color: #111827;
                }

                .best-value {
                    font-size: 14px;
                    font-weight: 500;
                    color: #0066CC;
                }
            }
        }

        .footer-actions {
            display: flex;
            align-items: center;
            gap: 15px;

            .footer-btn {
                font-size: 14px;
                padding: 10px 20px;
                border-radius: 8px;

                &.secondary {
                    background-color: #FFFFFF;
                    color: #374151;
                    border: 1px solid #E5E7EB;
                }

                &.primary {
                    background-color: #35A5DB;
                    color: #FFFFFF;
                }
            }

            .close-footer-btn {
                color: #9CA3AF;
            }
        }
    }
}

.footer-spacer {
    height: 80px;
}