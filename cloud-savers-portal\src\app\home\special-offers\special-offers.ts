import { CommonModule, isPlatformBrowser } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, HostListener, Inject, PLATFORM_ID } from '@angular/core';
import { MaterialModule } from '../../material-module';
import { Header } from '../header/header';
import { Footer } from '../footer/footer';
import { MobileFooter } from '../mobile-footer/mobile-footer';
import offersData from '../../../assets/shared/internal-offers/internal-offers.json';
import { Router } from '@angular/router';

@Component({
  selector: 'app-special-offers',
  imports: [
    CommonModule,
    MaterialModule,
    Header,
    Footer,
    MobileFooter
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './special-offers.html',
  styleUrl: './special-offers.scss'
})
export class SpecialOffers {
  featuredOffers = offersData.FEATURED_OFFERS;
  internalOffers = offersData.OFFERS;
  // featuredOffers = (offersData as any).FEATURED_OFFERS;
  // internalOffers = (offersData as any).OFFERS;
  mobile: boolean = false;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenWidth();
  }

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private router: Router
  ) { }

  ngOnInit() {
    this.checkScreenWidth();
    this.scrollToTopContent();
  }

  private checkScreenWidth() {
    if (isPlatformBrowser(this.platformId)) {
      if (window.innerWidth <= 1250) {
        this.mobile = true;
      } else {
        this.mobile = false;
      }
    }
  }

  slugify(text: string): string {
    return text
      ?.toString()
      ?.toLowerCase()
      ?.trim()
      ?.replace(/[\/\s]+/g, '-')
      ?.replace(/[^\w-]+/g, '')
      ?.replace(/-+/g, '-')
      ?.replace(/^-+|-+$/g, '');
  }

  viewOfferDetails(offer: any) {
    const titleSlug = this.slugify(offer.title);
    this.router.navigate(['/offers', titleSlug]);
  }

  backToHome() {
    this.router.navigate(['/']);
  }

  scrollToTopContent() {
    setTimeout(() => {
      if (isPlatformBrowser(this.platformId)) {
        const element = document.getElementById('pageContent');
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }
    }, 100);
  }

  getLowestPriceForDefaultRegion(offer: any): number {
    if (!offer?.region || !Array.isArray(offer.region)) {
      return 0;
    }
    const defaultRegion = offer.region.find(region => region.default === true);
    if (!defaultRegion) {
      return 0;
    }
    const awsPrice = defaultRegion.aws || 0;
    const azurePrice = defaultRegion.azure || 0;
    return Math.min(awsPrice, azurePrice);
  }

}