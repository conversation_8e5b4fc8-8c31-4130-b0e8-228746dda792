{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"cloud-savers-portal": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/cloud-savers-portal", "index": "src/html/dev/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss"], "server": "src/main.server.ts", "ssr": {"entry": "server.ts"}}, "configurations": {"dev": {"budgets": [{"type": "initial", "maximumWarning": "12mb", "maximumError": "16mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "200kb"}], "assets": ["src/assets/icons/favicon.ico", "src/assets"], "index": "src/html/dev/index.html", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "outputHashing": "all", "optimization": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "aot": true}, "qa": {"budgets": [{"type": "initial", "maximumWarning": "12mb", "maximumError": "16mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "200kb"}], "assets": ["src/assets/icons/favicon.ico", "src/assets"], "index": "src/html/qa/index.html", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.qa.ts"}], "outputHashing": "all", "optimization": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "aot": true}, "stg": {"budgets": [{"type": "initial", "maximumWarning": "12mb", "maximumError": "16mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "200kb"}], "assets": ["src/assets/icons/favicon.ico", "src/assets"], "index": "src/html/stg/index.html", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stg.ts"}], "outputHashing": "all", "optimization": true, "extractLicenses": true, "sourceMap": true, "namedChunks": true, "aot": true}, "prod": {"budgets": [{"type": "initial", "maximumWarning": "12mb", "maximumError": "16mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "200kb"}], "assets": ["src/assets/icons/favicon.ico", "src/assets", {"glob": "robots.txt", "input": "src/robots/prod/", "output": "./"}], "index": "src/html/prod/index.html", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all", "optimization": true, "extractLicenses": true, "sourceMap": false, "namedChunks": false, "aot": true}}, "defaultConfiguration": "dev"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"dev": {"buildTarget": "cloud-savers-portal:build:dev"}, "qa": {"buildTarget": "cloud-savers-portal:build:qa"}, "stg": {"buildTarget": "cloud-savers-portal:build:stg"}, "prod": {"buildTarget": "cloud-savers-portal:build:prod"}}, "defaultConfiguration": "dev"}, "extract-i18n": {"builder": "@angular/build:extract-i18n"}, "test": {"builder": "@angular/build:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/assets/icons/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss"]}}}}}, "cli": {"analytics": false}}