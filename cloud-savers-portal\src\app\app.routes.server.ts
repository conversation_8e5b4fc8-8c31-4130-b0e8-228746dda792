import { RenderMode, ServerRoute } from '@angular/ssr';

export const serverRoutes: ServerRoute[] = [
  {
    path: '', // This renders the "/" route on the client (CSR)
    renderMode: RenderMode.Server,
  },
  {
    path: 'skyscanner4cloud', // // This page requires user-specific data, so we use SSR
    renderMode: RenderMode.Server,
  },
  {
    path: 'service/:slug',
    renderMode: RenderMode.Server,
  },
  {
    path: 'order-billing',
    renderMode: RenderMode.Server,
  },
  {
    path: 'offers',
    renderMode: RenderMode.Server,
  },
  {
    path: 'offers/:title',
    renderMode: RenderMode.Server,
  },
  {
    path: 'order-history',
    renderMode: RenderMode.Server,
  },
  {
    path: 'compare/:slug',
    renderMode: RenderMode.Server,
  },
  {
    path: 'legal/cookie-policy',
    renderMode: RenderMode.Client,
  },
  {
    path: 'legal/terms-services',
    renderMode: RenderMode.Client,
  },
  {
    path: 'legal/privacy-policy',
    renderMode: RenderMode.Client,
  },
  {
    path: 'blogs',
    renderMode: RenderMode.Server,
  },
  {
    path: 'blog/:id',
    renderMode: RenderMode.Server,
  },
  {
    path: '**',
    renderMode: RenderMode.Client
  }
];
