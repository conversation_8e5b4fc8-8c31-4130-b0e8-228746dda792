.container {
    width: 100%;
    height: auto;
    background-image: url('../../../assets/images/background_img.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.head-row {
    display: flex;
    align-items: center;
    padding: 20px 50px;
    background-color: #FFFFFF;
    border-bottom: 1px solid #E5E7EB;

    .back-arrow {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 16px;
        color: #374151;
        cursor: pointer;
        text-align: start;
        width: 10%;
    }

    .pricing-head {
        font-size: 20px;
        font-weight: 600;
        color: #000000;
        text-align: center;
        flex: 1;
    }

    .menu-button {
        display: none;
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #000000;
    }
}

.blog-container {
    display: grid;
    grid-template-columns: 230px 1fr 270px;
    gap: 2rem;
    max-width: 1600px;
    margin: 0 auto;
    padding: 2rem;
}

.mobile-side-panel {
    position: fixed;
    top: 0;
    right: -280px;
    width: 280px;
    height: 100%;
    background: #fff;
    z-index: 1000;
    transition: transform 0.3s ease-in-out;
    overflow-y: auto;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    padding: 20px 20px 60px 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    &.active {
        transform: translateX(-280px);
    }

    .close-sidebar {
        position: absolute;
        top: 20px;
        right: 20px;
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #666;
        padding: 5px;
        margin: 0;
        z-index: 1002;

        &:hover {
            color: #000;
        }
    }

    .mobile-sidebar-content {
        width: 100%;
        padding: 20px 0;
    }
}

.side-panel-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 100;
    opacity: 0;
    transition: opacity 0.3s ease;

    &.active {
        display: block;
        opacity: 1;
    }
}

/* Left Sidebar */
.sidebar-left {
    background: #FFFFFF;
    padding: 1.5rem;
    border-radius: 8px;
    transition: transform 0.3s ease;
    height: fit-content;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
}

.sidebar-title {
    font-size: 18px;
    font-weight: 600;
    line-height: 28px;
    margin-bottom: 1rem;
    color: #000000;
}

.category-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.category-item {
    margin-bottom: 0.5rem;
}

.category-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    text-decoration: none;
    color: #666;
    transition: color 0.2s;
    cursor: pointer;
}

.category-link:hover {
    color: #007bff;
}

.category-name {
    font-size: 0.9rem;
}

.category-count {
    background: #e9ecef;
    color: #666;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Main Content */
.main-content {
    min-height: 100vh;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.page-title {
    font-size: 24px;
    line-height: 32px;
    font-weight: 700;
    color: #111827;
    margin: 0;
}

.search-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    background: #FFFFFF;
    padding: 1.5rem;
    border-radius: 8px;
    height: fit-content;
    width: 100%;
    margin-bottom: 15px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
}

.search-container {
    position: relative;
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    padding: 0 15px;
    box-sizing: border-box;

    @media (max-width: 768px) {
        max-width: 90%;
        margin: 15px auto;
        display: flex;
        justify-content: center;
    }
}

.search-input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    background: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: border-color 0.2s;
}

.search-input:focus {
    border-color: #4F46E5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
    outline: none;
}

.search-icon {
    position: absolute;
    left: 25px;
    top: 50%;
    transform: translateY(-50%);
    color: #9CA3AF;
    pointer-events: none;

    @media (max-width: 768px) {
        left: 28px;
    }
}

/* Blog Posts */
.blog-posts {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.blog-card {
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
    cursor: pointer;
}

.blog-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

//   .blog-image {
//     // position: relative;
//     // width: 300px;
//     // height: 200px;
//     // flex-shrink: 0;
//   }

.blog-image img {
    width: 100%;
    max-height: 300px;
    min-height: 200px;
    object-fit: cover;
    object-position: top;
}

.blog-category {
    position: relative;
    top: 1rem;
    left: 1rem;
    padding: 0.3rem 0.8rem;
    border-radius: 4px;
    color: #6B21A8;
    font-size: 0.8rem;
    font-weight: 500;
}

.blog-content {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.blog-title {
    font-size: 18px;
    font-weight: 700;
    color: #111827;
    margin: 8px 0 1rem 0;
    line-height: 28px;
}

.blog-description {
    color: #4B5563;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    margin: 0 0 1.5rem 0;
    flex: 1;
}

.blog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.author-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.author-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.author-details {
    display: flex;
    flex-direction: column;
}

.author-name {
    font-weight: 500;
    color: #111827;
    font-size: 14px;
    line-height: 20px;
}

.post-date {
    color: #6B7280;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
}

.read-more {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    border: none;
    background: none;
}

.read-more:hover {
    text-decoration: underline;
}

/* Right Sidebar */
.sidebar-right {
    background: #f8f9fa;
    border-radius: 8px;
    height: fit-content;
    transition: transform 0.3s ease;
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    background: #e9ecef;
    color: #495057;
    padding: 0.4rem 0.8rem;
    border-radius: 16px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.tag:hover {
    background: #007bff;
    color: white;
}

@media only screen and (max-width: 1250px) {

    .head-row {
        padding: 12px 16px 12px 56px;
        position: relative;

        .pricing-head {
            font-size: 16px;
            text-align: center;
            width: 100%;
            padding: 0 10px;
        }
    }

    .blog-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding-bottom: 90px;
    }

    .sidebar-left {
        position: fixed;
        top: 0;
        left: -300px;
        width: 280px;
        height: 100vh;
        z-index: 101;
        overflow-y: auto;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);

        &.active {
            transform: translateX(300px);
        }
    }

    .sidebar-right {
        position: fixed;
        top: 0;
        right: -300px;
        width: 280px;
        height: 100vh;
        z-index: 101;
        overflow-y: auto;
        padding: 1.5rem;
        box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);

        &.active {
            transform: translateX(-300px);
        }
    }

}