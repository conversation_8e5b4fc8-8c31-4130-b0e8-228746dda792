<div class="results">
    <!-- <div class="results-header">
        <div class="result-title">
            <div class="title">Cloud Instance Pricing</div>
            <div>Compare and select instances across providers</div>
        </div>
        <div class="result-sort">
            <div class="icon-button">
                <iconify-icon icon="material-symbols:filter-list"></iconify-icon>
                <span>Filter</span>
            </div>
            <div class="icon-button">
                <iconify-icon icon="material-symbols:download-sharp"></iconify-icon>
                <span>Export</span>
            </div>
        </div>
    </div> -->
    <!-- <div class="calculate-savings">
        <div class="saving-head">
            <div class="saving-title">
                <img src="assets/images/savings.png">
                <div class="saving-text">
                    <div class="title">Still Using On-Demand Instances?</div>
                    <div class="saving-desc">Save up to 72% with Reserved Instances</div>
                </div>
            </div>
            <button mat-button class="calculate-btn">Calculate Savings</button>
        </div>
        <div class="saving-desc">Lock in significant savings with 1-3 year commitments on your EC2 instances. Perfect
            for predictable workloads.</div>
    </div> -->
    <div class="results-header">
        <div class="price-filter">Sort by :</div>
        <mat-form-field appearance="outline" class="filter-select">
            <mat-select [formControl]="priceFilter" (selectionChange)="onPriceFilterChange($event.value)">
                <mat-option *ngFor="let filter of priceFilters || []" [value]="filter.ID">{{filter.NAME}}</mat-option>
            </mat-select>
        </mat-form-field>
    </div>
    <div class="no-results-message" *ngIf="noResults">
        <iconify-icon icon="material-symbols:info-outline" width="24" height="24"></iconify-icon>
        <span>No matching results found.</span>
    </div>
    <div *ngIf="!noResults" class="instance-wrapper">
        <div class="desktop-view">
            <div class="record-range">
                {{getRecordRangeText()}}
            </div>
            <div class="instance-cards">
                <div *ngFor="let group of pricingData || []" class="instance-card-container">
                    <div class="instance-card">
                        <div class="instance-header">
                            <div class="provider-type">
                                <img class="provider-logo"
                                    [src]="'assets/images/' + providerIconMap[group?.document?.service_provider | lowercase]">
                                <div class="provider-details">
                                    <div class="provider-name">{{group?.document?.service_provider | uppercase}}
                                        <span>- {{group?.document?.product_type}}</span>
                                    </div>
                                    <div class="instance-details">{{group?.document?.vcpu_cores}}
                                        vCPU • {{group?.document?.ram_gb}} GB RAM •
                                        {{group?.document?.storage_spec}}
                                    </div>
                                </div>
                            </div>
                            <div class="instance-price">
                                <div class="price">
                                    <div class="price-period-icon"
                                        matTooltip="This is an approximate price, actual price may vary.">
                                        <iconify-icon icon="material-symbols:info-outline-rounded"></iconify-icon>
                                    </div>
                                    <div class="price-period">Avg.</div>
                                    <div>${{getMonthlyPrice(group?.document?.hourly_rate_usd,
                                        group?.document?.service_provider)}}</div>
                                    <div class="price-period">/mo</div>
                                </div>
                                <div class="instance-family">
                                    {{group?.document?.instance_family}}
                                </div>
                            </div>
                        </div>
                        <mat-divider></mat-divider>
                        <div class="instance-specs">
                            <div class="spec-item">
                                <div class="spec-label">Product Name</div>
                                <div class="spec-value">{{group?.document?.product_name}}</div>
                            </div>
                            <div class="spec-item">
                                <div class="spec-label">Instance Type</div>
                                <div class="spec-value">{{group?.document?.vm_name}}</div>
                            </div>
                            <div class="spec-item">
                                <div class="spec-label">Processor Type</div>
                                <div class="spec-value">{{group?.document?.cpu_model}}
                                </div>
                            </div>
                            <div class="spec-item">
                                <div class="spec-label">Network</div>
                                <div class="spec-value">{{group?.document?.network_performance}}
                                </div>
                            </div>
                        </div>
                        <div class="instance-action">
                            <div class="location">
                                <div class="location-div">
                                    <iconify-icon icon="material-symbols:location-on-outline-rounded"></iconify-icon>
                                    {{group?.document?.cs_global_region}} - {{group?.document?.cs_geo_region}} -
                                    {{group?.document?.cs_region}} - {{group?.document?.cs_city}}
                                </div>
                                <div class="location-div">
                                    <iconify-icon icon="material-symbols:settings-outline-rounded"></iconify-icon>
                                    {{group?.document?.os_type}} - {{group?.document?.os}}
                                </div>
                            </div>
                            <div class="action-buttons">
                                <button mat-button class="compare-btn" [disabled]="compareInstances.length >= 4"
                                    (click)="addToCompare(group?.document)" *ngIf="!mobile">Compare</button>
                                <button mat-icon-button (click)="viewInstanceDetails(group?.document)"
                                    class="action-icons">
                                    <iconify-icon icon="lsicon:view-filled"></iconify-icon>
                                </button>
                                <ng-container
                                    *ngIf="!selectedCartIds?.includes(group?.document?.record_id?.toString()); else showQty">
                                    <button mat-icon-button (click)="addToCart(group?.document)" class="action-icons">
                                        <iconify-icon icon="mdi:cart-outline"></iconify-icon>
                                    </button>
                                </ng-container>
                                <ng-template #showQty>
                                    <div class="quantity-selector">
                                        <div class="quantity-controls">
                                            <button class="qty-btn"
                                                (click)="decreaseQuantity(group?.document?.record_id, group?.document)">−</button>
                                            <input type="text" class="qty-input"
                                                [value]="selectedCartQuantities[group?.document?.record_id?.toString()]"
                                                readonly>
                                            <button class="qty-btn"
                                                (click)="increaseQuantity(group?.document?.record_id, group?.document)">+</button>
                                        </div>
                                    </div>
                                </ng-template>
                            </div>
                        </div>
                    </div>
                    <div class="instance-card instance-specs instance-card-price">
                        <div class="price-item">
                            <div class="payment-option">{{formatText(group?.document?.payment_option)}}</div>
                        </div>
                        <div class="price-item">
                            <div class="spec-label">{{formatText(group?.document?.term)}}</div>
                            <div class="spec-value">${{(group?.document?.upfront_amount_usd)?.toFixed(2)}}</div>
                        </div>
                        <div class="price-item">
                            <div class="spec-label">Monthly</div>
                            <div class="spec-value">${{(group?.document?.monthly_rate_usd)?.toFixed(2)}}</div>
                        </div>
                        <div class="price-item">
                            <div class="payment-option">{{formatText(group?.document?.payment_plan)}}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="pagination">
                <!-- <div class="page-info">
                    Page {{currentPage}} of {{totalPages}}
                </div> -->
                <div class="pagination-controls">
                    <button mat-button [disabled]="currentPage === 1" (click)="setCurrentPage(currentPage - 1)">
                        <iconify-icon icon="material-symbols:chevron-left"></iconify-icon>
                    </button>
                    <!-- <span class="current-page">{{currentPage}}</span> -->
                    <button mat-button [disabled]="!hasNextPage()" (click)="setCurrentPage(currentPage + 1)">
                        <iconify-icon icon="material-symbols:chevron-right"></iconify-icon>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>