import { CommonModule, isPlatform<PERSON>rowser } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, HostListener, Inject, PLATFORM_ID } from '@angular/core';
import { MaterialModule } from '../../material-module';
import { Header } from '../header/header';
import { Footer } from '../footer/footer';
import { MobileFooter } from '../mobile-footer/mobile-footer';
import { ActivatedRoute, Router } from '@angular/router';
import { FormControl } from '@angular/forms';
import { Auth } from '../../services/auth';
import { MatDialog } from '@angular/material/dialog';
import { Login } from '../../auth/login/login';
import { CartService } from '../../services/cart-service';
import { Notification } from '../../services/notification';
import { MetaService } from '../../services/meta.service';
import { environment } from '../../../environments/environment';
import { colorObj } from '../../shared/color-object';
import * as offersData from '../../../assets/shared/internal-offers/internal-offers.json';

@Component({
  selector: 'app-offer-details',
  imports: [
    CommonModule,
    MaterialModule,
    Header,
    Footer,
    MobileFooter
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './offer-details.html',
  styleUrl: './offer-details.scss'
})
export class OfferDetails {

  loading: boolean = false;
  selectedRegion = new FormControl('');
  mobile: boolean = false;
  public websiteUrl = environment.PORTAL_URL;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenWidth();
  }
  selectedOffer: any = null;

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private router: Router,
    private route: ActivatedRoute,
    private authService: Auth,
    private cartService: CartService,
    private dialog: MatDialog,
    private notify: Notification,
    private metaService: MetaService
  ) { }

  ngOnInit() {
    this.checkScreenWidth();
    this.scrollToTopContent();
    this.loadOfferData();
  }

  ngAfterViewInit() {
    this.checkScreenWidth();
  }

  private checkScreenWidth() {
    if (isPlatformBrowser(this.platformId)) {
      if (window.innerWidth <= 1250) {
        this.mobile = true;
      } else {
        this.mobile = false;
      }
    }
  }

  private loadOfferData() {
    const offerTitle = this.route.snapshot.paramMap.get('title');

    if (offerTitle) {
      const allOffers = [
        ...(offersData as any).default?.FEATURED_OFFERS || [],
        ...(offersData as any).default?.OFFERS || []
      ];

      const offer = allOffers.find(o => {
        const offerSlug = this.slugify(o.title);
        return offerSlug === offerTitle;
      });

      if (offer) {
        this.selectedOffer = offer;
        this.initializeRegionSelector();
        this.updateMetaTags(offer);
        return;
      }
    }
    const navigation = this.router.currentNavigation();
    if (isPlatformBrowser(this.platformId)) {
      const state = navigation?.extras?.state || history.state;
      if (state && state.selectedOffer) {
        this.selectedOffer = state.selectedOffer;
        this.initializeRegionSelector();
      } else {
        // No offer data available, redirect back to home
        this.router.navigate(['/']);
      }
    }
  }

  private updateMetaTags(offer: any) {
    const title = `Exclusive Cloud Offer Details | Cloudatler`;
    const description = `View the details of exclusive offers from leading cloud providers.Understand the terms, savings, and benefits to choose the best deal for your needs.`;
    const canonicalPath = `/offers/${this.slugify(offer.title)}`;

    this.metaService.updateTitle(title);
    this.metaService.updateDescription(description);
    this.metaService.updateOgTitle(title);
    this.metaService.updateOgDescription(description);
    this.metaService.updateCanonicalUrl(this.websiteUrl, canonicalPath);
    this.metaService.updateHreflangTags(
      [
        { hreflang: 'en-us', href: canonicalPath },
        { hreflang: 'x-default', href: canonicalPath },
        { hreflang: 'en', href: canonicalPath }
      ],
      this.websiteUrl
    );
  }

  private slugify(text: string): string {
    return text
      ?.toString()
      ?.toLowerCase()
      ?.trim()
      ?.replace(/[\/\s]+/g, '-')
      ?.replace(/[^\w-]+/g, '')
      ?.replace(/-+/g, '-')
      ?.replace(/^-+|-+$/g, '');
  }

  private initializeRegionSelector() {
    if (this.selectedOffer?.region?.length > 0) {
      // Set default region in the selector
      const defaultRegion = this.selectedOffer.region.find(r => r.default === true);
      if (defaultRegion) {
        this.selectedRegion.setValue(defaultRegion.name);
      }
    }
  }

  getLowestPriceForDefaultRegion(): number {
    const regionData = this.getSelectedRegionData();
    if (!regionData) return 0;
    const awsPrice = regionData.aws || 0;
    const azurePrice = regionData.azure || 0;
    return Math.min(awsPrice, azurePrice);
  }

  getProviderWithLowestPrice(): string {
    const regionData = this.getSelectedRegionData();
    if (!regionData) return '';
    return (regionData.azure <= regionData.aws) ? 'Azure' : 'AWS';
  }

  onRegionChange(regionName: string) {
    this.selectedRegion.setValue(regionName);
  }

  private getSelectedRegionData(): any {
    const selectedRegionName = this.selectedRegion.value;
    if (!this.selectedOffer?.region) return null;
    // If a region is selected, find that specific region
    if (selectedRegionName) {
      const selectedRegion = this.selectedOffer.region.find(region => region.name === selectedRegionName);
      if (selectedRegion) return selectedRegion;
    }
    // Fall back to default region
    return this.selectedOffer.region.find(region => region.default === true) || null;
  }

  addToCart(instance: any) {
    if (!this.authService.isLoggedIn()) {
      const dialogRef = this.dialog.open(Login, {
        width: '558px',
        height: 'auto',
        disableClose: true
      });
      dialogRef.afterClosed().subscribe(result => {
        if (result === true) {
          this.updateItemQuantity(instance, 1);
        }
      });
    }
    else {
      this.updateItemQuantity(instance, 1);
    }
  }

  updateItemQuantity(element: any, itemQuantity: number) {
    this.loading = true;
    element.selection = {
      "region": this.selectedRegion.value,
      "service_provider": this.getProviderWithLowestPrice(),
      "cost": this.getLowestPriceForDefaultRegion()
    }
    let obj = {
      "RESOURCE": [{
        "RESOURCE_TYPE": "internal",
        "RESOURCE_ID": element?.record_id,
        "QUANTITY": itemQuantity,
        "SERVICE_DETAILS": element
      }]
    }
    this.cartService.addItemToCart(obj).subscribe({
      next: (resp) => {
        if (resp.status === 200 && resp.object.length > 0) {
          this.cartService.cartUpdated$.next();
          this.cartService.notifyLoginSuccess();
        }
        this.loading = false;
      },
      error: (e) => {
        this.loading = false;
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        );
      }
    });
  }

  backToHome() {
    this.router.navigate(['/offers']);
  }

  scrollToTopContent() {
    setTimeout(() => {
      if (isPlatformBrowser(this.platformId)) {
        const element = document.getElementById('pageContent');
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }
    }, 100);
  }

}