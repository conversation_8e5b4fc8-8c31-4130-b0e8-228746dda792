.cookie-policy {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    padding: 30px;
}

.cookie-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 60%;
}

.title {
    font-size: 16px;
    font-weight: 500;
    color: #111827;
}

.desc {
    font-size: 14px;
    line-height: 20px;
    color: #4B5563;
}

.cookie-actions {
    display: flex;
    align-items: center;
    justify-content: end;
    gap: 10px;
    width: 40%;
}

.deny-btn {
    background-color: #FFFFFF;
    color: #374151;
    font-size: 14px;
    padding: 10px 15px;
    border-radius: 8px;
    border: 1px solid #E5E7EB;
}

.accept-btn {
    background-color: #35A5DB;
    color: #FFFFFF;
    font-size: 14px;
    padding: 10px 15px;
    border-radius: 8px;
}

@media only screen and (max-width: 1250px) {
    .cookie-policy {
        flex-direction: column;
    }

    .cookie-content {
        width: 100%;
    }

    .cookie-actions {
        width: 100%;
    }
}