version: 0.2

env:
    variables:
        # S3_BUCKET: aws-cloud-savers-fe-portal-build-dev
        CLOUDFRONT: E1GMJ0MHV252EW
phases:
  install:
    runtime-versions:
        nodejs: 20
    commands:
    - echo $CODEBUILD_SRC_DIR
    - npm install -y npm@latest
    - npm install -g @angular/cli
    - rm package-lock.json
  pre_build:
    commands:
    - cd cloud-savers-portal
    - npm install
    - aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin 349024249383.dkr.ecr.ap-south-1.amazonaws.com
    # Get commit hash value to tag the image
    - IMAGE_TAG=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
    # Application name
    - APPLICATION_NAME=aws-cs-fe-portal-ecr-dev
    # ECR Repository URI
    - REPOSITORY_URI=349024249383.dkr.ecr.ap-south-1.amazonaws.com/$APPLICATION_NAME
  build:
    commands:
    - echo Building Angular browser and server...
    - echo Build started on `date`
    - export NODE_OPTIONS=--max-old-space-size=8192
    - ng build --configuration=dev
    - echo Building the Docker image...
    - docker build -t $APPLICATION_NAME:$IMAGE_TAG -f Dockerfile-dev .
    - docker tag $APPLICATION_NAME:$IMAGE_TAG $REPOSITORY_URI:$IMAGE_TAG
    # docker build -t cloud-savers-portal -f Dockerfile-dev .
    # docker run -p 8190:8190 cloud-savers-portal
    - ls -l -F
  post_build:
    commands:
    - echo Pushing to ECR
    - docker push $REPOSITORY_URI:$IMAGE_TAG
    - echo Writing image definitions file...
    - echo "Listing workspace root:"
    - ls -la
    - echo "Listing source dir:"
    - ls -la $CODEBUILD_SRC_DIR || true
    - echo "Print imagedefinitions.json if exists:"
    - if [ -f imagedefinitions.json ]; then cat imagedefinitions.json; else echo "NO imagedefinitions.json"; fi
    # Give your container name
    - printf '[{"name":"aws-cs-fe-portal-cont-dev", "imageUri":"%s"}]' $REPOSITORY_URI:$IMAGE_TAG > imagedefinitions.json
    - aws cloudfront create-invalidation --distribution-id=${CLOUDFRONT} --paths '/*'
    - echo Build completed on `date`
artifacts:
  base-directory: cloud-savers-portal
  files:
    - imagedefinitions.json
cache:
  paths:
    - '/root/.m2/**/*'