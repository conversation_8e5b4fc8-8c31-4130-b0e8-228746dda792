import { CommonModule, isPlatformBrowser } from '@angular/common';
import { AfterViewInit, Component, CUSTOM_ELEMENTS_SCHEMA, HostListener, Inject, PLATFORM_ID } from '@angular/core';
import { MaterialModule } from '../../material-module';
import { Header } from '../header/header';
import { ActivatedRoute, Router } from '@angular/router';
import { PROVIDER_ICON_MAP } from '../../shared/filter-headers.constants';
import { environment } from '../../../environments/environment';
import { Auth } from '../../services/auth';
import { CartService } from '../../services/cart-service';
import { Notification } from '../../services/notification';
import { colorObj } from '../../shared/color-object';
import { MatDialog } from '@angular/material/dialog';
import { Login } from '../../auth/login/login';
import { MetaService } from 'src/app/services/meta.service';

@Component({
  selector: 'app-comparison-results',
  imports: [
    CommonModule,
    MaterialModule,
    Header
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './comparison-results.html',
  styleUrl: './comparison-results.scss'
})
export class ComparisonResults implements AfterViewInit {

  compareInstances: any = [];
  selectedInstanceId: string | null = null;
  selectedInstance: any = null;
  get providerIconMap() {
    return PROVIDER_ICON_MAP;
  }
  public monthlyHours = environment.HOURS_IN_A_MONTH;
  public websiteUrl = environment.PORTAL_URL;
  mobile: boolean = false;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenWidth();
  }

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private router: Router,
    private route: ActivatedRoute,
    private authService: Auth,
    private notify: Notification,
    private cartService: CartService,
    private metaService: MetaService,
    private dialog: MatDialog
  ) { }

  ngOnInit() { }

  ngAfterViewInit() {
    this.checkScreenWidth();
    this.scrollToTopContent();
    this.loadCompareData();
  }

  private checkScreenWidth() {
    if (isPlatformBrowser(this.platformId)) {
      if (window.innerWidth <= 1250) {
        this.mobile = true;
      } else {
        this.mobile = false;
      }
    }
  }

  loadCompareData() {
    const slug = this.route.snapshot.paramMap.get('slug');
    const parts = slug.split('--vs--');
    let resources = [];
    parts.map(part => {
      const parts = part.split('--');
      const id = parts[parts.length - 2];
      const recordId = parts[parts.length - 1];
      resources.push({
        "ID": id,
        "RECORD_ID": recordId
      });
    })
    if (resources.length > 0) {
      const obj = {
        "RESOURCES": resources
      };
      this.authService.getCompareResources(obj).subscribe({
        next: (data: any) => {
          if (data && data.object) {
            this.compareInstances = data.object.results[0].hits;
          }
        },
        error: (e) => {
          this.notify.showNotification(
            e?.error?.message,
            "top",
            (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
            e?.error?.status
          )
        }
      });
    }
    this.updateMetaTags(slug);
  }

  private updateMetaTags(slug) {
    const title = `Cloudatler | Compare Cloud Services & Manage Costs Securely`;
    const description = `Securely compare cloud providers, find exclusive deals, and manage costs with Cloudatler. Our FinOps platform turns bills into actionable insights with bank-level security.`;
    const canonicalPath = `/compare/${slug}`;
    this.metaService.updateTitle(title);
    this.metaService.updateDescription(description);
    this.metaService.updateOgTitle(title);
    this.metaService.updateOgDescription(description);
    this.metaService.updateCanonicalUrl(this.websiteUrl, canonicalPath);
    this.metaService.updateHreflangTags(
      [
        { hreflang: 'en-us', href: canonicalPath },
        { hreflang: 'x-default', href: canonicalPath },
        { hreflang: 'en', href: canonicalPath }
      ],
      this.websiteUrl
    );
  }

  getMonthlyPrice(hourlyRate: number, provider: string): string {
    const monthlyHours = this.monthlyHours[!!provider ? provider.toLowerCase() : ''];
    return hourlyRate ? (hourlyRate * monthlyHours)?.toFixed(2) : '--';
  }

  getFeatureIcon(featureValue: any): string {
    if (featureValue === true || featureValue === 'yes' || featureValue === 'available') {
      return 'material-symbols:check-circle';
    } else if (featureValue === false || featureValue === 'no' || featureValue === 'unavailable') {
      return 'material-symbols:cancel';
    }
    // return 'mingcute:question-line';
    return 'mdi:tick';
  }

  getFeatureClass(featureValue: any): string {
    if (featureValue === true || featureValue === 'yes' || featureValue === 'available') {
      return 'feature-available';
    } else if (featureValue === false || featureValue === 'no' || featureValue === 'unavailable') {
      return 'feature-unavailable';
    }
    // return 'feature-unknown';
    return 'feature-available';
  }

  getComparisonCount(): string {
    const count = this.compareInstances.length;
    return `Comparing ${count} instance${count !== 1 ? 's' : ''}`;
  }

  // Extracts numeric value from a string, handling various formats
  private extractNumericValue(value: any): number | null {
    if (value === null || value === undefined || value === '--' || value === '') {
      return null;
    }
    // If it's already a number, return it
    if (typeof value === 'number' && !isNaN(value)) {
      return value;
    }
    // Convert to string and clean up
    const stringValue = String(value).toLowerCase()?.trim();
    // Remove common units and symbols
    const cleanValue = stringValue
      ?.replace(/[,$%]/g, '') // Remove commas, dollar signs, percentages
      ?.replace(/\s*(gb|ghz|vcpu|cores?|hours?|days?|gbps?|mbps?)\s*/gi, '') // Remove units
      ?.replace(/\s+/g, '') // Remove spaces
      ?.trim();
    const numericValue = parseFloat(cleanValue);
    return isNaN(numericValue) ? null : numericValue;
  }

  // Determines the highlighting class for a numeric value in a row
  getNumericHighlightClass(rowValues: any[], currentValue: any, fieldType: string): string {
    // Only highlight specific numeric fields
    const numericFields = ['vcpu_cores', 'ram_gb', 'cpu_clock_ghz', 'hourly_rate_usd', 'monthly_rate_usd', 'upfront_amount_usd', 'avg_monthly_price', 'gpu_count', 'gpu_memory_mb', 'sla_guarantee'];
    if (!numericFields?.includes(fieldType)) {
      return '';
    }
    const numericValues: number[] = [];
    const currentNumeric = this.extractNumericValue(currentValue);
    if (currentNumeric === null) {
      return '';
    }
    // Extract all numeric values from the row
    rowValues.forEach(value => {
      const numeric = this.extractNumericValue(value);
      if (numeric !== null) {
        numericValues.push(numeric);
      }
    });
    // Need at least 2 values to compare
    if (numericValues.length < 2) {
      return '';
    }
    const minValue = Math.min(...numericValues);
    const maxValue = Math.max(...numericValues);
    // If all values are the same, no highlighting
    if (minValue === maxValue) {
      return '';
    }
    // For pricing fields, lower is better (green)
    const pricingFields = ['hourly_rate_usd', 'monthly_rate_usd', 'upfront_amount_usd', 'avg_monthly_price'];
    if (pricingFields?.includes(fieldType)) {
      if (currentNumeric === minValue) return 'highlight-best';
      if (currentNumeric === maxValue) return 'highlight-worst';
    } else {
      // For performance fields (vCPU, RAM, etc.), higher is usually better
      if (currentNumeric === maxValue) return 'highlight-best';
      if (currentNumeric === minValue) return 'highlight-worst';
    }
    return '';
  }

  // Get values for a specific field across all instances
  private getRowValues(fieldName: string): any[] {
    return this.compareInstances.map(instance => {
      switch (fieldName) {
        case 'vcpu_cores':
          return instance?.document?.vcpu_cores;
        case 'ram_gb':
          return instance?.document?.ram_gb;
        case 'cpu_clock_ghz':
          return instance?.document?.cpu_clock_ghz;
        case 'hourly_rate_usd':
          return instance?.document?.hourly_rate_usd;
        case 'monthly_rate_usd':
          return instance?.document?.monthly_rate_usd;
        case 'upfront_amount_usd':
          return instance?.document?.upfront_amount_usd;
        case 'avg_monthly_price':
          return this.getMonthlyPrice(instance?.document?.hourly_rate_usd, instance?.document?.service_provider);
        case 'gpu_count':
          return instance?.document?.gpu_count;
        case 'gpu_memory_mb':
          return this.extractNumericValue(instance?.document?.gpu_memory_mb);
        case 'sla_guarantee':
          return instance?.document?.sla_guarantee;
        default:
          return null;
      }
    });
  }

  // Get highlight class for a specific instance and field
  getHighlightClass(instance: any, fieldName: string): string {
    const rowValues = this.getRowValues(fieldName);
    let currentValue;
    switch (fieldName) {
      case 'vcpu_cores':
        currentValue = instance?.document?.vcpu_cores;
        break;
      case 'ram_gb':
        currentValue = instance?.document?.ram_gb;
        break;
      case 'cpu_clock_ghz':
        currentValue = instance?.document?.cpu_clock_ghz;
        break;
      case 'hourly_rate_usd':
        currentValue = instance?.document?.hourly_rate_usd;
        break;
      case 'monthly_rate_usd':
        currentValue = instance?.document?.monthly_rate_usd;
        break;
      case 'upfront_amount_usd':
        currentValue = instance?.document?.upfront_amount_usd;
        break;
      case 'avg_monthly_price':
        currentValue = this.getMonthlyPrice(instance?.document?.hourly_rate_usd, instance?.document?.service_provider);
        break;
      case 'gpu_count':
        currentValue = instance?.document?.gpu_count;
        break;
      case 'gpu_memory_mb':
        currentValue = this.extractNumericValue(instance?.document?.gpu_memory_mb);
        break;
      case 'sla_guarantee':
        currentValue = instance?.document?.sla_guarantee;
        break;
      default:
        currentValue = instance?.document?.[fieldName];
    }
    return this.getNumericHighlightClass(rowValues, currentValue, fieldName);
  }

  // Check if any instance has meaningful GPU data
  hasGpuData(): boolean {
    if (!this.compareInstances || this.compareInstances.length === 0) {
      return false;
    }
    return this.compareInstances.some(instance => {
      const doc = instance?.document;
      if (!doc) return false;
      const gpuModel = doc.gpu_model;
      const hasGpuModel = !!gpuModel &&
        gpuModel !== '' && gpuModel.toString().toLowerCase() !== '0' &&
        gpuModel.toString().toLowerCase() !== '0.0' && gpuModel.toString().toLowerCase() !== 'na';
      const gpuCount = doc.gpu_count;
      const hasGpuCount = !!gpuCount &&
        gpuCount !== '' && gpuCount.toString().toLowerCase() !== '0' &&
        gpuCount.toString().toLowerCase() !== '0.0' && gpuCount.toString().toLowerCase() !== 'na';
      const gpuMemory = doc.gpu_memory_mb;
      const hasGpuMemory = !!gpuMemory &&
        gpuMemory !== '' && gpuMemory.toString().toLowerCase() !== '0' &&
        gpuMemory.toString().toLowerCase() !== '0.0' && gpuMemory.toString().toLowerCase() !== 'na';
      return hasGpuModel || hasGpuCount || hasGpuMemory;
    });
  }

  selectInstance(instance: any) {
    this.selectedInstanceId = instance.document.record_id;
    this.selectedInstance = instance.document;
  }

  clearSelection() {
    this.selectedInstanceId = null;
    this.selectedInstance = null;
  }

  isInstanceSelected(instance: any): boolean {
    return this.selectedInstanceId === instance?.document?.record_id;
  }

  removeFromComparison(instance: any) {
    if (this.authService.isLoggedIn()) {
      let obj = {
        "ID": [instance?.document?.id]
      };
      this.cartService.removeComparison(obj).subscribe({
        next: (data: any) => {
          if (data && data.object && Array.isArray(data.object)) {
            this.compareInstances = data.object;
            if (isPlatformBrowser(this.platformId)) {
              window.localStorage.setItem('COMPARE_INSTANCES', JSON.stringify(this.compareInstances));
            }
          }
        },
        error: (e) => {
          this.notify.showNotification(
            e?.error?.message,
            "top",
            (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
            e?.error?.status
          )
        }
      });
    }
    // Clear selection if removing the selected instance
    if (this.selectedInstanceId === instance?.document?.record_id) {
      this.clearSelection();
    }
    // Remove from comparison
    const updatedInstances = this.compareInstances.filter(i => i.document?.record_id !== instance.document?.record_id);
    this.compareInstances = updatedInstances;
    if (isPlatformBrowser(this.platformId)) {
      window.localStorage.setItem('COMPARE_INSTANCES', JSON.stringify(updatedInstances));
    }
    // Clear selection if no instances left
    if (this.compareInstances.length === 0) {
      this.clearSelection();
    }
  }

  addSelectedToCart() {
    if (this.selectedInstance) {
      if (!this.authService.isLoggedIn()) {
        const dialogRef = this.dialog.open(Login, {
          width: '558px',
          height: 'auto',
          disableClose: true
        });
        dialogRef.afterClosed().subscribe(result => {
          if (result === true) {
            this.updateItemQuantity(this.selectedInstance, 1);
          }
        });
      }
      else {
        this.updateItemQuantity(this.selectedInstance, 1);
      }
    }
  }

  updateItemQuantity(element: any, itemQuantity: number) {
    let obj = {
      "RESOURCE": [{
        "RESOURCE_TYPE": "external",
        "RESOURCE_ID": element?.record_id,
        "QUANTITY": itemQuantity,
        "SERVICE_DETAILS": element
      }]
    }
    this.cartService.addItemToCart(obj).subscribe({
      next: (resp) => {
        if (resp.status === 200 && resp.object.length > 0) {
          this.cartService.cartUpdated$.next();
          this.cartService.notifyLoginSuccess();
          this.router.navigate(['/skyscanner4cloud']);
        }
      },
      error: (e) => {
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        );
      }
    });
  }

  viewSelectedDetails() {
    if (this.selectedInstance) {
      const serviceProvider = this.slugify(this.selectedInstance?.service_provider);
      const productName = this.slugify(this.selectedInstance?.product_name);
      const slug = serviceProvider + '-' + productName + '--' + this.selectedInstance?.record_id;
      this.router.navigate(['/service', slug]);
    }
  }

  slugify(text: string): string {
    return text
      ?.toString()
      ?.toLowerCase()
      ?.trim()
      ?.replace(/[\/\s]+/g, '-')
      ?.replace(/[^\w-]+/g, '')
      ?.replace(/-+/g, '-')
      ?.replace(/^-+|-+$/g, '');
  }

  getTotalMonthlyCost(): string {
    if (this.selectedInstance) {
      return this.getMonthlyPrice(this.selectedInstance?.hourly_rate_usd, this.selectedInstance?.service_provider);
    }
    return '0.00';
  }

  getBestValue(): string {
    if (this.compareInstances.length === 0) return '';
    // Find instance with lowest monthly cost
    let bestInstance = this.compareInstances[0];
    let lowestCost = parseFloat(this.getMonthlyPrice(bestInstance?.document?.hourly_rate_usd, bestInstance?.document?.service_provider));
    this.compareInstances.forEach(instance => {
      const cost = parseFloat(this.getMonthlyPrice(instance?.document?.hourly_rate_usd, instance?.document?.service_provider));
      if (cost < lowestCost) {
        lowestCost = cost;
        bestInstance = instance;
      }
    });
    return bestInstance?.document?.product_name || '';
  }

  formatText(input: string) {
    return input?.replace(/-/g, ' ')?.replace(/\b\w/g, char => char?.toUpperCase());
  }

  backToHome() {
    this.router.navigate(['/skyscanner4cloud']);
  }

  scrollToTopContent() {
    setTimeout(() => {
      if (isPlatformBrowser(this.platformId)) {
        const element = document.getElementById('pageContent');
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }
    }, 100);
  }

}