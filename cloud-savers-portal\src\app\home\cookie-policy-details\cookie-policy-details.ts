import { Component, CUSTOM_ELEMENTS_SCHEMA, HostListener, Inject, PLATFORM_ID } from '@angular/core';
import { Header } from '../header/header';
import { Footer } from '../footer/footer';
import { Router } from '@angular/router';
import { MaterialModule } from '../../material-module';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { MobileFooter } from '../mobile-footer/mobile-footer';

@Component({
  selector: 'app-cookie-policy-details',
  standalone: true,
  imports: [
    Header,
    Footer,
    MaterialModule,
    CommonModule,
    MobileFooter
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './cookie-policy-details.html',
  styleUrl: './cookie-policy-details.scss'
})
export class CookiePolicyDetails {
  mobile: boolean = false;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenWidth();
  }
  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private router: Router) { }

  ngOnInit() {
    this.checkScreenWidth();
    this.scrollToTop();
  }

  private checkScreenWidth() {
    if (isPlatformBrowser(this.platformId)) {
      if (window.innerWidth <= 1250) {
        this.mobile = true;
      } else {
        this.mobile = false;
      }
    }
  }

  private scrollToTop() {
    setTimeout(() => {
      if (isPlatformBrowser(this.platformId)) {
        window.scrollTo(0, 0);
        document.documentElement.scrollTop = 0;
        document.body.scrollTop = 0;
      }
    }, 50);
  }

  backToHome() {
    this.router.navigate(['/']);
  }

}