.footer {
    background-color: #133A4D;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 30px 50px 25px 50px;
    gap: 30px;

    .footer-row {
        display: flex;
        gap: 20px;
        font-size: 16px;
        color: #9CA3AF;

        .footer-link {
            cursor: pointer;
        }

        .footer-contact {
            display: flex;
            gap: 10px;
            align-items: center;

            a {
                color: #9CA3AF;
                text-decoration: none;
            }
        }
    }

    .footer-bottom {
        font-size: 14px;
    }

    // .footer-top {
    //     display: flex;
    //     flex-direction: row;
    //     justify-content: space-between;
    //     gap: 50px;
    //     align-items: flex-start;

    //     .footer-section {
    //         display: flex;
    //         flex-direction: column;
    //         gap: 20px;

    //         .section-title {
    //             font-weight: 600;
    //         }

    //         .section-content {
    //             display: flex;
    //             flex-direction: column;
    //             gap: 10px;
    //         }
    //     }

    //     .apply-box {
    //         border: none;
    //         background-color: #2C2C2C1A;
    //         height: 38px;
    //         width: 230px;
    //         border-radius: 8px 0px 0px 8px;
    //         padding-left: 7px;
    //         outline: none;
    //         text-transform: lowercase;
    //     }

    //     .apply-box::placeholder {
    //         text-transform: none;
    //     }

    //     .apply-btn {
    //         background: #C0E3F4;
    //         border: none;
    //         height: 38px;
    //         width: 144px;
    //         border-radius: 0px 8px 8px 0px;
    //         cursor: pointer;
    //     }
    // }

    // .section-desc {
    //     line-height: 24px;
    //     color: #2C2C2CB2;
    //     letter-spacing: 0px;
    // }

    // .footer-bottom {
    //     display: flex;
    //     flex-direction: row;
    //     justify-content: space-between;
    //     align-items: center;

    //     .bottom-left {
    //         display: flex;
    //         flex-direction: row;
    //         gap: 10px;
    //     }

    //     .bottom-right {
    //         display: flex;
    //         flex-direction: column;
    //         gap: 5px;
    //         font-size: 14px;
    //     }

    //     .section-desc-bottom {
    //         line-height: 20px;
    //         color: #2C2C2CB2;
    //         letter-spacing: 0px;
    //     }
    // }
}