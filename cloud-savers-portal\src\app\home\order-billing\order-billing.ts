import { CommonModule, isPlatformBrowser } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, HostListener, Inject, PLATFORM_ID } from '@angular/core';
import { MaterialModule } from '../../material-module';
import { Header } from '../header/header';
import { SearchPanel } from '../search-panel/search-panel';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { Notification } from '../../services/notification';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Popup } from '../popup/popup';
import { MatDialog } from '@angular/material/dialog';
import { environment } from '../../../environments/environment';
import { CartService } from '../../services/cart-service';
import { colorObj } from '../../shared/color-object';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { PROVIDER_ICON_MAP, PROVIDER_NAME_MAP } from '../../shared/filter-headers.constants';

@Component({
  selector: 'app-order-billing',
  standalone: true,
  imports: [
    CommonModule,
    MaterialModule,
    Header,
    SearchPanel,
    RouterModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './order-billing.html',
  styleUrl: './order-billing.scss'
})
export class OrderBilling {

  loading: boolean = false;
  userInfo: any;
  billingForm: FormGroup;
  public emailPattern = `${environment.EMAIL_PATTERN}`;
  get providerIconMap() {
    return PROVIDER_ICON_MAP;
  }
  get providerNameMap() {
    return PROVIDER_NAME_MAP;
  }
  cartItems = [];
  countryList: any = [];
  filterList: any = [];
  filterCountry: FormControl = new FormControl();
  private _onDestroy = new Subject<void>();
  showBilling: boolean = false;
  promoCode: string = '';
  public monthlyHours = environment.HOURS_IN_A_MONTH;
  mobile: boolean = false;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenWidth();
  }
  specialOffers = [
    {
      provider: 'AWS',
      providerIcon: 'assets/images/aws.png',
      title: 'AWS Startup Credits',
      validUntil: new Date('2025-06-30'),
      description: 'Get up to $5,000 in AWS credits for eligible startups. Perfect for new cloud projects.',
      amount: '$5,000'
    },
    {
      provider: 'Google',
      providerIcon: 'assets/images/gcp.png',
      title: 'GCP First Year Discount',
      validUntil: new Date('2025-07-15'),
      description: 'New customers get 30% off on all Google Cloud services for the first 12 months.',
      amount: '30% OFF'
    },
    {
      provider: 'DigitalOcean',
      providerIcon: 'assets/images/digitalocean.png',
      title: 'DigitalOcean Bundle',
      validUntil: new Date('2025-09-15'),
      description: 'Get $100 in credit over 60 days when you add storage and database to your Droplet.',
      amount: '$100 Credit'
    }
  ];
  cloudSolutions = [
    {
      image: 'assets/images/cloud-infrastructure-provider.png',
      title: 'Cloud Infrastructure',
      subtitle: 'Enterprise-grade infrastructure with 99.99% uptime guarantee',
      price: '$99/mo',
      tag: 'Featured',
      tagClass: 'featured'
    },
    {
      image: 'assets/images/cloud-security-provider.png',
      title: 'Cloud Security',
      subtitle: 'Advanced security features with real-time threat detection',
      price: '$149/mo',
      tag: 'New',
      tagClass: 'new'
    },
    {
      image: 'assets/images/cloud-storage-provider.png',
      title: 'Cloud Storage',
      subtitle: 'Scalable storage solutions with instant access',
      price: '$79/mo',
      tag: 'Popular',
      tagClass: 'popular'
    },
    {
      image: 'assets/images/cloud-analytics-provider.png',
      title: 'Cloud Analytics',
      subtitle: 'Advanced analytics with AI-powered insights',
      price: '$199/mo',
      tag: 'Best Value',
      tagClass: 'best-value'
    }
  ];
  recommendedItems = [
    {
      icon: 'assets/images/aws-lambda-provider.png',
      title: 'AWS Lambda',
      subtitle: 'Serverless Computing',
      description: 'Run code without provisioning or managing servers. Pay only for the compute time you consume.',
      price: '$0.20/million requests'
    },
    {
      icon: 'assets/images/google-cloud-cdn-provider.png',
      title: 'Google Cloud CDN',
      subtitle: 'Content Delivery Network',
      description: 'Accelerate content delivery using Google’s global edge network with simple setup and monitoring.',
      price: '$0.08/GB'
    },
    {
      icon: 'assets/images/azure-kubernetes-service-provider.png',
      title: 'Azure Kubernetes Service',
      subtitle: 'Container Orchestration',
      description: 'Simplify Kubernetes deployment, management, and operations with a fully managed service.',
      price: '$73.00/month'
    }
  ]

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private router: Router,
    private notify: Notification,
    private fb: FormBuilder,
    private dialog: MatDialog,
    public cartService: CartService,
    private route: ActivatedRoute
  ) {
    this.billingForm = this.fb.group({
      fullName: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.pattern(this.emailPattern)]],
      phone: [''],
      company: [''],
      street: [''],
      area: [''],
      city: [''],
      state: [''],
      postalCode: [''],
      country: ['', [Validators.required]]
    });
  }

  ngOnInit() {
    this.checkScreenWidth();
    if (isPlatformBrowser(this.platformId)) {
      this.countryList = window.localStorage.getItem('COUNTRY');
      this.countryList = this.countryList ? JSON.parse(this.countryList) : [];
      this.filterList = this.countryList;
    }
    this.getCart();
  }

  ngAfterViewInit() {
    this.checkScreenWidth();
    this.scrollToCartContent();
    this.filterCountry.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.filterCountryName();
      });
    this.route.fragment.subscribe((fragment: string | null) => {
      if (fragment) {
        setTimeout(() => this.billingContent(fragment), 0);
      }
    });
  }

  private billingContent(fragment: string) {
    if (fragment === 'billingContent') {
      this.setShowBilling(true);
      this.completePurchase();
      if (isPlatformBrowser(this.platformId)) {
        const element = document.getElementById(fragment);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }
    }
  }

  private checkScreenWidth() {
    if (isPlatformBrowser(this.platformId)) {
      if (window.innerWidth <= 1250) {
        this.mobile = true;
      } else {
        this.mobile = false;
      }
    }
  }

  filterCountryName() {
    let search = this.filterCountry.value;
    this.filterList = this.countryList.filter((country: any) =>
      country?.NAME?.toLowerCase().indexOf(search?.toLowerCase()) !== -1);
  }

  getCart() {
    this.loading = true;
    this.cartService.getCartItems().subscribe({
      next: (data) => {
        this.cartItems = data.object;
        this.loading = false;
      },
      error: (e) => {
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        )
        this.loading = false;
      }
    })
  }

  updateItemQuantity(element: any, itemQuantity: number) {
    this.loading = true;
    let obj = {
      "RESOURCE": [{
        "RESOURCE_TYPE": element.RESOURCE_TYPE,
        "RESOURCE_ID": element.RESOURCE_ID,
        "QUANTITY": itemQuantity,
        "SERVICE_DETAILS": element.SERVICE_DETAILS
      }]
    }
    this.cartService.addItemToCart(obj).subscribe({
      next: (resp) => {
        if (resp.status === 200 && resp.object.length > 0) {
          this.getCart();
        }
      },
      error: (e) => {
        this.loading = false;
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        );
      }
    });
  }

  increaseQuantity(element: any) {
    if (element.QUANTITY != null) {
      let newQuantity = element.QUANTITY + 1;
      this.updateItemQuantity(element, newQuantity);
    }
  }

  decreaseQuantity(element: any) {
    if (element.QUANTITY > 1) {
      let newQuantity = element.QUANTITY - 1;
      this.updateItemQuantity(element, newQuantity);
    } else {
      this.removeItemFromCart(element.ID);
    }
  }

  removeItemFromCart(instanceId: string | number) {
    this.loading = true;
    let obj = {
      "ID": [instanceId]
    };
    this.cartService.removeCartItem(obj).subscribe({
      next: (resp) => {
        if (resp && resp.status === 200) {
          this.getCart();
        }
      },
      error: (e) => {
        this.loading = false;
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        );
      }
    });
  }

  viewInstanceDetails(type, instance: any) {
    if (type === 'external') {
      const serviceProvider = this.slugify(instance.service_provider);
      const productName = this.slugify(instance.product_name);
      const slug = serviceProvider + '-' + productName + '--' + instance.record_id;
      this.router.navigate(['/service', slug]);
    }
    else if (type === 'internal') {
      const titleSlug = this.slugify(instance.title);
      this.router.navigate(['/offers', titleSlug]);
    }
  }

  slugify(text: string): string {
    return text
      ?.toString()
      ?.toLowerCase()
      ?.trim()
      ?.replace(/[\/\s]+/g, '-')
      ?.replace(/[^\w-]+/g, '')
      ?.replace(/-+/g, '-')
      ?.replace(/^-+|-+$/g, '');
  }

  get itemCount(): number {
    return this.cartItems.length;
  }

  get HrSubtotal(): number {
    return this.cartItems.reduce((sum, cartItem) => sum + (cartItem?.SERVICE_DETAILS?.hourly_rate_usd * cartItem?.QUANTITY), 0);
  }

  get MnSubtotal(): number {
    return this.cartItems.reduce((sum, cartItem) => sum + ((cartItem?.RESOURCE_TYPE
      === 'external' ? this.getExternalPrice(cartItem) : cartItem?.SERVICE_DETAILS?.selection?.cost) * cartItem?.QUANTITY), 0);
  }

  getExternalPrice(item): number {
    if (item?.SERVICE_DETAILS?.payment_option?.toLowerCase() === 'partial upfront') {
      return (item?.SERVICE_DETAILS?.upfront_amount_usd + item?.SERVICE_DETAILS?.monthly_rate_usd);
    } else if (item?.SERVICE_DETAILS?.payment_option?.toLowerCase() === 'all upfront') {
      return item?.SERVICE_DETAILS?.upfront_amount_usd;
    } else {
      return item?.SERVICE_DETAILS?.monthly_rate_usd;
    }
  }

  get formattedHrSubtotal(): string {
    return this.HrSubtotal?.toFixed(2);
  }

  get formattedMnSubtotal(): string {
    return this.MnSubtotal?.toFixed(2);
  }

  scrollToCartContent() {
    setTimeout(() => {
      if (isPlatformBrowser(this.platformId)) {
        const element = document.getElementById('cartContent');
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }
    }, 100);
  }

  backToInstances() {
    this.router.navigate(['/skyscanner4cloud']);
  }

  applyPromoCode() {
    if (this.promoCode?.trim() === '') {
      return;
    }
    this.promoCode = '';
  }

  patchBillingForm() {
    if (isPlatformBrowser(this.platformId)) {
      const userData = window.localStorage.getItem('userInfo') || '{}';
      this.userInfo = userData ? JSON.parse(userData) : null;
    }
    if (this.userInfo) {
      this.billingForm.patchValue({
        fullName: this.userInfo.firstName + ' ' + this.userInfo.lastName,
        email: this.userInfo.emailId,
        phone: this.userInfo.mobile
      });
    }
  }

  proceedToCheckout() {
    this.setShowBilling(true);
    setTimeout(() => {
      if (isPlatformBrowser(this.platformId)) {
        const element = document.getElementById('billingContent');
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }
    }, 100);
    this.patchBillingForm();
  }

  setShowBilling(value: boolean) {
    this.showBilling = value;
    if (this.showBilling && this.mobile) {
      this.patchBillingForm();
    }
  }

  backToCart() {
    this.showBilling = false;
  }

  completePurchase() {
    if (this.billingForm.valid) {
      this.loading = true;
      let orderedItems = [];
      this.cartItems.forEach((item: any) => {
        let selectedItem = {
          "RESOURCE_TYPE": item?.RESOURCE_TYPE,
          "RESOURCE_ID": item?.RESOURCE_ID,
          "QUANTITY": item?.QUANTITY,
          "SERVICE_DETAILS": item?.SERVICE_DETAILS
        }
        orderedItems.push(selectedItem);
      });
      let obj = {
        "ORDER_DETAILS": {
          "COMPANY_NAME": this.billingForm.value.company,
          "ADDRESS": {
            "STREET_HOUSE": this.billingForm.value.street,
            "AREA": this.billingForm.value.area,
            "CITY": this.billingForm.value.city,
            "STATE": this.billingForm.value.state,
            "PIN_CODE": this.billingForm.value.postalCode,
            "COUNTRY": this.billingForm.value.country
          }
        },
        "ORDER_ITEMS": orderedItems
      }
      this.cartService.confirmOrder(obj).subscribe({
        next: (resp) => {
          if (resp.status === 200 && resp.object) {
            this.loading = false;
            this.successPopup();
          }
        },
        error: (e) => {
          this.loading = false;
          this.notify.showNotification(
            e?.error?.message,
            "top",
            (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
            e?.error?.status
          );
        }
      });
    }
  }

  formatText(input: string) {
    return input?.replace(/-/g, ' ')?.replace(/\b\w/g, char => char?.toUpperCase());
  }

  getMonthlyPrice(hourlyRate: number, provider: string, quantity: number): string {
    const monthlyHours = this.monthlyHours[!!provider ? provider.toLowerCase() : ''];
    return (hourlyRate * monthlyHours * quantity)?.toFixed(2);
  }

  successPopup() {
    const dialogRef = this.dialog.open(Popup, {
      width: '950px',
      height: '450px',
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(result => {
    });
  }

}