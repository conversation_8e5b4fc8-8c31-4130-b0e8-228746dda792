import { CommonModule, isPlatformBrowser } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, EventEmitter, Output, HostListener, Input, Inject, PLATFORM_ID } from '@angular/core';
import { MaterialModule } from '../../../material-module';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { Auth } from '../../../services/auth';
import { Notification } from '../../../services/notification';
import { colorObj } from '../../../shared/color-object';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { SearchStateService } from '../../../services/search-state.service';
import { FILTER_HEADERS } from '../../../shared/filter-headers.constants';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-filters-sidebar',
  imports: [
    CommonModule,
    MaterialModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './filters-sidebar.html',
  styleUrl: './filters-sidebar.scss'
})
export class FiltersSidebar {

  @Input() pricingData: any[] = [];
  private resizeTimeout: any;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    // Debounce resize events to improve performance
    clearTimeout(this.resizeTimeout);
    this.resizeTimeout = setTimeout(() => {
      this.checkScreenWidth();
      // Emit current filters to update parent component
      this.emitFilters();
    }, 0);
  }
  filterForm: FormGroup;
  regions = {};
  filteredRegions = {};
  regionCode: FormControl = new FormControl();
  instanceFamilies = [];
  vCPUList = [];
  RAMList = [];
  hostTypes = [];
  operatingSystems = {};
  minPrice: number;
  maxPrice: number;
  serviceName: any;
  requiredInstances = [];
  selectedvCPU: any;
  selectedRAM: any;
  regionGroup: any;
  operatingSystem: any;
  requiredHostTypes = [];
  @Output() loadingChange = new EventEmitter<boolean>();
  @Output() filtersChanged = new EventEmitter<any>();
  private _onDestroy = new Subject<void>();
  selectedFilters = [];
  mobile: boolean = false;
  searchableFields = ['vcpu_cores', 'ram_gb', 'cs_region', 'cs_city'];
  searchInputs: { [fieldName: string]: string } = {};
  filteredCounts: { [fieldName: string]: any[] } = {};
  // Search state monitoring properties
  currentSearchKeyword: string = '';
  currentSearchFormState: any = null;
  hasActiveKeywordSearch: boolean = false;
  hasActiveFormState: boolean = false;

  get filterHeaders() {
    return FILTER_HEADERS;
  }
  public monthlyHours = environment.HOURS_IN_A_MONTH;

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private fb: FormBuilder,
    private authService: Auth,
    private notify: Notification,
    private searchStateService: SearchStateService
  ) {
    this.filterForm = this.fb.group({
      vCPU: [[]],
      RAM: [[]],
      selectedRegion: [[]],
      selectedOS: [[]],
      minPrice: [],
      maxPrice: []
    })
  }

  ngOnInit(): void {
    this.checkScreenWidth();
    this.initializeSearchStateMonitoring();
    this.getPricingFilters();
    this.initializeFilteredCounts();
    // Subscribe to filter clear requests
    this.searchStateService.clearFiltersRequested$
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.clearFilters();
      });
  }

  initializeSearchStateMonitoring(): void {
    // Monitor search keyword changes
    this.currentSearchKeyword = this.searchStateService.getSearchKeyword();
    this.hasActiveKeywordSearch = !!(this.currentSearchKeyword && this.currentSearchKeyword.trim().length > 0);
    // Monitor search form state changes
    this.currentSearchFormState = this.searchStateService.getSearchFormState();
    this.hasActiveFormState = !!(this.currentSearchFormState &&
      this.currentSearchFormState.compareArray &&
      this.currentSearchFormState.compareArray.length > 0 &&
      this.currentSearchFormState.compareArray.some((item: any) =>
        item.selectedService || item.selectedProduct || item.selectedInstance ||
        item.vCPU || item.RAM || item.selectedRegion || item.selectedCity ||
        item.selectedOS || item.selectedHost
      ));
  }

  initializeFilteredCounts(): void {
    if (this.pricingData && this.pricingData.length > 0) {
      this.pricingData.forEach(provider => {
        provider?.facet_counts?.forEach(instance => {
          if (this.searchableFields?.includes(instance?.field_name)) {
            this.filteredCounts[instance.field_name] = this.sortFilterValues(instance.counts, instance.field_name);
          }
        });
      });
    }
  }

  isSearchableField(fieldName: string): boolean {
    return this.searchableFields?.includes(fieldName);
  }

  getSearchPlaceholder(fieldName: string): string {
    const placeholders = {
      'vcpu_cores': 'Search vCPUs',
      'ram_gb': 'Search RAM',
      'cs_region': 'Search regions',
      'cs_city': 'Search cities'
    };
    return placeholders[fieldName] || 'Search';
  }

  onSearchInput(fieldName: string, searchValue): void {
    this.searchInputs[fieldName] = searchValue.target.value;
    this.filterFieldValues(fieldName);
  }

  filterFieldValues(fieldName: string): void {
    const provider = this.pricingData[0]; // Assuming single provider for now
    const instance = provider?.facet_counts?.find(inst => inst.field_name === fieldName);
    if (!instance) return;
    const searchTerm = !!this.searchInputs[fieldName] ? this.searchInputs[fieldName]?.toLowerCase() : '';
    if (!searchTerm) {
      this.filteredCounts[fieldName] = this.sortFilterValues(instance.counts, fieldName);
    } else {
      const filtered = instance.counts.filter(filter =>
        this.formatText(filter.value, fieldName)?.toLowerCase()?.includes(searchTerm)
      );
      this.filteredCounts[fieldName] = this.sortFilterValues(filtered, fieldName);
    }
  }

  sortFilterValues(counts: any[], fieldName: string): any[] {
    if (!counts) return [];
    // Sort numerically for vCPU and RAM fields
    if (fieldName === 'vcpu_cores' || fieldName === 'ram_gb') {
      return [...counts].sort((a, b) => {
        const numA = parseFloat(a.value) || 0;
        const numB = parseFloat(b.value) || 0;
        return numA - numB;
      });
    }
    // Default alphabetical sort for other fields
    return [...counts].sort((a, b) =>
      this.formatText(a.value, fieldName).localeCompare(this.formatText(b.value, fieldName))
    );
  }

  getFilteredCounts(fieldName: string, originalCounts: any[]): any[] {
    if (this.searchableFields?.includes(fieldName)) {
      return this.filteredCounts[fieldName] || this.sortFilterValues(originalCounts, fieldName);
    }
    return originalCounts;
  }

  clearSearch(fieldName: string): void {
    this.searchInputs[fieldName] = '';
    this.filterFieldValues(fieldName);
  }

  private checkScreenWidth() {
    if (isPlatformBrowser(this.platformId)) {
      if (window.innerWidth < 1250) {
        this.mobile = true;
      } else {
        this.mobile = false;
      }
    }
  }

  ngAfterViewInit() {
    this.checkScreenWidth();
    this.regionCode.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.filterRegion();
      });
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  getPricingFilters(customParams?: any) {
    this.loadingChange.emit(true);
    // Determine filter parameters based on search state
    let obj = this.buildFilterParams(customParams);
    // Call filter API to get updated filter options (facet_counts) based on current selections
    this.authService.getFilters(obj).subscribe({
      next: (data) => {
        this.pricingData = data.object?.results || [];
        this.initializeFilteredCounts();
        this.loadingChange.emit(false);
      },
      error: (e) => {
        this.loadingChange.emit(false);
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        )
      }
    })
  }

  buildFilterParams(customParams?: any): any {
    // If custom parameters are provided (e.g., from checkbox selections), use them
    if (customParams) {
      return customParams;
    }
    // Update search state before building params
    this.initializeSearchStateMonitoring();
    // Always prioritize form array parameters when available
    if (this.hasActiveFormState) {
      const formParams = this.extractFormStateParams();
      // If searchKeyword has a value, preserve current filter behavior but still use form params
      if (this.hasActiveKeywordSearch) {
        // Keep the form parameters as the base for filtering
        return formParams;
      }
      // If no keyword search, use form parameters
      return formParams;
    }
    // If searchKeyword has a value but no form state, use empty params (original behavior)
    if (this.hasActiveKeywordSearch) {
      return {};
    }
    // Default case - empty params
    return {};
  }

  extractFormStateParams(): any {
    if (!this.currentSearchFormState || !this.currentSearchFormState.compareArray) {
      return {};
    }

    const params: any = {};
    const formArray = this.currentSearchFormState.compareArray;

    // Initialize arrays for collecting values from all form items
    const serviceTypes: string[] = [];
    const productNames: string[] = [];
    const instanceFamilies: string[] = [];
    const vcpus: any[] = [];
    const rams: any[] = [];
    const regionGroups: string[] = [];
    const regionCodes: string[] = [];
    const operatingSystems: string[] = [];
    const hostTypes: string[] = [];

    // Extract values from all form items and combine them
    formArray.forEach((item: any) => {
      if (item.selectedService) {
        const services = Array.isArray(item.selectedService) ? item.selectedService : [item.selectedService];
        serviceTypes.push(...services.filter((s: string) => s && !serviceTypes.includes(s)));
      }

      if (item.selectedProduct) {
        const products = Array.isArray(item.selectedProduct) ? item.selectedProduct : [item.selectedProduct];
        productNames.push(...products.filter((p: string) => p && !productNames.includes(p)));
      }

      if (item.selectedInstance) {
        const instances = Array.isArray(item.selectedInstance) ? item.selectedInstance : [item.selectedInstance];
        instanceFamilies.push(...instances.filter((i: string) => i && !instanceFamilies.includes(i)));
      }

      if (item.vCPU) {
        const cpus = Array.isArray(item.vCPU) ? item.vCPU : [item.vCPU];
        vcpus.push(...cpus.filter((c: any) => c !== null && c !== undefined && !vcpus.includes(c)));
      }

      if (item.RAM) {
        const memories = Array.isArray(item.RAM) ? item.RAM : [item.RAM];
        rams.push(...memories.filter((r: any) => r !== null && r !== undefined && !rams.includes(r)));
      }

      if (item.selectedRegion) {
        const regions = Array.isArray(item.selectedRegion) ? item.selectedRegion : [item.selectedRegion];
        regionGroups.push(...regions.filter((r: string) => r && !regionGroups.includes(r)));
      }

      if (item.selectedCity) {
        const cities = Array.isArray(item.selectedCity) ? item.selectedCity : [item.selectedCity];
        regionCodes.push(...cities.filter((c: string) => c && !regionCodes.includes(c)));
      }

      if (item.selectedOS) {
        const oses = Array.isArray(item.selectedOS) ? item.selectedOS : [item.selectedOS];
        operatingSystems.push(...oses.filter((os: string) => os && !operatingSystems.includes(os)));
      }

      if (item.selectedHost) {
        const hosts = Array.isArray(item.selectedHost) ? item.selectedHost : [item.selectedHost];
        hostTypes.push(...hosts.filter((h: string) => h && !hostTypes.includes(h)));
      }
    });

    // Build the final parameters object with collected values
    if (serviceTypes.length > 0) {
      params.SERVICE_TYPE = serviceTypes;
    }

    if (productNames.length > 0) {
      params.PRODUCT_NAME = productNames;
    }

    if (instanceFamilies.length > 0) {
      params.INSTANCE_FAMILY = instanceFamilies;
    }

    if (vcpus.length > 0) {
      params.VCPU = vcpus;
    }

    if (rams.length > 0) {
      params.RAM = rams;
    }

    if (regionGroups.length > 0) {
      params.REGION_GROUP = regionGroups;
    }

    if (regionCodes.length > 0) {
      params.REGION_CODE = regionCodes;
    }

    if (operatingSystems.length > 0) {
      params.OPERATING_SYSTEM = operatingSystems;
    }

    if (hostTypes.length > 0) {
      params.HOST_TYPE = hostTypes;
    }

    return params;
  }

  filterRegion() {
    const search = !!this.regionCode.value ? this.regionCode.value.toLowerCase() : '';
    if (!search) {
      this.filteredRegions = { ...this.regions };
      return;
    }
    const filtered = {};
    Object.keys(this.regions).forEach(groupKey => {
      // If group key matches, include the whole group
      if (groupKey?.toLowerCase()?.includes(search)) {
        filtered[groupKey] = this.regions[groupKey];
        return;
      }
      // Otherwise, filter regions inside the group
      const matchedRegions = (this.regions[groupKey] as any[]).filter(region =>
        (region.CS_CITY && region.CS_CITY?.toLowerCase()?.includes(search)) ||
        (region.CS_CLOUD_SAVER_CODE && region.CS_CLOUD_SAVER_CODE?.toLowerCase()?.includes(search))
      );
      if (matchedRegions.length) {
        filtered[groupKey] = matchedRegions;
      }
    });
    this.filteredRegions = filtered;
  }

  changeFilter(event: any, fieldName: string, value: any) {
    if (event.checked) {
      // Add filter logic
      const existingFilterIndex = this.selectedFilters.findIndex(filter =>
        filter.hasOwnProperty(fieldName)
      );
      if (existingFilterIndex !== -1) {
        // Field exists, add value to existing array (avoid duplicates)
        const existingValues = this.selectedFilters[existingFilterIndex][fieldName];
        if (!existingValues?.includes(value)) {
          existingValues.push(value);
        }
      }
      else {
        // Field doesn't exist, create new filter object
        const newFilter = {
          [fieldName]: [value]
        };
        this.selectedFilters.push(newFilter);
      }
    }
    else {
      // Remove filter logic
      const existingFilterIndex = this.selectedFilters.findIndex(filter =>
        filter.hasOwnProperty(fieldName)
      );
      if (existingFilterIndex !== -1) {
        const existingValues = this.selectedFilters[existingFilterIndex][fieldName];
        const valueIndex = existingValues.indexOf(value);
        if (valueIndex !== -1) {
          // Remove the specific value
          existingValues.splice(valueIndex, 1);
          // If array becomes empty, remove the entire filter object
          if (existingValues.length === 0) {
            this.selectedFilters.splice(existingFilterIndex, 1);
          }
        }
      }
    }
    // Emit filters for parent component
    // this.emitFilters();
    // Call filter API to update available filter options dynamically
    this.handleDynamicFiltering();
  }

  handleDynamicFiltering(): void {
    // Update search state
    this.initializeSearchStateMonitoring();
    // Perform dynamic filtering if:
    // 1. Has active form state (always use form array parameters when available) OR
    // 2. Has selected filters (user has made filter selections)
    if (this.hasActiveFormState || this.selectedFilters.length > 0) {
      const filterParams = this.buildDynamicFilterParams();
      this.getPricingFilters(filterParams);
    }
  }

  buildDynamicFilterParams(): any {
    const params: any = {};

    // Start with form state parameters if available
    if (this.hasActiveFormState) {
      const formParams = this.extractFormStateParams();
      Object.assign(params, formParams);
    }

    // Add or merge selected filter values with the parameters
    this.selectedFilters.forEach(filterObj => {
      Object.keys(filterObj).forEach(fieldName => {
        const values = filterObj[fieldName];
        if (values && values.length > 0) {
          // Map field names to API parameter names
          const apiFieldName = this.mapFieldNameToApiParam(fieldName);
          if (apiFieldName) {
            // If the parameter already exists from form state, merge the values
            if (params[apiFieldName]) {
              // Combine existing values with new filter values, avoiding duplicates
              const existingValues = Array.isArray(params[apiFieldName]) ? params[apiFieldName] : [params[apiFieldName]];
              const newValues = Array.isArray(values) ? values : [values];
              const combinedValues = [...existingValues];

              newValues.forEach(newValue => {
                if (!combinedValues.includes(newValue)) {
                  combinedValues.push(newValue);
                }
              });

              params[apiFieldName] = combinedValues;
            } else {
              // If parameter doesn't exist, add it
              params[apiFieldName] = Array.isArray(values) ? values : [values];
            }
          }
        }
      });
    });

    return params;
  }

  mapFieldNameToApiParam(fieldName: string): string | null {
    const fieldMapping: { [key: string]: string } = {
      'vcpu_cores': 'VCPU',
      'ram_gb': 'RAM',
      'cs_region': 'REGION_GROUP',
      'cs_city': 'REGION_CODE',
      'service_type': 'SERVICE_TYPE',
      'product_name': 'PRODUCT_NAME',
      'instance_family': 'INSTANCE_FAMILY',
      'operating_system': 'OPERATING_SYSTEM',
      'host_type': 'HOST_TYPE'
    };
    return fieldMapping[fieldName] || null;
  }

  // Method to check if a specific filter value is selected
  isFilterSelected(fieldName: string, value: any): boolean {
    const filterObj = this.selectedFilters.find(filter => filter.hasOwnProperty(fieldName));
    return filterObj ? filterObj[fieldName]?.includes(value) : false;
  }

  // async fetchPricingData(formArray: any[], selectedIndex?: number) {
  //   this.loadingChange.emit(true);
  //   try {
  //     let item = (typeof selectedIndex === 'number' && formArray[selectedIndex] !== undefined)
  //       ? formArray[selectedIndex]
  //       : formArray[0];
  //     if (!item) return;
  //     this.serviceName = item?.selectedService;
  //     this.requiredInstances = item?.selectedInstance ? (Array.isArray(item.selectedInstance) ? item.selectedInstance : [item.selectedInstance]) : [];
  //     this.selectedvCPU = item?.vCPU ? (Array.isArray(item.vCPU) ? item.vCPU : [item.vCPU]) : [];
  //     this.selectedRAM = item?.RAM ? (Array.isArray(item.RAM) ? item.RAM : [item.RAM]) : [];
  //     this.regionGroup = item?.selectedRegion;
  //     this.operatingSystem = item?.selectedOS ? (Array.isArray(item.selectedOS) ? item.selectedOS : [item.selectedOS]) : [];
  //     this.requiredHostTypes = item?.selectedHost ? (Array.isArray(item.selectedHost) ? item.selectedHost : [item.selectedHost]) : [];
  //     await this.getCategoryInstances();
  //     this.filterForm.patchValue({
  //       vCPU: item.vCPU ? (Array.isArray(item.vCPU) ? item.vCPU : [item.vCPU]) : [],
  //       RAM: item.RAM ? (Array.isArray(item.RAM) ? item.RAM : [item.RAM]) : [],
  //       selectedRegion: [],
  //       selectedOS: item.selectedOS ? (Array.isArray(item.selectedOS) ? item.selectedOS : [item.selectedOS]) : []
  //     }, { emitEvent: false });
  //   }
  //   finally {
  //     this.loadingChange.emit(false);
  //   }
  // }

  // async fetchPricingData(resp: any) {
  //   this.loadingChange.emit(true);
  //   try {
  //     this.pricingData = resp?.results || [];
  //   }
  //   finally {
  //     this.loadingChange.emit(false);
  //   }
  // }

  formatTitle(input: string) {
    let title = input?.replace(/_/g, ' ')?.toUpperCase();
    return title;
  }

  formatText(input: string, fieldName: string) {
    if (fieldName === 'service_provider' || fieldName === 'service_type') {
      return input?.toUpperCase();
    }
    else {
      return input?.replace(/-/g, ' ')?.replace(/\b\w/g, char => char?.toUpperCase());
    }
  }

  getCategoryInstances(): Promise<any> {
    return new Promise((resolve, reject) => {
      this.authService.getProductCategoryInstances(this.serviceName).subscribe({
        next: async (data) => {
          this.instanceFamilies = data.object;
          resolve(data);
          await this.getvCPUs();
        },
        error: (e) => {
          this.notify.showNotification(
            e?.error?.message,
            "top",
            (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
            e?.error?.status
          )
          reject(e);
        }
      })
    })
  }

  getvCPUs(): Promise<any> {
    return new Promise((resolve, reject) => {
      this.authService.getvCPU(this.serviceName, this.requiredInstances).subscribe({
        next: async (data) => {
          this.vCPUList = data.object;
          resolve(data);
          if (this.selectedvCPU) {
            await this.getRAMs();
          }
        },
        error: (e) => {
          this.notify.showNotification(
            e?.error?.message,
            "top",
            (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
            e?.error?.status
          )
          reject(e);
        }
      })
    })
  }

  getRAMs(): Promise<any> {
    return new Promise((resolve, reject) => {
      this.authService.getRAM(this.serviceName, this.requiredInstances, this.selectedvCPU).subscribe({
        next: async (data) => {
          this.RAMList = data.object;
          resolve(data);
          if (this.selectedRAM) {
            await this.getRegion();
          }
        },
        error: (e) => {
          this.notify.showNotification(
            e?.error?.message,
            "top",
            (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
            e?.error?.status
          )
          reject(e);
        }
      })
    })
  }

  getRegion(): Promise<any> {
    return new Promise((resolve, reject) => {
      this.authService.getRegions(this.serviceName, this.requiredInstances, this.selectedvCPU, this.selectedRAM).subscribe({
        next: async (data) => {
          this.regions = data.object;
          this.filteredRegions = this.regions;
          let selectedRegionCodes: string[] = [];
          if (this.regionGroup && this.regions) {
            const selectedRegionKeys = Array.isArray(this.regionGroup)
              ? this.regionGroup.map(String)
              : [String(this.regionGroup)];
            Object.keys(this.regions).forEach(regionKey => {
              if (selectedRegionKeys?.includes(regionKey)) {
                const group = this.regions[regionKey] as any[];
                group.forEach(region => {
                  if (region.CS_CLOUD_SAVER_CODE) {
                    selectedRegionCodes.push(region.CS_CLOUD_SAVER_CODE);
                  }
                });
              }
            });
          }
          this.filterForm.patchValue({
            selectedRegion: selectedRegionCodes
          }, { emitEvent: false });
          resolve(data);
          if (this.regionGroup) {
            await this.getOS();
          }
        },
        error: (e) => {
          this.notify.showNotification(
            e?.error?.message,
            "top",
            (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
            e?.error?.status
          )
          reject(e);
        }
      })
    })
  }

  getSelectedRegionNames(): string {
    const codes: string[] = this.filterForm.get('selectedRegion')?.value || [];
    if (!codes.length || !this.regions) return '';
    const names: string[] = [];
    for (const group of Object.values(this.regions)) {
      for (const region of group as any[]) {
        if (codes?.includes(region.CS_CLOUD_SAVER_CODE)) {
          names.push(region.CS_CITY);
        }
      }
    }
    return names.join(', ');
  }

  getOS(): Promise<any> {
    return new Promise((resolve, reject) => {
      const hasRegionCode = this.filterForm.get('selectedRegion')?.value && this.filterForm.get('selectedRegion')?.value.length > 0;
      let obj = {
        "SERVICE_NAME": this.serviceName,
        "INSTANCE_FAMILY": Array.isArray(this.requiredInstances) ? this.requiredInstances : [this.requiredInstances],
        "VCPU": Array.isArray(this.selectedvCPU) ? this.selectedvCPU : [this.selectedvCPU],
        "RAM": Array.isArray(this.selectedRAM) ? this.selectedRAM : [this.selectedRAM],
      }
      if (hasRegionCode) {
        obj["REGION_CODE"] = this.filterForm.get('selectedRegion')?.value;
      } else if (this.regionGroup) {
        obj["REGION_GROUP"] = Array.isArray(this.regionGroup) ? this.regionGroup : [this.regionGroup];
      } else {
        obj["REGION_GROUP"] = [];
      }
      this.authService.getOperatingSystems(obj).subscribe({
        next: async (data) => {
          this.operatingSystems = data.object;
          resolve(data);
          if (this.operatingSystem) {
            await this.getHost();
          }
        },
        error: (e) => {
          this.notify.showNotification(
            e?.error?.message,
            "top",
            (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
            e?.error?.status
          )
          reject(e);
        }
      })
    })
  }

  getHost(): Promise<any> {
    const hasRegionCode = this.filterForm.get('selectedRegion')?.value && this.filterForm.get('selectedRegion')?.value.length > 0;
    return new Promise((resolve, reject) => {
      let obj = {
        "SERVICE_NAME": this.serviceName,
        "INSTANCE_FAMILY": Array.isArray(this.requiredInstances) ? this.requiredInstances : [this.requiredInstances],
        "VCPU": Array.isArray(this.selectedvCPU) ? this.selectedvCPU : [this.selectedvCPU],
        "RAM": Array.isArray(this.selectedRAM) ? this.selectedRAM : [this.selectedRAM],
        "OPERATING_SYSTEM": Array.isArray(this.operatingSystem) ? this.operatingSystem : [this.operatingSystem]
      }
      if (hasRegionCode) {
        obj["REGION_CODE"] = this.filterForm.get('selectedRegion')?.value;
      } else if (this.regionGroup) {
        obj["REGION_GROUP"] = Array.isArray(this.regionGroup) ? this.regionGroup : [this.regionGroup];
      } else {
        obj["REGION_GROUP"] = [];
      }
      this.authService.getHosts(obj).subscribe({
        next: (data) => {
          this.hostTypes = data.object;
          resolve(data);
        },
        error: (e) => {
          this.notify.showNotification(
            e?.error?.message,
            "top",
            (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
            e?.error?.status
          )
          reject(e);
        }
      })
    })
  }

  // Validate price inputs when they lose focus
  // validatePriceInputs(): void {
  //   let minPrice = parseFloat(this.filterForm.get('minPrice')?.value);
  //   let maxPrice = parseFloat(this.filterForm.get('maxPrice')?.value);
  //   if (minPrice < 0.01) {
  //     minPrice = 0.01;
  //     this.filterForm.get('minPrice')?.setValue(minPrice?.toFixed(2));
  //   }
  //   if (minPrice < maxPrice) {
  //     this.filterForm.get('minPrice')?.setValue(minPrice?.toFixed(2));
  //     this.filterForm.get('maxPrice')?.setValue(maxPrice?.toFixed(2));
  //   }
  //   if (maxPrice < minPrice) {
  //     maxPrice = minPrice;
  //     this.filterForm.get('minPrice')?.setValue(minPrice?.toFixed(2));
  //     this.filterForm.get('maxPrice')?.setValue(maxPrice?.toFixed(2));
  //   }
  // }

  applyPriceFilter(): void {
    // this.validatePriceInputs();
    this.emitFilters();
  }

  // selectInstanceTypes(event, name) {
  //   let requiredIdIndex = this.requiredInstances.findIndex(x => x == name);
  //   if (requiredIdIndex == -1 && event.checked == true) {
  //     this.requiredInstances.push(name);
  //   } else if (requiredIdIndex != -1 && event.checked == false) {
  //     this.requiredInstances.splice(requiredIdIndex, 1);
  //   }
  //   this.vCPUList = [];
  //   this.RAMList = [];
  //   this.regions = {};
  //   this.filteredRegions = {};
  //   this.operatingSystems = {};
  //   this.hostTypes = [];
  //   if (this.requiredInstances.length == 0) {
  //     this.filterForm.patchValue({
  //       vCPU: [],
  //       RAM: [],
  //       selectedRegion: [],
  //       selectedOS: []
  //     });
  //     this.regionCode.reset();
  //     this.selectedvCPU = [];
  //     this.selectedRAM = [];
  //     this.operatingSystem = [];
  //     this.requiredHostTypes = [];
  //   }
  //   else {
  //     this.getvCPUs();
  //     if (this.selectedvCPU.length > 0 && this.selectedRAM.length > 0) {
  //       this.emitFilters(); // Emit after change
  //     }
  //   }
  // }

  // selectvCPU(event) {
  //   this.selectedvCPU = event;
  //   this.RAMList = [];
  //   this.regions = {};
  //   this.filteredRegions = {};
  //   this.operatingSystems = {};
  //   this.hostTypes = [];
  //   if (this.selectedvCPU.length == 0) {
  //     this.filterForm.patchValue({
  //       RAM: [],
  //       selectedRegion: [],
  //       selectedOS: []
  //     });
  //     this.regionCode.reset();
  //     this.selectedRAM = null;
  //     this.operatingSystem = null;
  //     this.requiredHostTypes = [];
  //   }
  //   else {
  //     this.getRAMs();
  //     if (this.selectedRAM.length > 0) {
  //       this.emitFilters();
  //     }
  //   }
  // }

  // selectRAM(event) {
  //   this.selectedRAM = event;
  //   this.regions = {};
  //   this.filteredRegions = {};
  //   this.operatingSystems = {};
  //   this.hostTypes = [];
  //   if (this.selectedRAM.length == 0) {
  //     this.filterForm.patchValue({
  //       selectedRegion: [],
  //       selectedOS: []
  //     });
  //     this.regionCode.reset();
  //     this.operatingSystem = null;
  //     this.requiredHostTypes = [];
  //   }
  //   else {
  //     this.getRegion();
  //     this.emitFilters();
  //   }
  // }

  // selectRegions(event) {
  //   this.operatingSystems = {};
  //   this.hostTypes = [];
  //   if (event.length == 0) {
  //     this.filterForm.patchValue({
  //       selectedOS: []
  //     });
  //     this.operatingSystem = null;
  //     this.requiredHostTypes = [];
  //   }
  //   else {
  //     this.getOS();
  //   }
  //   this.emitFilters();
  // }

  // selectOperatingSystems(event) {
  //   this.operatingSystem = event;
  //   this.hostTypes = [];
  //   if (this.operatingSystem.length == 0) {
  //     this.requiredHostTypes = [];
  //   }
  //   else {
  //     this.getHost();
  //   }
  //   this.emitFilters();
  // }

  // selectHostTypes(event, name) {
  //   let requiredIdIndex = this.requiredHostTypes.findIndex(x => x == name);
  //   if (requiredIdIndex == -1 && event.checked == true) {
  //     this.requiredHostTypes.push(name);
  //   } else if (requiredIdIndex != -1 && event.checked == false) {
  //     this.requiredHostTypes.splice(requiredIdIndex, 1);
  //   }
  //   this.emitFilters();
  // }

  emitFilters() {
    setTimeout(() => {
      const filters = {
        // minPrice: this.filterForm.get('minPrice')?.value / this.monthlyHours.avg,
        // maxPrice: this.filterForm.get('maxPrice')?.value / this.monthlyHours.avg,
        selectedFilters: [...this.selectedFilters]
        // selectedInstance: [...this.requiredInstances],
        // vCPU: this.filterForm.get('vCPU')?.value,
        // RAM: this.filterForm.get('RAM')?.value,
        // selectedRegion: this.filterForm.get('selectedRegion')?.value,
        // selectedOS: this.filterForm.get('selectedOS')?.value,
        // selectedHost: [...this.requiredHostTypes]
      };
      this.filtersChanged.emit(filters);
    }, 100);
  }

  clearFilters() {
    this.filterForm.reset();
    this.selectedFilters = [];
    this.searchInputs = {};
    this.initializeFilteredCounts();
    this.emitFilters();
  }

}