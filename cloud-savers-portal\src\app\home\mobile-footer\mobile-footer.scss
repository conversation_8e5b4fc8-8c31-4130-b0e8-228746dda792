.mobile-bottom-nav {
  display: flex;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 8px 0;
  height: 60px;
  justify-content: space-around;
  align-items: center;

  .nav-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #4B5563;
    text-transform: none;
    font-size: 12px;
    min-width: 80px;
    height: 100%;
    padding: 8px 0;
    position: relative;
    background: none;
    border: none;
    cursor: pointer;

    .button-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 2px;
      width: 100%;
      height: 100%;
    }

    mat-icon {
      margin-bottom: 2px;
      font-size: 20px;
      width: 20px;
      height: 20px;
    }

    &.cart-button {
      position: relative;
    }

    .cart-badge {
      position: absolute;
      top: -8px;
      right: -8px;
      background: #EF4444;
      color: white;
      border-radius: 50%;
      width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      font-weight: 500;
      line-height: 1;
    }

    &[disabled] {
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        background-color: transparent;
      }
    }
  }

  .active {
    color: #3B82F6;

    mat-icon {
      color: #3B82F6;
    }
  }
}

// Cart Side Panel
.cart-side-panel {
  position: fixed;
  top: 0;
  right: -100%;
  width: 90%;
  max-width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1100;
  transition: right 0.3s ease-in-out;
  display: flex;
  flex-direction: column;

  &.show {
    right: 0;
  }

  .cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #E5E7EB;
    background-color: #F9FAFB;

    h2 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 500;
      color: #111827;
    }

    .close-button {
      color: #6B7280;
    }
  }

  .cart-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;

    .loading-spinner {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgba(255, 255, 255, 0.8);
      z-index: 10;

      ::ng-deep .mat-progress-spinner circle,
      .mat-spinner circle {
        stroke: #3f51b5;
      }
    }
  }
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1099;
}

::ng-deep app-cart {
  .cart-container {
    box-shadow: none;
    border-radius: 0;
    margin: 0;
    padding: 0;
    max-height: none;
    height: 100%;
  }
}

// In mobile-footer.scss
// .filter-side-panel {
//   position: fixed;
//   top: 0;
//   right: -100%;
//   width: 100%;
//   max-width: 400px;
//   height: 100vh;
//   background: white;
//   box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
//   transition: right 0.3s ease-in-out;
//   z-index: 1001;
//   display: flex;
//   flex-direction: column;

//   &.show {
//     right: 0;
//   }

//   .filter-header {
//     padding: 1rem;
//     border-bottom: 1px solid #eee;
//     display: flex;
//     justify-content: space-between;
//     align-items: center;
//     position: sticky;
//     top: 0;
//     background: white;
//     z-index: 1;
//   }

//   .filter-content {
//     flex: 1;
//     overflow-y: auto;
//     padding: 1rem;
//   }
// }

// Cart loader styles
.cart-loader {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 18px;
  height: 18px;

  ::ng-deep {
    circle {
      stroke: #EF4444;
    }

    .mat-mdc-progress-spinner {
      width: 18px !important;
      height: 18px !important;

      circle {
        stroke-width: 4px !important;
      }
    }
  }
}