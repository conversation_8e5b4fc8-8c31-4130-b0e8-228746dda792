@import "@angular/material/prebuilt-themes/indigo-pink.css";

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html,
body {
    height: 100%;
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
}

body {
    background: #F9FAFB;
    color: #2C2C2C;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

button {
    cursor: pointer;
    border: none;
    outline: none;
    letter-spacing: 0px !important;
}

p {
    margin: 0;
}

* {
    font-family: 'Roboto';
}

// @font-face {
//     font-family: "Inter";
//     src: local("Inter"), url(./assets/font/Inter-Regular.ttf) format("truetype");
// }

.mat-form-field-appearance-outline .mat-form-field-outline {
    color: #828282 !important;
}

.mat-mdc-checkbox .mdc-checkbox .mdc-checkbox__native-control:checked~.mdc-checkbox__background {
    background-color: #35A5DB !important;
    border-color: #35A5DB !important;
}

.mat-mdc-checkbox .mdc-checkbox .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background {
    background-color: #35A5DB !important;
    border-color: #35A5DB !important;
}

.mat-mdc-form-field-icon-suffix .mat-mdc-form-field-icon-prefix {
    padding: 0 0 10px 4px;
}

.codeClass {
    min-width: 250px !important;
}

/* country code dropdown */
.mat-mdc-select-panel {
    .options {
        width: 100%;
        display: grid;
        grid-template-columns: auto 1fr;
        align-items: center;
        padding: 0 12px;
        box-sizing: border-box;

        .c-code {
            color: rgba(130, 130, 130, 1);
            font-size: 12px;
            white-space: nowrap;
            font-weight: 500;
            margin-right: 10px;
            width: 50px;
        }

        .c-name {
            color: rgba(53, 95, 115, 1);
            font-size: 12px;
            white-space: nowrap;
            text-align: left;
            padding-left: 16px;
        }
    }

    .close-icon {
        font-size: 12px;
        --mat-mdc-button-persistent-ripple-color: #F5F5F5 !important;
    }
}

.mat-mdc-optgroup {
    .region-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 10px;

        .r-name {
            width: 55%;
            text-align: left;
            font-size: 15px;
        }

        .r-code {
            text-align: right;
            font-size: 14px;
        }

        .os-name {
            text-align: left;
            font-size: 15px;
        }
    }
}

.mat-mdc-option .mdc-list-item__primary-text {
    width: 100%;
}

.mat-mdc-snack-bar-container.success {
    --mdc-snackbar-container-color: #4AB543 !important;
    --mdc-snackbar-supporting-text-color: #fff !important;

    .mdc-snackbar__surface {
        background-color: #4AB543 !important;
    }

    .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) {
        color: #fff !important;
    }
}

.mat-mdc-snack-bar-container.error {
    --mdc-snackbar-container-color: #F44336 !important;
    --mdc-snackbar-supporting-text-color: #fff !important;

    .mdc-snackbar__surface {
        background-color: #F44336 !important;
    }

    .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) {
        color: #fff !important;
    }
}

.mat-mdc-snack-bar-container.warning {
    --mdc-snackbar-container-color: #EF984B !important;
    --mdc-snackbar-supporting-text-color: #fff !important;

    .mdc-snackbar__surface {
        background-color: #EF984B !important;
    }

    .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) {
        color: #fff !important;
    }
}

.mat-mdc-tab.mdc-tab {
    flex-grow: 0 !important;
}

.custom-overlay {
    position: fixed;
    z-index: 9999;
    inset: 0;
    background: rgba(173, 167, 167, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-overlay__inner {
    text-align: center;
}

.custom-loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #FFFFFF;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgb(0 0 0 / 0.07);
}

.progress-circle {
    width: 60px;
    height: 60px;
    margin-bottom: 20px;
}

.progress-bg {
    fill: none;
    stroke: #e6e6e6;
    stroke-width: 4;
}

.progress-bar {
    fill: none;
    stroke: #35A5DB;
    stroke-width: 4;
    stroke-linecap: round;
    stroke-dasharray: 126;
    stroke-dashoffset: 100;
    animation: spin 1.2s linear infinite;
    transform-origin: 50% 50%;
}

@keyframes spin {
    0% {
        stroke-dashoffset: 126;
    }

    100% {
        stroke-dashoffset: 0;
    }
}

.loader-title {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 12px;
    color: #000000;
}

.loader-desc {
    font-size: 15px;
    color: #374151;
}

.mat-select-search-input {
    width: 80% !important;
    border: 1px solid #DCD4D4 !important;
    margin: 24px;
    background-color: #F5F5F5 !important;
    border-radius: 4px !important;
    height: 37px !important;
    font-size: 12px !important;
}

.mat-select-search-clear {
    position: absolute !important;
    right: 13px !important;
    top: 22px !important;
}

.mat-select-search-no-entries-found {
    font-size: 12px !important;
}

.mat-select-search-no-entries-found .mat-mdc-option mat-icon {
    font-size: 14px !important;
}

.mat-paginator-container {
    .mat-paginator-range-label {
        display: none !important;
    }
}

.mat-paginator {
    background: none !important;
}

.GTM-Script {
    display:none;
    visibility:hidden
}

.storyblok-content * {
  all: revert; /* attempt reset for all children */
}

.storyblok-content p {
  margin: 1em 0;
}

.storyblok-content h1,
.storyblok-content h2,
.storyblok-content h3,
.storyblok-content h4,
.storyblok-content h5,
.storyblok-content h6 {
  margin: 1em 0 0.5em;
  font-weight: bold;
}

.storyblok-content ul,
.storyblok-content ol {
  margin: 1em 0;
  padding-left: 2em;
}