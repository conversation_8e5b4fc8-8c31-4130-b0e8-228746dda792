import { isPlatformBrowser } from '@angular/common';
import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private router: Router) { }

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    if (isPlatformBrowser(this.platformId)) {
      const token = window.localStorage.getItem('access_token');
      if (token) {
        // logged in so return true
        return true;
      }
      // not logged in so redirect to login page with the return url
      localStorage.clear();
    }
    this.router.navigate([''], { queryParams: { returnUrl: state.url } });
    return false;
  }

}