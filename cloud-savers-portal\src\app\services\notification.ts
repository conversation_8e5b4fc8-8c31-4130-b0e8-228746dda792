import { isPlatformBrowser } from '@angular/common';
import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class Notification {

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private snackBar: MatSnackBar,
    private router: Router
  ) { }

  private addNotificationStyles(className: string, type: 'success' | 'error' | 'warning') {
    const colors = {
      success: '#4AB543',
      error: '#F44336',
      warning: '#EF984B'
    };

    if (isPlatformBrowser(this.platformId)) {
      const style = document.createElement('style');
      style.type = 'text/css';
      style.innerHTML = `
        .${className} {
          --mdc-snackbar-container-color: ${colors[type]} !important;
          --mdc-snackbar-supporting-text-color: #fff !important;
        }
        .${className} .mdc-snackbar__surface {
          background-color: ${colors[type]} !important;
        }
        .${className} .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) {
          color: #fff !important;
        }
      `;
      document.head.appendChild(style);
    }
  }

  showNotification(
    message: string,
    verticalPosition: 'top' | 'bottom' = 'top',
    panelClass: 'success' | 'error' | 'warning' = 'success',
    status: number) {
    if (status === 401) {
      if (isPlatformBrowser(this.platformId)) {
        const cookieConsent = window.localStorage.getItem('cookieConsent');
        window.localStorage.clear();
        if (cookieConsent) {
          window.localStorage.setItem('cookieConsent', cookieConsent);
        }
        this.router.navigate(['']);
        window.location.reload();
      }
    }

    const notificationClass = `custom-snackbar-${panelClass}-${Date.now()}`;

    const snackBarRef = this.snackBar.open(
      message || 'API failed, Please try again after sometime!!',
      '',
      {
        duration: 4000,
        horizontalPosition: 'center',
        verticalPosition: verticalPosition,
        panelClass: [notificationClass, 'mat-mdc-snack-bar-container']
      }
    );

    this.addNotificationStyles(notificationClass, panelClass);
    return snackBarRef;
  }

}