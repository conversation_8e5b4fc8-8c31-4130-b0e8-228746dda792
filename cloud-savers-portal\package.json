{"name": "cloud-savers-portal", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration dev", "test": "ng test", "lint": "ng lint", "prerender": "http-server -c-1 dist/cloud-savers-portal/browser", "serve:ssr": "node dist/cloud-savers-portal/server/server.mjs"}, "private": true, "dependencies": {"@angular/animations": "^20.3.0", "@angular/cdk": "^20.0.0", "@angular/common": "^20.3.0", "@angular/compiler": "^20.3.0", "@angular/core": "^20.3.0", "@angular/forms": "^20.3.0", "@angular/material": "^20.0.0", "@angular/platform-browser": "^20.3.0", "@angular/platform-browser-dynamic": "^20.3.0", "@angular/platform-server": "^20.3.0", "@angular/router": "^20.3.0", "@angular/ssr": "^20.3.1", "@storyblok/richtext": "^3.8.0", "express": "^5.1.0", "ngx-mat-select-search": "^8.0.2", "rxjs": "~7.8.0", "storyblok-js-client": "^7.1.4", "tslib": "^2.8.1", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^20.3.0", "@angular/build": "^20.3.1", "@angular/cli": "^20.3.1", "@angular/compiler-cli": "^20.3.0", "@types/express": "^5.0.1", "@types/jasmine": "~5.1.0", "@types/node": "^20.17.19", "jasmine-core": "~5.7.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.2"}}