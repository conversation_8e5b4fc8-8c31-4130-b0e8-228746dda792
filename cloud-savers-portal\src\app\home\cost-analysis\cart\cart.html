<div class="right-top-section" *ngIf="mobile">
    <div class="cart">
        <div class="section">
            <div class="header-row">
                <!-- <div class="cart-title">Your Cart</div> -->
                <div class="items-no">{{itemCount}} items</div>
            </div>
            <div class="cart-content">
                <div class="cart-container">
                    <div class="cart-items">
                        <div *ngFor="let item of cartItems || []" class="cart-item">
                            <div class="provider-image">
                                <img *ngIf="item?.RESOURCE_TYPE === 'external'"
                                    [src]="'assets/images/' + providerIconMap[item?.SERVICE_DETAILS?.service_provider | lowercase]" class="provider-icon" />
                                <img *ngIf="item?.RESOURCE_TYPE === 'internal'"
                                    [src]="'assets/images/' + providerIconMap[item?.SERVICE_DETAILS?.selection?.service_provider | lowercase]" class="provider-icon" />
                            </div>
                            <div class="item-content">
                                <div class="item-details">
                                    <div>
                                        <div class="item-header">
                                            <div class="item-name" *ngIf="item?.RESOURCE_TYPE === 'external'">
                                                {{item?.SERVICE_DETAILS?.product_name}}</div>
                                            <div class="item-name" *ngIf="item?.RESOURCE_TYPE === 'internal'">
                                                {{item?.SERVICE_DETAILS?.title}}</div>
                                        </div>
                                        <div><mat-icon class="remove-icon"
                                                (click)="removeItemFromCart(item?.ID)">close</mat-icon></div>
                                    </div>
                                    <div class="item-provider" *ngIf="item?.RESOURCE_TYPE === 'external'">
                                        {{providerNameMap[item?.SERVICE_DETAILS?.service_provider | lowercase]}}
                                    </div>
                                    <div class="item-provider" *ngIf="item?.RESOURCE_TYPE === 'internal'">
                                        {{providerNameMap[item?.SERVICE_DETAILS?.selection?.service_provider |
                                        lowercase]}}
                                    </div>
                                    <div class="item-tags-row">
                                        <span class="tag" *ngIf="item?.SERVICE_DETAILS?.payment_plan">
                                            {{formatText(item?.SERVICE_DETAILS?.payment_plan)}}</span>
                                        <span class="tag" *ngIf="item?.SERVICE_DETAILS?.vm_name">
                                            {{item?.SERVICE_DETAILS?.vm_name}}</span>
                                        <span class="tag" *ngIf="item?.SERVICE_DETAILS?.ram_gb">
                                            {{item?.SERVICE_DETAILS?.ram_gb}} GB</span>
                                        <span class="tag" *ngIf="item?.SERVICE_DETAILS?.vcpu_cores">
                                            {{item?.SERVICE_DETAILS?.vcpu_cores}} vCPUs</span>
                                        <span class="tag" *ngIf="item?.SERVICE_DETAILS?.storage_spec">
                                            {{item?.SERVICE_DETAILS?.storage_spec}}</span>
                                    </div>
                                    <div class="item-controls-row">
                                        <div class="item-qty-row">
                                            <button mat-button color="basic" class="qty-btn-minus"
                                                (click)="decreaseQuantity(item)">−</button>
                                            <span class="item-qty">{{item?.QUANTITY}}</span>
                                            <button mat-button color="basic" class="qty-btn-plus"
                                                (click)="increaseQuantity(item)">+</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="item-price-details" *ngIf="item?.RESOURCE_TYPE === 'external'">
                                    <div class="price-item"
                                        *ngIf="(item?.SERVICE_DETAILS?.payment_option | lowercase) == 'partial upfront' || (item?.SERVICE_DETAILS?.payment_option | lowercase) == 'all upfront'">
                                        <div class="price-label">{{formatText(item?.SERVICE_DETAILS?.payment_option)}}
                                        </div>
                                    </div>
                                    <div class="price-item"
                                        *ngIf="(item?.SERVICE_DETAILS?.payment_option | lowercase) == 'partial upfront' || (item?.SERVICE_DETAILS?.payment_option | lowercase) == 'all upfront'">
                                        {{formatText(item?.SERVICE_DETAILS?.term)}} -
                                        <span>${{(item?.SERVICE_DETAILS?.upfront_amount_usd *
                                            item?.QUANTITY)?.toFixed(2)}}</span>
                                    </div>
                                    <div class="price-item">Monthly -
                                        <span>${{(item?.SERVICE_DETAILS?.monthly_rate_usd *
                                            item?.QUANTITY)?.toFixed(2)}}</span>
                                    </div>
                                    <div class="price-item">Avg. /mo -
                                        <span>${{getMonthlyPrice(item?.SERVICE_DETAILS?.hourly_rate_usd,
                                            item?.SERVICE_DETAILS?.service_provider, item?.QUANTITY)}}</span>
                                    </div>
                                </div>
                                <div class="item-price-details" *ngIf="item?.RESOURCE_TYPE === 'internal'">
                                    <div class="price-item">
                                        <!-- <div class="price-label">Total</div> -->
                                        <span>${{((item?.SERVICE_DETAILS?.selection?.cost) *
                                            item?.QUANTITY)?.toFixed(2)}}</span>
                                    </div>
                                </div>
                                <div class="item-activation-row">
                                    <div class="item-qty">Qty: {{item?.QUANTITY}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- </div> -->
    <mat-divider></mat-divider>
    <div class="cart-summary">
        <div class="item">
            <div class="price-label">Subtotal</div>
            <div class="price-value">
                <span>${{formattedMnSubtotal}}</span>
            </div>
        </div>
    </div>
    <button mat-button class="buy-btn" [disabled]="cartItems.length === 0" (click)="proceedToCheckout()">
        Proceed to checkout
    </button>
    <!-- </div> -->

    <!-- Commented out sections -->
    <!-- <div class="offers" *ngIf="!mobile">
    <div class="section">
        <div class="header-row">
            <img src="assets/images/offers.png" />
            <div class="cart-title">Special Offers</div>
                    <div class="view-details">Learn More</div>
                </div>
            </div>
        </div>
    </div> -->
    <!-- <div class="deals" *ngIf="!mobile">
        <div class="section">
            <div class="header-row">
                <div class="deal-head">
                    <img src="assets/images/limited-deal.png" />
                    <div class="cart-title">Limited Time Deal</div>
                </div>
                <div class="timer">24h left</div>
            </div>
            <div class="cart-items">
                <div class="item" *ngFor="let item of limitedOffers">
                    <div class="offer-head">{{item.name}}</div>
                    <div class="offer-details">{{item.desc}}</div>
                    <button mat-button class="claim-btn">Claim Offer</button>
                </div>
            </div>
        </div>
    </div> -->
</div>