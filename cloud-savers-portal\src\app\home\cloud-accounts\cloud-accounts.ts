import { CommonModule, isPlatformBrowser } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, HostListener, Inject, PLATFORM_ID } from '@angular/core';
import { MaterialModule } from '../../material-module';
import { MatDialogRef } from '@angular/material/dialog';

interface CloudProvider {
  name: string;
  logo: string;
  accountsConnected: number;
  connectUrl?: string;
}

@Component({
  selector: 'app-cloud-accounts',
  imports: [CommonModule, MaterialModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './cloud-accounts.html',
  styleUrl: './cloud-accounts.scss'
})
export class CloudAccounts {

  cloudProviders: CloudProvider[] = [
    {
      name: 'Amazon Web Services',
      logo: 'assets/images/aws.png',
      accountsConnected: 0,
      connectUrl: '#'
    },
    {
      name: 'Microsoft Azure',
      logo: 'assets/images/azure.png',
      accountsConnected: 0,
      connectUrl: '#'
    },
    {
      name: 'Google Cloud Platform',
      logo: 'assets/images/gcp.png',
      accountsConnected: 0,
      connectUrl: '#'
    },
    {
      name: 'Oracle',
      logo: 'assets/images/oracle.png',
      accountsConnected: 0,
      connectUrl: '#'
    }
  ];
  mobile: boolean;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenWidth();
  }

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    public dialogRef: MatDialogRef<CloudAccounts>
  ) { }

  ngOnInit() {
    this.checkScreenWidth();
  }

  private checkScreenWidth() {
    if (isPlatformBrowser(this.platformId)) {
      if (window.innerWidth <= 1250) {
        this.mobile = true;
      } else {
        this.mobile = false;
      }
    }
  }

  connect(provider: CloudProvider) {

  }

}