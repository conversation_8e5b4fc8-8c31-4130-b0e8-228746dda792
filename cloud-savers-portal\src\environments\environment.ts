// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  PORTAL_URL: 'https://portal-dev.cloudatler.com',
  API_BASE_URL: 'https://app-dev-service.cloudatler.com',
  BUCKET_URL: 'https://app-dev-assets.cloudatler.com/',
  DASHBOARD_URL: 'https://app-dev.cloudatler.com',
  EMAIL_PATTERN: '^(?!.{201})[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,5}$',
  PASSWORD_PATTERN: '^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$',
  HOURS_IN_A_MONTH: { "avg": 730, "aws": 730, "azure": 730, "gcp": 730, "oracle": 843, "digital ocean": 672 },
  PER_PAGE: 6,
  MAX_GROUPED_LIMIT: 99
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
