import { CommonModule, isPlatformBrowser } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, HostListener, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { MaterialModule } from '../../material-module';
import { Header } from '../header/header';
import { Footer } from '../footer/footer';
import { MobileFooter } from '../mobile-footer/mobile-footer';
import { Router, RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { Auth } from '../../services/auth';
@Component({
  selector: 'app-blogs',
  standalone: true,
  imports: [
    CommonModule,
    MaterialModule,
    Header,
    Footer,
    MobileFooter,
    RouterModule,
    FormsModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './blogs.html',
  styleUrl: './blogs.scss'
})
export class Blogs implements OnInit {
  blogPosts: any[] | null = null;
  searchTerm: string = '';
  mobile: boolean = false;
  isSidePanelOpen = false;
  loading: boolean = true;
  categories = [];
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenWidth();
  }

  popularTags: string[] = [ //check
    'AWS', 'Azure', 'Kubernetes', 'Migration', 'DevOps',
    'Security', 'Serverless', 'Cost', 'Docker', 'GCP'
  ];

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private router: Router,
    private authService: Auth
  ) { }

  ngOnInit() {
    this.checkScreenWidth();
    this.scrollToTop();
    this.loadBlogPosts();
    if (isPlatformBrowser(this.platformId)) {
      this.mobile = window.innerWidth <= 1250;
    }
  }

  async loadBlogPosts() {
    const res = await this.authService.getAllBlogs();
    const jsonPath = res.data.stories;
    this.blogPosts = jsonPath;
    this.categories = [];
    this.blogPosts.forEach((post: any) => {
      if (!post.content?.category) return;
      const category = post.content?.category;
      const existingCategory = this.categories.find((cat: any) => cat.name === category);
      if (existingCategory) {
        existingCategory.count++;
      } else {
        this.categories.push({ name: category, count: 1 });
      }
    });
    setTimeout(() => {
      this.loading = false;
    }, 3000);
  }

  blogDetails(post: any) {
    this.router.navigate([post.content.url || post.id]);
  }

  // private getTagsForBlog(blogId: string): string[] {
  //   const tags: { [key: string]: string[] } = {
  //     'blog1': ['AWS', 'Azure', 'Cloud Migration', 'Performance'],
  //     'blog2': ['SMB', 'Cloud Strategy', 'Cost Optimization'],
  //     'blog3': ['Cloud Comparison', 'Performance', 'Cloud Strategy']
  //   };
  //   return tags[blogId] || ['Cloud'];
  // }

  toggleSidePanel() {
    this.isSidePanelOpen = !this.isSidePanelOpen;
    if (isPlatformBrowser(this.platformId)) {
      document.body.style.overflow = this.isSidePanelOpen ? 'hidden' : '';
    }
  }

  closeSidePanel() {
    this.isSidePanelOpen = false;
    if (isPlatformBrowser(this.platformId)) {
      document.body.style.overflow = '';
    }
  }

  private checkScreenWidth() {
    if (isPlatformBrowser(this.platformId)) {
      const isMobile = window.innerWidth <= 1250;
      if (this.mobile !== isMobile) {
        this.mobile = isMobile;
        if (!this.mobile) {
          this.closeSidePanel();
        } else {
          this.isSidePanelOpen = false;
        }
      }
    }
  }

  onSearch(): void {
    if (!this.searchTerm?.trim()) {
      this.loadBlogPosts();
      return;
    }
    const searchLower = !!this.searchTerm ? this.searchTerm.toLowerCase() : '';
    this.blogPosts = this.blogPosts?.filter((post: any) =>
      (!!post?.content?.heading ? post?.content?.heading?.toLowerCase() : '')?.includes(searchLower) ||
      (!!post?.content?.short_description ? post?.content?.short_description?.toLowerCase() : '')?.includes(searchLower) ||
      post?.tag_list?.some((tag: string) => (!!tag ? tag.toLowerCase() : '')?.includes(searchLower)) //check
    )
  }

  private scrollToTop() {
    setTimeout(() => {
      if (isPlatformBrowser(this.platformId)) {
        window.scrollTo(0, 0);
        document.documentElement.scrollTop = 0;
        document.body.scrollTop = 0;
      }
    }, 50);
  }

  backToHome() {
    this.router.navigate(['/']);
  }

}