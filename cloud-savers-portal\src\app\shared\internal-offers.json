{"FEATURED_OFFERS": [{"type": "featured", "record_id": "internal_featured_1", "title": "AI/ML Dev Sandbox", "title_details": "Cloud Cost Comparison", "description": "Ideal for ML Researchers & Developers", "details": ["Compute: 1x GPU (AWS: p3.2xlarge / Azure: Standard_NC6)", "Storage: 100 GB SSD", "Platform: JupyterLab pre-installed"], "key_insight": ["Compute: 1x GPU (AWS: p3.2xlarge / Azure: Standard_NC6)", "Storage: 100 GB SSD", "Platform: JupyterLab pre-installed"], "region": [{"name": "US Central (Iowa)", "type": "Primary Region", "aws": 741, "azure": 239.36, "notes": "", "default": false}, {"name": "India (Mumbai)", "type": "Primary Region", "aws": 540.94, "azure": 302.37, "notes": "", "default": false}, {"name": "US East (N. Virginia)", "type": "Primary Region", "aws": 687, "azure": 211, "notes": "", "default": true}, {"name": "West Europe (Netherlands)", "type": "Primary Region", "aws": 805.74, "azure": 240.24, "notes": "", "default": false}]}, {"type": "featured", "record_id": "internal_featured_2", "title": "SaaS Startup Infra", "title_details": "Cloud Cost Comparison", "description": "Ideal for Early-Stage SaaS Apps", "details": ["Compute: 2x t3.micro (AWS) / B2s (Azure)", "Database: RDS db.t3.micro (AWS) / Azure DB for PostgreSQL", "Networking: Load Balancer (ALB / Azure LB)", "Security: AWS WAF/ Azure Application GW (WAF)"], "key_insight": ["Compute: 2x t3.micro (AWS) / B2s (Azure)", "Database: RDS db.t3.micro (AWS) / Azure DB for PostgreSQL", "Networking: Load Balancer (ALB / Azure LB)", "Security: AWS WAF/ Azure Application GW (WAF)"], "region": [{"name": "US East (N. Virginia)", "type": "Primary Region", "aws": 178.84, "azure": 202.39, "notes": "", "default": true}, {"name": "US Central (Iowa)", "type": "Primary Region", "aws": 198, "azure": 225.32, "notes": "", "default": false}, {"name": "West Europe (Netherlands)", "type": "Primary Region", "aws": 188.22, "azure": 234, "notes": "", "default": false}, {"name": "India (Mumbai)", "type": "Primary Region", "aws": 198, "azure": 259.56, "notes": "", "default": false}]}, {"type": "featured", "record_id": "internal_featured_3", "title": "CI/CD Test Automation", "title_details": "Cloud Cost Comparison", "description": "Ideal for DevOps, Regression Testing, and Staging Pipelines", "details": ["Compute: t3.small (AWS) / B1s (Azure)", "CI/CD Tooling: CodePipeline (AWS) / Azure DevOps", "Storage: S3 (AWS) / Azure Blob"], "key_insight": ["Compute: t3.small (AWS) / B1s (Azure)", "CI/CD Tooling: CodePipeline (AWS) / Azure DevOps", "Storage: S3 (AWS) / Azure Blob"], "region": [{"name": "US East (N. Virginia)", "type": "Primary Region", "aws": 21.8, "azure": 78.72, "notes": "", "default": true}, {"name": "India (Mumbai)", "type": "Primary Region", "aws": 22.15, "azure": 79.32, "notes": "", "default": false}, {"name": "West Europe (Netherlands)", "type": "Primary Region", "aws": 22.3, "azure": 79.77, "notes": "", "default": false}, {"name": "US Central (Iowa)", "type": "Primary Region", "aws": 21, "azure": 80.01, "notes": "", "default": false}]}], "OFFERS": [{"type": "general", "record_id": "internal_4", "title": "E-Commerce Store Hosting", "title_details": "Cloud Cost Comparison", "description": "Ideal for Online Retailers, D2C Brands & Digital Marketplaces", "details": ["AWS: EC2 m5.large, RDS db.m5.large, CloudFront", "Azure: D2s_v3, Azure SQL, Azure CDN", "Usage Assumption: Web + transactional traffic with CDN acceleration"], "key_insight": ["Azure provides substantially lower cost hosting across all regions", "Ideal setup for storefronts with medium-scale traffic", "Easily layer with additional security (WAF, custom domains, failover routing)"], "region": [{"name": "US East (N. Virginia", "type": "Primary Region", "aws": 506, "azure": 158.08, "notes": "", "default": true}, {"name": "US Central (Iowa)", "type": "Primary Region", "aws": 556, "azure": 170.3, "notes": "", "default": false}, {"name": "West Europe (Netherlands)", "type": "Primary Region", "aws": 555, "azure": 175.39, "notes": "", "default": false}]}, {"type": "general", "record_id": "internal_5", "title": "E-Commerce Store Hosting", "title_details": "Cloud Cost Comparison", "description": "Ideal for Online Stores, Marketplaces & High-Traffic Websites", "details": ["AWS: EC2 m5.large, RDS db.m5.large, CloudFront", "Azure: D2s_v3, Azure SQL, Azure CDN", "Assumption: Mid-sized e-commerce workload with DB + CDN"], "key_insight": ["Azure offers 60-70% lower hosting costs across regions", "Suitable for B2C storefronts, multi-region brands, and D2C apps", "CDN bundled for fast content delivery and improved page speed"], "region": [{"name": "US East (N. Virginia)", "type": "Primary Region", "aws": 507, "azure": 558, "notes": "", "default": true}, {"name": "India (Mumbai)", "type": "Primary Region", "aws": 548, "azure": 596, "notes": "", "default": false}, {"name": "US Central (Iowa)", "type": "Primary Region", "aws": 595, "azure": 625, "notes": "", "default": false}, {"name": "West Europe (Netherlands)", "type": "Primary Region", "aws": 606, "azure": 618, "notes": "", "default": false}]}, {"type": "general", "record_id": "internal_6", "title": "BI & Analytics Stack", "title_details": "Cloud Cost Comparison", "description": "Ideal for Dashboards, Data Lakes & Enterprise Reporting", "details": ["AWS: Redshift dc2.large, AWS Glue, QuickSight", "Azure: Synapse Analytics, Data Factory, Power BI", "Use Case: End-to-end data pipeline, warehousing & visualization"], "key_insight": ["AWS delivers more cost-effective analytics stack in all regions", "Great for data engineering teams needing SQL-based warehousing + BI", "Ideal for regulated industries needing scalable ETL + visual layers"], "region": [{"name": "US East (N. Virginia)", "type": "Primary Region", "aws": 1004.82, "azure": 2695.0, "notes": "", "default": true}, {"name": "US Central (Iowa)", "type": "Primary Region", "aws": 1023.48, "azure": 2856.61, "notes": "", "default": false}, {"name": "India (Mumbai)", "type": "Primary Region", "aws": 1011.83, "azure": 2909.0, "notes": "", "default": false}, {"name": "West Europe (Netherlands)", "type": "Primary Region", "aws": 1041.32, "azure": 2910.42, "notes": "", "default": false}]}, {"type": "general", "record_id": "internal_7", "title": "Static Website + CDN", "title_details": "Cloud Cost Comparison", "description": "Ideal for Marketing Sites, Documentation Portals & Landing Pages", "details": ["AWS: S3 (Static Hosting), CloudFront, Route53", "Azure: Blob Storage Static Web, Azure CDN, Azure DNS", "Use Case: Fast, globally distributed static site delivery"], "key_insight": ["Azure consistently provides ~70-75% cost savings for static content", "Ideal for simple, low-maintenance site hosting with global reach", "Both platforms include CDN & DNS - suitable for performance-first teams"], "region": [{"name": "US Central (Iowa)", "type": "Primary Region", "aws": 31.95, "azure": 33.98, "notes": "", "default": true}, {"name": "US East (N. Virginia)", "type": "Primary Region", "aws": 31.95, "azure": 34.2, "notes": "", "default": false}, {"name": "West Europe (Netherlands)", "type": "Primary Region", "aws": 31.95, "azure": 34.19, "notes": "", "default": false}, {"name": "India (Mumbai)", "type": "Primary Region", "aws": 31.95, "azure": 34.25, "notes": "", "default": false}]}, {"type": "general", "record_id": "internal_8", "title": "GenAI API Infrastructure", "title_details": "Cloud Cost Comparison", "description": "Ideal for Building & Serving LLM APIs, Chatbots, and AI Agents", "details": ["AWS: Graviton EC2, Bedrock API (serverless), API Gateway", "Azure: A10 GPU VM, OpenAI (Azure) APIs, Azure API Management", "Use Case: GenAI inference APIs with secure, scalable compute"], "key_insight": ["AWS offers cheaper serverless GenAI API stack across all regions", "Azure runs A10 GPU with OpenAI — more powerful, but costlier", "Best for devs building LLM-powered features with scalable inference APIs"], "region": [{"name": "US East (N. Virginia)", "type": "Primary Region", "aws": 356, "azure": 379.47, "notes": "", "default": true}, {"name": "US Central (Iowa)", "type": "Primary Region", "aws": 362, "azure": 455.37, "notes": "", "default": false}, {"name": "West Europe (Netherlands)", "type": "Primary Region", "aws": 362.26, "azure": 461.94, "notes": "", "default": false}, {"name": "India (Mumbai)", "type": "Primary Region", "aws": 337, "azure": 512.31, "notes": "", "default": false}]}, {"type": "general", "record_id": "internal_9", "title": "Mobile App Backend (MBaaS)", "title_details": "Cloud Cost Comparison", "description": "Ideal for User Auth, Realtime Data, and Serverless Mobile APIs", "details": ["AWS: Cognito, DynamoDB, Lambda, Amplify", "Azure: AD B2C, Cosmos DB, Functions, Static Web Apps", "Use Case: Mobile-ready backend with identity, data, logic & hosting"], "key_insight": ["Azure offers dramatic cost savings for serverless mobile backends", "Great for MVPs, consumer apps, PWA APIs", "Amplify + Lambda stack in AWS is powerful but pricier"], "region": [{"name": "US East (N. Virginia)", "type": "Primary Region", "aws": 37.55, "azure": 2.5, "notes": "", "default": true}, {"name": "US Central (Iowa)", "type": "Primary Region", "aws": 40.35, "azure": 2.5, "notes": "", "default": false}, {"name": "West Europe (Netherlands)", "type": "Primary Region", "aws": 41.24, "azure": 2.5, "notes": "", "default": false}, {"name": "India (Mumbai)", "type": "Primary Region", "aws": 41.43, "azure": 2.5, "notes": "", "default": false}]}]}