.cloud-accounts {
    display: flex;
    flex-direction: column;

    .account-head {
        display: flex;
        flex-direction: column;
        padding: 35px 50px;
        background-color: #0A1930;
        color: #FFFFFF;
        gap: 15px;
        align-items: start;
        justify-content: center;

        .head-row {
            display: flex;
            justify-content: space-between;
            width: 100%;

            .title {
                font-size: 24px;
                font-weight: 600;
                line-height: 32px;
            }

            .close-icon {
                border-radius: 50%;
                background: transparent;
                border: 2px solid #FFFFFF;
                color: #FFFFFF !important;
                padding: 4px;
                height: 30px;
                width: 30px;
            }
        }

        .desc {
            font-size: 16px;
            line-height: 24px;
        }
    }

    .accounts-grid {
        padding: 40px 50px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: 25px;
        justify-content: space-between;
        align-items: center;
        overflow-y: scroll;
        overflow-x: hidden;
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .accounts-grid ::-webkit-scrollbar {
        display: none;
    }

    .account-card {
        border-radius: 16px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap: 20px;
        align-items: start;
        border: 1px solid #E5E7EB;
        width: 48%;

        .provider {
            display: flex;
            flex-direction: row;
            gap: 15px;
            align-items: center;

            .account-info {
                flex-grow: 1;
                display: flex;
                flex-direction: column;
                gap: 5px;

                .provider-name {
                    font-size: 16px;
                    font-weight: 500;
                    line-height: 24px;
                    color: #111827;
                }

                .accounts-connected {
                    font-size: 14px;
                    line-height: 20px;
                    color: #6B7280;
                }
            }
        }

        .action {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            // gap: 10px;
            align-items: center;
            width: 100%;

            .connect-btn {
                background-color: #35A5DB;
                color: #FFFFFF;
                font-size: 16px;
                padding: 10px 20px;
                border-radius: 8px;
            }

            .create-link {
                font-size: 16px;
                color: #35A5DB;
                cursor: pointer;
                text-decoration: none;
            }
        }
    }
}

@media only screen and (max-width: 1250px) {

    .accounts-head {
        padding: 20px;
    }

    .account-card {
        width: 100% !important;

        .action {
            justify-content: center !important;
            align-items: center !important;
            flex-wrap: wrap !important;
            gap: 12px !important;
        }
    }
}