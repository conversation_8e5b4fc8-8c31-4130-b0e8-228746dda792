<div class="close-row">
    <button mat-button mat-dialog-close class="close-btn">
        <mat-icon class="close-icon">
            close
        </mat-icon>
    </button>
</div>
<div class="add-user">
    <div class="header-row">
        <div>
            <span mat-dialog-title class="header">{{isLogin ? 'Log In to your company’s account':'Create Your Free
                Account'}}</span>
        </div>
    </div>
    <form [formGroup]="loginForm" autocomplete="off">
        <div class="content">
            <div class="content-row" *ngIf="!isLogin">
                <div class="box-container">
                    <div class="box-name">
                        First Name*
                    </div>
                    <div>
                        <input matInput class="text-box" placeholder="Enter your first name here"
                            formControlName="firstName" maxlength="50" required />
                        <mat-error class="error"
                            *ngIf="loginForm.controls['firstName']?.touched && loginForm.get('firstName')?.hasError('required')">
                            Required
                        </mat-error>
                    </div>
                </div>
                <div class="box-container">
                    <div class="box-name">
                        Last Name*
                    </div>
                    <div>
                        <input matInput class="text-box" placeholder="Enter your last name here"
                            formControlName="lastName" maxlength="50" required />
                        <mat-error class="error"
                            *ngIf="loginForm.controls['lastName']?.touched && loginForm.get('lastName')?.hasError('required')">
                            Required
                        </mat-error>
                    </div>
                </div>
            </div>
            <div class="content-row">
                <div class="box-container">
                    <div class="box-name">
                        Email*
                    </div>
                    <div>
                        <input matInput class="text-box" placeholder="Enter your email here" formControlName="emailId"
                            required />
                        <mat-error class="error"
                            *ngIf="loginForm.controls['emailId']?.touched && loginForm.get('emailId')?.hasError('required')">
                            Required
                        </mat-error>
                        <mat-error class="error" *ngIf="loginForm.get('emailId')?.hasError('pattern')">
                            Invalid Pattern
                        </mat-error>
                    </div>
                </div>
            </div>
            <div class="content-row" *ngIf="!isLogin">
                <div class="box-container">
                    <div class="box-name">
                        Phone Number*
                    </div>
                    <div class="phone-input">
                        <mat-select panelClass="codeClass" class="theme-input-phcode" formControlName="phoneCode"
                            placeholder="Select" (selectionChange)="changeCountry($event.value)">
                            <mat-select-trigger>{{countryCode}}</mat-select-trigger>
                            <mat-option>
                                <ngx-mat-select-search placeholderLabel="Search" [formControl]="country"
                                    noEntriesFoundLabel="No Matching Found">
                                    <mat-icon ngxMatSelectSearchClear class="close-icon">close</mat-icon>
                                </ngx-mat-select-search>
                            </mat-option>
                            <mat-option *ngFor="let country of filterList || []" [value]="country.ID">
                                <div class="options">
                                    <div class="c-code">+{{country.CODE}}</div>
                                    <div class="c-name">{{country.NAME}}</div>
                                </div>
                            </mat-option>
                        </mat-select>
                        <input class="theme-input-num" type="text" placeholder="Enter phone number here"
                            autocomplete="off" formControlName="phoneNumber" (input)="getInput($event)">
                    </div>
                    <mat-error class="error"
                        *ngIf="loginForm.controls['phoneCode']?.touched && loginForm.get('phoneCode')?.hasError('required')">
                        Country Code is required
                    </mat-error>
                    <mat-error class="error" *ngIf="loginForm.get('phoneNumber')?.hasError('pattern')">
                        Phone number must contain only numbers and shouldn't exceed 50 digits
                    </mat-error>
                    <mat-error class="error"
                        *ngIf="loginForm.controls['phoneNumber']?.touched && loginForm.get('phoneNumber')?.hasError('required')">
                        Phone number is required
                    </mat-error>
                </div>
            </div>
            <div class="content-row" *ngIf="!isLogin">
                <div class="box-container">
                    <div class="box-name">
                        Company Name*
                    </div>
                    <div>
                        <input matInput class="text-box" placeholder="Enter your company name here"
                            formControlName="companyName" required />
                        <mat-error class="error"
                            *ngIf="loginForm.controls['companyName']?.touched && loginForm.get('companyName')?.hasError('required')">
                            Required
                        </mat-error>
                    </div>
                </div>
            </div>
            <div class="content-row">
                <div class="box-container">
                    <div class="box-name">
                        Password*
                    </div>
                    <div>
                        <div class="password-input">
                            <input matInput class="text-box password" [type]="hidePassword ? 'password' : 'text'"
                                formControlName="password" placeholder="Enter your password here" required />
                            <button mat-icon-button matSuffix class="hide-icon" type="button"
                                (click)="hidePassword = !hidePassword">
                                <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                            </button>
                        </div>
                        <mat-error class="error"
                            *ngIf="loginForm.controls['password']?.touched && loginForm.get('password')?.hasError('required')">
                            Required
                        </mat-error>
                        <mat-error class="error" *ngIf="loginForm.get('password')?.hasError('pattern')">
                            Invalid Pattern
                        </mat-error>
                    </div>
                </div>
            </div>
            <div class="password-rules" *ngIf="!isLogin">
                <div class="rules">
                    <img [src]="'assets/images/' + (passwordRules.minLength ? 'rule-obeyed.png' : 'rule-violated.png')">
                    <div>At least 8 characters long</div>
                </div>
                <div class="rules">
                    <img [src]="'assets/images/' + (passwordRules.upper ? 'rule-obeyed.png' : 'rule-violated.png')">
                    <div>One uppercase letter (A-Z)</div>
                </div>
                <div class="rules">
                    <img [src]="'assets/images/' + (passwordRules.lower ? 'rule-obeyed.png' : 'rule-violated.png')">
                    <div>One lowercase letter (a-z)</div>
                </div>
                <div class="rules">
                    <img [src]="'assets/images/' + (passwordRules.number ? 'rule-obeyed.png' : 'rule-violated.png')">
                    <div>One number (0-9)</div>
                </div>
                <div class="rules">
                    <img [src]="'assets/images/' + (passwordRules.special ? 'rule-obeyed.png' : 'rule-violated.png')">
                    <div>One special character (!?&#64;#$%^&*)</div>
                </div>
            </div>
            <mat-checkbox formControlName="tnc" *ngIf="!isLogin">
                <span class="accept-text">
                    Accept <span (click)="viewServiceTerms()">Terms of Service</span> and <span
                        (click)="viewPrivacyPolicy()">Privacy Policy</span>
                </span>
            </mat-checkbox>
        </div>
        <div class="btn-container">
            <div class="create-btn">
                <button mat-button class="btn-create-account" (click)="createAccount()"
                    [disabled]="!loginForm.valid || !loginForm.get('tnc')?.value || disabled" *ngIf="!isLogin">
                    Create Account <i class="fa fa-spinner fa-spin" *ngIf="disabled"></i>
                </button>
                <button mat-button class="btn-create-account" cdkFocusInitial (click)="userLogin()"
                    [disabled]="!loginForm.valid || disabled" *ngIf="isLogin">Log In
                    <i class="fa fa-spinner fa-spin" *ngIf="disabled"></i>
                </button>
            </div>
            <div class="login-link">{{isLogin ? 'Don’t have a account?':'Already have an account?'}}
                <span (click)="switchForm()">{{isLogin ? 'Sign up':'Log in'}}</span>
            </div>
        </div>
        <!-- <div class="divider">- OR -</div>
        <div class="social-buttons">
            <button mat-button class="btn-google" (click)="socialLogin('Google')">
                <div class="btn-txt">
                    <img src="assets/images/Google.png" alt="Google" />
                    <span>Sign up with Google</span>
                </div>
            </button>
            <button mat-button class="btn-google" (click)="socialLogin('GitHub')">
                <div class="btn-txt">
                    <img src="assets/images/GitHub.png" alt="GitHub" />
                    <span>Sign up with GitHub</span>
                </div>
            </button>
        </div> -->
    </form>
</div>