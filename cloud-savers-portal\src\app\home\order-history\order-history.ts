import { CommonModule, isPlatform<PERSON>rowser } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, HostListener, ViewChild, Inject, PLATFORM_ID } from '@angular/core';
import { MaterialModule } from '../../material-module';
import { Header } from '../header/header';
import { Router } from '@angular/router';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { CartService } from '../../services/cart-service';
import { Notification } from '../../services/notification';
import { colorObj } from '../../shared/color-object';
import { PROVIDER_ICON_MAP } from '../../shared/filter-headers.constants';

@Component({
  selector: 'app-order-history',
  imports: [
    CommonModule,
    MaterialModule,
    Header
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './order-history.html',
  styleUrl: './order-history.scss'
})
export class OrderHistory {

  loading: boolean = false;
  displayedColumns: string[] = ['orderId', 'date', 'services', 'totalPrice'];
  @ViewChild(MatPaginator) paginator: MatPaginator;
  dataSource = new MatTableDataSource<any>();
  get providerIconMap() {
    return PROVIDER_ICON_MAP;
  }
  mobile: boolean = false;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenWidth();
  }

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private cartService: CartService,
    private router: Router,
    private notify: Notification
  ) { }

  ngOnInit() {
    this.checkScreenWidth();
    this.getOrderList();
  }

  private checkScreenWidth() {
    if (isPlatformBrowser(this.platformId)) {
      if (window.innerWidth <= 1250) {
        this.mobile = true;
      } else {
        this.mobile = false;
      }
    }
  }

  getOrderList() {
    this.loading = true;
    this.cartService.getOrderHistory().subscribe({
      next: (data) => {
        this.dataSource.data = data.object;
        this.dataSource.paginator = this.paginator;
        this.loading = false;
      },
      error: (e) => {
        this.loading = false;
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        )
      }
    })
  }

  getTotalPrice(element) {
    if (element.ORDER_ITEMS && element.ORDER_ITEMS.length > 0) {
      return element.ORDER_ITEMS.reduce((total, item) => total + ((item?.RESOURCE_TYPE
        === 'external' ? item?.SERVICE_DETAILS?.monthly_rate_usd : item?.SERVICE_DETAILS?.selection?.cost) * item?.QUANTITY || 0), 0);
    }
  }

  backToHome() {
    this.router.navigate(['/']);
  }

}