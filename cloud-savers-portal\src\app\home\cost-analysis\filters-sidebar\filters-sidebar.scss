:host ::ng-deep .mat-mdc-form-field-flex>.mat-mdc-form-field-infix {
    padding: 5px 0px 0.4em 0px !important;
    min-height: 0px !important;
}

::ng-deep .mat-mdc-form-field-subscript-wrapper {
    box-sizing: border-box;
    width: 100%;
    position: relative !important;
}

mat-checkbox {
    display: block;
    --mdc-checkbox-selected-icon-color: #35A5DB !important;
    --mdc-checkbox-selected-hover-icon-color: #35A5DB !important;
    --mdc-checkbox-selected-pressed-icon-color: #35A5DB !important;
    --mdc-checkbox-selected-focused-icon-color: #35A5DB !important;
}

::ng-deep {
    .mat-mdc-slider {
        --mdc-slider-handle-color: #35A5DB;
        --mdc-slider-focus-handle-color: #35A5DB;
        --mdc-slider-hover-handle-color: #35A5DB;
        --mdc-slider-active-track-color: #35A5DB;
        --mdc-slider-inactive-track-color: #E5E7EB;
    }

    .mat-mdc-slider .mdc-slider__thumb-knob {
        border-color: #35A5DB;
        background-color: #35A5DB;
    }

    .mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb-knob {
        border-color: #35A5DB;
    }

    .mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb-knob {
        border-color: #35A5DB;
    }

    .mat-mdc-slider .mdc-slider__track--active_fill {
        border-color: #35A5DB;
    }
}

.filters-sidebar {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 30px;

    .filter-select {
        width: 100%;
        height: 35px;
        background-color: #FFFFFF !important;
        border: none;
        outline: none;
        border-radius: 8px;
    }

    .filter-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0px 1px 2px 0px #0000000D;
        display: flex;
        flex-direction: column;
        gap: 15px;
        min-height: auto;
        max-height: 400px;
        overflow-y: scroll;
        overflow-x: hidden;
        -ms-overflow-style: none;
        scrollbar-width: none;

        .search-container {
            margin-bottom: 10px;

            .search-input-wrapper {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                background-color: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                padding: 8px 12px;
                transition: border-color 0.2s ease;
                min-height: 40px;

                &:focus-within {
                    border-color: #0066CC;
                    box-shadow: 0 0 0 1px #0066CC;
                }

                .search-icon {
                    color: #9CA3AF;
                    font-size: 18px;
                    margin-right: 8px;
                    flex-shrink: 0;
                }

                .search-input {
                    flex: 1;
                    border: none;
                    outline: none;
                    background: transparent;
                    font-size: 14px;
                    color: #374151;
                    min-width: 0;

                    &::placeholder {
                        color: #9CA3AF;
                    }
                }

                .clear-icon {
                    color: #9CA3AF;
                    font-size: 18px;
                    cursor: pointer;
                    margin-left: 8px;
                    transition: color 0.2s ease;
                    flex-shrink: 0;

                    &:hover {
                        color: #374151;
                    }
                }
            }
        }

        .filter-head {
            font-weight: 500;
            font-size: 16px;
            color: #111827;
            line-height: 24px;
        }

        .filter-body {
            display: flex;
            flex-direction: column;

            .filter-option {
                font-weight: 400;
                font-size: 14px;
                color: #374151;
                line-height: 24px;
            }

            .see-more {
                color: #0066CC;
                cursor: pointer;
                font-size: 14px;
                padding-top: 10px;
            }
        }

        .quantity-selector {
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 25px;

            .qty-group {
                display: flex;
                flex-direction: column;
                gap: 10px;
                width: auto;

                .qty-label {
                    font-size: 15px;
                    font-weight: 400;
                    color: #374151;
                }
            }
        }

        .price-range-container {
            display: flex;
            flex-direction: column;
            gap: 15px;

            input[type="range"] {
                width: 100%;
            }

            span {
                font-size: 12px;
                font-weight: 400;
                color: #6B7280;
            }

            .price-inputs {
                display: flex;
                align-items: center;
                margin-bottom: 16px;

                .price-input {
                    flex: 1;
                    padding: 10px;
                    border: 1px solid #D1D5DB;
                    border-radius: 8px;
                    font-size: 14px;
                    outline: none;
                    width: 40%;
                }

                .separator {
                    margin: 0 8px;
                    color: #D1D5DB;
                }
            }
        }

        .apply-price-btn {
            width: 100%;
            background: #F3F4F6;
            border-radius: 8px;
            color: #374151;
            font-size: 14px;
            padding: 10px 20px;
            border: none;

            &:disabled {
                opacity: 0.5;
            }
        }
    }

    .filter-section ::-webkit-scrollbar {
        display: none;
    }

    .action-section {
        position: sticky;
        bottom: 0;
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0px 1px 2px 0px #0000000D;
        display: flex;
        justify-content: space-between;
        gap: 20px;
        align-items: center;
    }

    .apply-btn {
        width: 45%;
        background: #35A5DB;
        border-radius: 8px;
        color: #FFFFFF;
        font-size: 14px;
        padding: 10px 20px;
        border: none;
    }

    .apply-btn:disabled {
        opacity: 0.5;
    }

    .clear-btn {
        width: 45%;
        background: #F3F4F6;
        border-radius: 8px;
        color: #374151;
        font-size: 14px;
        padding: 10px 20px;
        border: none;
    }
}