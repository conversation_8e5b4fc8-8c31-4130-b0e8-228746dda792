.container {
    width: 100%;
    height: auto;
    background-image: url('../../../assets/images/background_img.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.provider-logo {
    width: 40px;
    height: 40px;
}

.head-row {
    display: flex;
    align-items: center;
    padding: 20px 50px;
    background-color: #FFFFFF;
    border-bottom: 1px solid #E5E7EB;

    .back-arrow {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 16px;
        color: #374151;
        cursor: pointer;
        text-align: start;
        width: 13%;
    }

    .pricing-head {
        font-size: 20px;
        font-weight: 600;
        color: #000000;
        text-align: center;
        width: 85%;
    }
}

.detail-content {
    padding: 30px 50px;
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 30px;

    .compute-section {
        background-color: #FFFFFF;
        border-radius: 16px;
        border: 1px solid #E5E7EB;
        padding: 10px 25px 25px 25px;
        display: flex;
        justify-content: space-between;
        gap: 20px;

        .compute-tab {
            display: flex;
            justify-content: space-between;
            gap: 30px;
            padding-left: 23px;

            .left-panel,
            .right-panel {
                width: 48%;
                display: flex;
                flex-direction: column;
                position: relative;
                gap: 20px;

                .spec-column {
                    gap: 20px;
                    display: flex;
                    flex-direction: column;
                }

                .spec-row {
                    display: flex;
                    justify-content: space-between;
                    gap: 15px;
                    width: 100%;
                    font-size: 16px;
                    color: #4B5563;

                    span {
                        font-size: 16px;
                        font-weight: 500;
                        color: #000000;
                    }

                }

                .memory-recommendation {
                    padding: 15px;
                    background-color: #F9FAFB;
                    border-radius: 16px;
                }

            }
        }
    }
}

.feature-title {
    font-size: 18px;
    font-weight: 600;
    color: #000000;
    padding: 10px 0;
}

.performance-description {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
}

.description {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.desc-title {
    font-size: 16px;
    font-weight: 500;
    color: #000000;
}

.desc-content {
    font-size: 16px;
    color: #4B5563;
    line-height: 24px;
}

.feature-head {
    display: flex;
    flex-direction: column;
    gap: 5px;

    .title {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        font-weight: 500;
        font-size: 16px;
        color: #000000;
        line-height: 1.4;

        iconify-icon {
            margin-top: 2px;
            flex-shrink: 0;
            color: #22C55E;
        }
    }

    .desc {
        font-size: 14px;
        color: #4B5563;
        line-height: 20px;
        padding-left: 22px;
    }
}

.benchmark-scores {
    margin-bottom: 16px;

    .benchmark-title {
        font-size: 18px;
        font-weight: 600;
        color: #000000;
        padding: 10px 0;
        margin-bottom: 8px;
    }

    .benchmark-item {
        margin-bottom: 16px;

        &:last-child {
            margin-bottom: 0;
        }

        .benchmark-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;

            .benchmark-label {
                font-size: 14px;
                color: #4B5563;
            }

            .benchmark-value {
                font-size: 14px;
                font-weight: 500;
                color: #000000;
            }
        }

        .benchmark-bar {
            height: 8px;
            background: #E5E7EB;
            border-radius: 4px;
            position: relative;
            overflow: hidden;

            .benchmark-progress {
                height: 100%;
                background: linear-gradient(90deg, #3B82F6 0%, #2563EB 100%);
                border-radius: 4px;
                min-width: 8px;
                transition: width 0.5s ease-in-out;
            }
        }
    }
}

.right-panel {
    >div:first-child {
        flex: 1;
        display: flex;
        flex-direction: column;
    }
}

.recommendation {
    padding: 15px;
    background-color: #EFF6FF;
    border-radius: 16px;

    .recc-title {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 16px;
        font-weight: 500;
        color: #1E40AF;
        margin-bottom: 8px;
    }

    .recc-desc {
        font-size: 14px;
        color: #1D4ED8;
        line-height: 20px;
    }
}

.atlerpilot-container {
    background-color: #FFFFFF;
    border-radius: 16px;
    border: 1px solid #E5E7EB;
    padding: 25px;

    .atler-row {
        display: flex;
        justify-content: space-between;
        gap: 10px;
    }

    .atler-column {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .atler-title {
        font-size: 14px;
        line-height: 20px;
        font-weight: 500;
        color: #35A5DB;
    }

    .atler-sub-title {
        font-size: 30px;
        line-height: 36px;
        font-weight: bold;
        color: #000000;
    }

    .atler-sub-desc {
        font-size: 18px;
        line-height: 28px;
        font-weight: 400;
        color: #4B5563;
    }

    .atler-sub-row {
        display: flex;
        flex-direction: row;
        gap: 40px;
    }

    .atler-sub-content {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .atler-sub-item {
        font-size: 16px;
        line-height: 24px;
        font-weight: 400;
        color: #374151;
    }

    .atler-button {
        font-size: 16px;
        line-height: 24px;
        font-weight: 500;
        color: #FFFFFF;
        background-color: #35A5DB;
        border-radius: 8px;
        padding: 10px 20px;
        width: 203px;

    }

    .atler-dashboard {
        width: 384px;
        border-radius: 16px;
    }
}

.specs-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    background-color: #FFFFFF;
    border-radius: 16px;
    border: 1px solid #E5E7EB;
    padding: 25px;
}

.specs-section {
    display: flex;
    justify-content: space-between;
    gap: 20px;

    .spec-details {
        width: 65%;
        display: flex;
        flex-direction: column;
        justify-content: start;
        gap: 20px;

        .title-row {
            display: flex;
            justify-content: start;
            align-items: center;
            gap: 25px;

            .provider-details {
                display: flex;
                flex-direction: column;
                gap: 5px;

                .provider-name {
                    font-size: 20px;
                    font-weight: 700;
                    color: #000000;
                }

                .provider-region {
                    font-size: 15px;
                    font-weight: 400;
                    color: #6B7280;
                }
            }
        }

        .spec-row {
            display: flex;
            justify-content: space-between;
            gap: 15px;
            width: 100%;

            .spec-card {
                width: 31%;
                background-color: #F9FAFB;
                padding: 15px;
                border-radius: 16px;
                display: flex;
                flex-direction: column;
                gap: 10px;
                color: #6B7280;

                .spec-title {
                    font-size: 14px;
                }

                .spec-value {
                    font-size: 18px;
                    font-weight: 600;
                    color: #000000;
                }

                .spec-desc {
                    font-size: 12px;
                }
            }
        }
    }

    .price-card {
        width: 35%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        gap: 20px;
        background-color: #F9FAFB;
        border-radius: 16px;
        padding: 25px;

        .price-details {
            display: flex;
            // flex-direction: row;
            justify-content: space-between;
            align-items: start;
            gap: 10px;
            font-size: 14px;
            color: #6B7280;

            .payment-details {
                display: flex;
                flex-direction: column;
                gap: 10px;
                align-items: start;
            }

            .payment-option {
                font-size: 13px;
                color: #1F2937;
                background-color: #dcdcdc;
                padding: 5px 10px;
                border-radius: 20px;
                text-align: center;
            }

            .price-item {
                span {
                    font-weight: 600;
                    color: #1F2937;
                }
            }

            .price-actions {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                gap: 30px;
            }

            .cart-btn {
                font-size: 14px;
                padding: 10px 20px;
                border-radius: 8px;
                background-color: #35A5DB;
                color: #FFFFFF;
            }

            .price {
                font-size: 18px;
                font-weight: 700;
                color: #1F2937;
            }

            .price-period {
                font-size: 14px;
                font-weight: 400;
                color: #6B7280;
            }

            .price-value {
                display: flex;
                align-items: center;
                font-size: 30px;
                font-weight: 700;
                color: #000000;

                span {
                    font-size: 16px;
                    font-weight: 400;
                    color: #6B7280;
                }
            }
        }

        .price-actions {
            display: flex;
            flex-direction: column;
            gap: 15px;

            .deploy-btn {
                width: 100%;
                background-color: #35A5DB;
                color: #FFFFFF;
                font-size: 16px;
                font-weight: 500;
                border-radius: 8px;
                padding: 7px 15px;
                height: auto;
            }
        }
    }
}

.features-section {
    display: flex;
    justify-content: space-between;
    gap: 30px;

    .feature-card {
        width: 50%;
        background-color: #FFFFFF;
        border-radius: 16px;
        border: 1px solid #E5E7EB;
        padding: 25px;
        display: flex;
        flex-direction: column;
        gap: 20px;

        .feature-row {
            display: flex;
            flex-direction: row;
            align-items: start;
            gap: 15px;

            .feature-head {
                display: flex;
                flex-direction: column;
                gap: 5px;

                .title {
                    font-weight: 500;
                    font-size: 16px;
                    color: #000000;
                }

                .desc {
                    font-size: 14px;
                    color: #4B5563;
                    line-height: 20px;
                    padding-left: 10px;
                }
            }
        }
    }
}

.instance-cards {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 25px;

    .instance-card-container {
        display: flex;
        flex-direction: column;
        flex: 1 1 calc(50% - 15px); // 2 cards per row, minus gap
        max-width: calc(50% - 10px);
        border-radius: 16px;

        &:hover {
            transform: scale(1.02);
        }
    }

    .instance-card {
        display: flex;
        flex-direction: column;
        border-radius: 16px;
        border: 2px solid #FFFFFF;
        overflow: hidden;
        background-color: #FFFFFF;
        gap: 15px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        z-index: 2;
    }

    .instance-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 20px 0 20px;

        .provider-type {
            display: flex;
            align-items: center;
            gap: 15px;

            .provider-logo {
                width: 40px;
                height: 40px;
            }

            .provider-details {
                display: flex;
                flex-direction: column;
                gap: 5px;

                .provider-name {
                    font-size: 14px;
                    font-weight: 500;
                    color: #1F2937;

                    span {
                        font-size: 13px;
                    }
                }

                .instance-details {
                    font-size: 12px;
                    font-weight: 400;
                    color: #6B7280;
                }
            }
        }

        .instance-family {
            font-size: 11px;
            color: #047857;
            background-color: #D1FAE5;
            padding: 5px 10px;
            border-radius: 20px;
            text-align: center;
        }

        .instance-price {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 7px;

            .price {
                display: flex;
                align-items: center;
                gap: 3px;
                font-size: 15px;
                font-weight: 700;
                color: #1F2937;
            }

            .price-period-icon {
                font-size: 14px;
                color: #6B7280;
            }

            .price-period {
                font-size: 12px;
                font-weight: 400;
                color: #6B7280;
            }
        }
    }

    .instance-specs {
        display: flex;
        padding: 0px 20px;
        justify-content: space-between;
        align-items: start;

        .spec-item {
            display: flex;
            flex-direction: column;
            gap: 10px;
            max-width: 25%;
        }

        .price-item {
            display: flex;
            flex-direction: column;
            gap: 7px;
        }

        .spec-label {
            font-size: 12px;
            color: #6B7280;
        }

        .spec-value {
            font-size: 12px;
            font-weight: 500;
            color: #1F2937;
        }
    }

    .instance-card-price {
        flex-direction: row;
        align-items: center;
        padding: 30px 20px 10px 20px;
        top: -20px;
        position: relative;
        z-index: 1;
    }

    .payment-option {
        font-size: 11px;
        color: #1F2937;
        background-color: #dcdcdc;
        padding: 5px 10px;
        border-radius: 20px;
        text-align: center;
    }

    .instance-action {
        display: flex;
        justify-content: space-between;
        gap: 5px;
        align-items: center;
        padding: 0 20px 20px 20px;

        .location {
            display: flex;
            gap: 10px;
            width: 70%;

            .location-div {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 12px;
                color: #6B7280;
            }
        }

        .action-buttons {
            display: flex;
            justify-content: end;
            align-items: center;
            width: 30%;
        }

        .action-icons {
            color: #35A5DB;

            &:disabled {
                opacity: 0.5;
            }
        }

        .quantity-selector {
            display: flex;
            justify-content: center;

            .quantity-controls {
                display: flex;
                align-items: center;
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                overflow: hidden;
                width: auto;
                height: 30px;

                .qty-btn {
                    width: 30px;
                    height: 30px;
                    background: #F9FAFB;
                    border: none;
                    font-size: 16px;
                    cursor: pointer;
                    padding: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #6B7280;

                    &:hover {
                        background: #F3F4F6;
                    }

                    &:active {
                        background: #E5E7EB;
                    }
                }

                .qty-input {
                    width: 34px;
                    height: 30px;
                    border: none;
                    text-align: center;
                    font-size: 14px;
                    outline: none;
                    padding: 0;
                    background: white;
                }
            }
        }
    }
}

.pricing-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.loading-spinner {
    display: flex;
    justify-content: center;
}

table {
    table-layout: fixed;
    width: 100%;
    border: none;
    border-bottom: 1px solid #E5E7EB;
    box-shadow: none;
}

table th,
td {
    text-align: start !important;
}

.mat-mdc-header-row {
    background: #F9FAFB;
    border: none;
}

.table-header {
    font-size: 12px;
    font-weight: 500;
    color: #6B7280;
    width: 20%;
}

table td {
    font-size: 14px;
    color: #111827;
    font-weight: 500;
    line-height: 20px;
}

.table-cell {
    width: 9%;
}

.mdc-data-table__cell,
.mdc-data-table__header-cell {
    padding: 10px 20px;
}

th.mat-header-cell,
td.mat-cell {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 10px;
    text-align: start !important;
}

.no-data {
    font-size: 14px;
    color: #4B5563;
    text-align: center;
    font-style: italic;
}

.support-section {
    background-color: #FFFFFF;
    border-radius: 16px;
    border: 1px solid #E5E7EB;
    padding: 25px;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .support-title {
        font-size: 20px;
        font-weight: 600;
        color: #000000;
    }

    .support-row {
        display: flex;
        justify-content: space-between;
        gap: 15px;

        .support-card {
            width: 31%;
            border: 1px solid #E5E7EB;
            padding: 15px;
            border-radius: 16px;
            display: flex;
            flex-direction: column;
            align-items: start;
            gap: 10px;

            .support-head {
                font-size: 16px;
                font-weight: 500;
                color: #000000;
            }

            .support-desc {
                font-size: 14px;
                color: #4B5563;
                line-height: 20px;
            }
        }
    }
}

.footer-row {
    background: #FFFFFF;
    border-top: 1px solid #E5E7EB;
    padding: 15px 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: fixed;
    bottom: 60px;
    left: 0;
    right: 0;
    z-index: 1000;

    @media only screen and (min-width: 1251px) {
        position: static;
        bottom: auto;
    }

    .footer-start {
        display: flex;
        gap: 10px;
        align-items: center;
    }

    .footer-details {
        display: flex;
        flex-direction: column;
        gap: 5px;

        .foot-name {
            font-size: 16px;
            font-weight: 500;
            color: #000000;
        }

        .foot-region {
            font-size: 14px;
            font-weight: 400;
            color: #6B7280;
        }
    }
}

.detail-tabs {
    width: 100%;
}

:host ::ng-deep .mat-mdc-tab-disabled {
    opacity: 1;
}

:host ::ng-deep .mat-mdc-tab-header {
    background: transparent;
    border-bottom: 1px solid #E5E7EB;
    margin-bottom: 20px;
}

:host ::ng-deep .mdc-tab-indicator--active {
    background: transparent !important;
    color: #35A5DB !important;
    font-weight: 500;
}

:host ::ng-deep .mat-mdc-tab.mdc-tab--active .mdc-tab__text-label {
    color: #35A5DB !important;
    font-family: 'Roboto';
    font-size: 16px;
    font-weight: 500;
}

:host ::ng-deep .mat-mdc-tab:not(.mat-mdc-tab-disabled).mdc-tab--active .mdc-tab__text-label {
    color: #35A5DB !important;
    font-family: 'Roboto';
    font-size: 16px;
    border-radius: 15px;
}

:host ::ng-deep .mdc-tab__text-label {
    color: #6B7280 !important;
    font-family: 'Roboto';
    font-size: 16px;
    font-weight: 500;
}

:host ::ng-deep .mdc-tab-indicator__content--underline {
    border: 2px solid #35A5DB !important;
    border-radius: 15px;
}

@media only screen and (max-width: 1250px) {

    .head-row {
        padding: 20px;
    }

    .detail-content {
        padding: 20px;
        position: relative;
        z-index: 1;
        margin-bottom: 150px;

        .compute-tab {
            flex-direction: column;
        }

        .left-panel,
        .right-panel {
            width: 100% !important;
        }

        .specs-section {
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;

            .spec-card {
                width: 100%;
            }

            .spec-details {
                width: 100%;

                .spec-row {
                    flex-wrap: wrap;
                    justify-content: center;
                    align-items: center;

                    .spec-card {
                        width: 80%;
                    }
                }
            }

            .price-card {
                width: 80%;

                .price-details {
                    flex-wrap: wrap;

                    .price-actions {
                        gap: 15px !important;
                    }

                }

                .provider-details {
                    flex-direction: column;
                    gap: 25px !important;

                    .payment-details {
                        gap: 15px !important;
                    }
                }
            }
        }

        .features-section {
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;

            .feature-card {
                width: 100%;
            }
        }

        .support-row {
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;

            .support-card {
                width: 100% !important;
            }
        }
    }

    .atler-row {
        gap: 20px !important;
        flex-wrap: wrap;
    }

    .atler-sub-row {
        gap: 15px !important;
        flex-wrap: wrap;
    }

    .instance-cards {
        .instance-card-container {
            flex: 1 1 100% !important;
            max-width: 100% !important;
        }
    }

    .instance-header {
        flex-wrap: wrap;
        gap: 20px;
        justify-content: center;
        align-items: center;
    }

    .instance-specs {
        flex-wrap: wrap;
        gap: 20px;

        .spec-item {
            max-width: 100% !important;
            flex-direction: row;
            gap: 25px;
            justify-content: space-between;
        }
    }

    .instance-action {
        flex-wrap: wrap;
        gap: 20px;

        .location {
            width: fit-content !important;
            flex-wrap: wrap;
        }

        .action-buttons {
            width: fit-content !important;
        }
    }

    .price-table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;

        table {
            min-width: 600px;

            th,
            td {
                padding: 10px;
                font-size: 13px;

                &:first-child {
                    position: sticky;
                    left: -0px;
                    min-width: 250px;
                }
            }

            thead {
                position: sticky;
                top: 0;
                background: white;
                z-index: 2;
            }
        }
    }
}