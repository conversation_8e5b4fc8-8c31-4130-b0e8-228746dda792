import { isPlatformServer, isPlatformBrowser } from '@angular/common';
import { HttpClient, HttpContext, HttpContextToken } from '@angular/common/http';
import { Injectable, Inject, PLATFORM_ID, TransferState, makeStateKey, StateKey } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { environment } from '../../environments/environment';
import { storyblok } from './storyblok-service';
export const BYPASS_LOG = new HttpContextToken(() => false);
import { tap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class Auth {
  private GET_MASTER_DATA: StateKey<any> = makeStateKey<any>('get-master-data');
  private USER_SIGN_UP: StateKey<any> = makeStateKey<any>('user-sign-up');
  private USER_LOG_IN: StateKey<any> = makeStateKey<any>('user-log-in');
  private GET_PRODUCT_CATEGORIES: StateKey<any> = makeStateKey<any>('get-product-categories');
  private GET_PRODUCT_CATEGORY_SERVICES: StateKey<any> = makeStateKey<any>('get-product-category-services');
  private GET_PRODUCT_CATEGORY_INSTANCES: StateKey<any> = makeStateKey<any>('get-product-category-instances');
  private GET_VCPU: StateKey<any> = makeStateKey<any>('get-vcpu');
  private GET_RAM: StateKey<any> = makeStateKey<any>('get-ram');
  private GET_REGIONS: StateKey<any> = makeStateKey<any>('get-regions');
  private GET_OPERATING_SYSTEMS: StateKey<any> = makeStateKey<any>('get-operating-systems');
  private GET_HOSTS: StateKey<any> = makeStateKey<any>('get-hosts');
  private GET_FILTERS: StateKey<any> = makeStateKey<any>('get-filters');
  private GET_PRICING_DATA: StateKey<any> = makeStateKey<any>('get-pricing-data');
  private GET_AUTOFILL_LIST: StateKey<any> = makeStateKey<any>('get-autofill-list');
  private GET_RESOURCE: StateKey<any> = makeStateKey<any>('get-resource');
  private GET_RESOURCE_DETAILS: StateKey<any> = makeStateKey<any>('get-resource-details');
  private GET_REGIONAL_PRICING: StateKey<any> = makeStateKey<any>('get-regional-pricing');
  private GET_SIMILAR_DEALS: StateKey<any> = makeStateKey<any>('get-similar-deals');
  private GET_COMPARE_RESOURCES: StateKey<any> = makeStateKey<any>('get-compare-resources');

  public resourceUrl = `${environment.API_BASE_URL}`;
  private formArraySource = new BehaviorSubject<any[]>([]);
  formArray$ = this.formArraySource.asObservable();
  private lastFormArray: any[] = [];
  setFormArray(values: any[]) {
    if (JSON.stringify(this.lastFormArray) !== JSON.stringify(values)) {
      this.lastFormArray = JSON.parse(JSON.stringify(values));
      this.formArraySource.next(values);
    }
  }

  private searchSource = new BehaviorSubject<any>('');
  searchKeyword$ = this.searchSource.asObservable();
  setSearchKeyword(values: any) {
    this.searchSource.next(values);
  }

  private showMoreFiltersIndices: number[] = [];
  setShowMoreFiltersIndices(indices: number[]) {
    this.showMoreFiltersIndices = indices;
  }
  getShowMoreFiltersIndices(): number[] {
    return this.showMoreFiltersIndices;
  }
  clearShowMoreFiltersIndices() {
    this.showMoreFiltersIndices = [];
  }

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private transferState: TransferState,
    private http: HttpClient) { }

  isLoggedIn(): boolean {
    if (isPlatformBrowser(this.platformId)) {
      return window.localStorage.getItem('userLoggedIn') === 'true';
    }
    return false;
  }

  getMasterData(): Observable<any> {
    if (this.transferState.hasKey(this.GET_MASTER_DATA)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_MASTER_DATA, null);
      this.transferState.remove(this.GET_MASTER_DATA); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.get<any>(`${this.resourceUrl}/web/reve/master-data`, { context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_MASTER_DATA, data);
          }
        })
      );
    }
  }

  userSignUp(obj: any): Observable<any> {
    if (this.transferState.hasKey(this.USER_SIGN_UP)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.USER_SIGN_UP, null);
      this.transferState.remove(this.USER_SIGN_UP); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.post<any>(`${this.resourceUrl}/web/reve/user/create`, obj, { context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.USER_SIGN_UP, data);
          }
        })
      );
    }
  }

  userLogIn(obj: any): Observable<any> {
    if (this.transferState.hasKey(this.USER_LOG_IN)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.USER_LOG_IN, null);
      this.transferState.remove(this.USER_LOG_IN); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.post<any>(`${this.resourceUrl}/web/reve/auth/login/email`, obj, { context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.USER_LOG_IN, data);
          }
        })
      );
    }
  }

  getProductCategories(): Observable<any> {
    if (this.transferState.hasKey(this.GET_PRODUCT_CATEGORIES)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_PRODUCT_CATEGORIES, null);
      this.transferState.remove(this.GET_PRODUCT_CATEGORIES); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.get<any>(`${this.resourceUrl}/web/reve/portal/providers/products`, { context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_PRODUCT_CATEGORIES, data);
          }
        })
      );
    }
  }

  getProductCategoryServices(categoryId): Observable<any> {
    if (this.transferState.hasKey(this.GET_PRODUCT_CATEGORY_SERVICES)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_PRODUCT_CATEGORY_SERVICES, null);
      this.transferState.remove(this.GET_PRODUCT_CATEGORY_SERVICES); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.get<any>(`${this.resourceUrl}/web/reve/portal/providers/products/services?PRODUCT_ID=${categoryId}`, { context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_PRODUCT_CATEGORY_SERVICES, data);
          }
        })
      );
    }
  }

  getProductCategoryInstances(serviceName): Observable<any> {
    if (this.transferState.hasKey(this.GET_PRODUCT_CATEGORY_INSTANCES)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_PRODUCT_CATEGORY_INSTANCES, null);
      this.transferState.remove(this.GET_PRODUCT_CATEGORY_INSTANCES); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.get<any>(`${this.resourceUrl}/web/reve/portal/providers/products/services/type?SERVICE_NAME=${serviceName}`, { context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_PRODUCT_CATEGORY_INSTANCES, data);
          }
        })
      );
    }
  }

  getvCPU(serviceName, instance_family): Observable<any> {
    let obj = {
      "SERVICE_NAME": serviceName,
      "INSTANCE_FAMILY": Array.isArray(instance_family) ? instance_family : [instance_family]
    }
    if (this.transferState.hasKey(this.GET_VCPU)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_VCPU, null);
      this.transferState.remove(this.GET_VCPU); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.post<any>(`${this.resourceUrl}/web/reve/portal/providers/products/services/vcpu`, obj, { context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_VCPU, data);
          }
        })
      );
    }
  }

  getRAM(serviceName, instance_family, vCPU): Observable<any> {
    let obj = {
      "SERVICE_NAME": serviceName,
      "INSTANCE_FAMILY": Array.isArray(instance_family) ? instance_family : [instance_family],
      "VCPU": Array.isArray(vCPU) ? vCPU : [vCPU]
    }
    if (this.transferState.hasKey(this.GET_RAM)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_RAM, null);
      this.transferState.remove(this.GET_RAM); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.post<any>(`${this.resourceUrl}/web/reve/portal/providers/products/services/memory`, obj, { context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_RAM, data);
          }
        })
      );
    }
  }

  getRegions(serviceName, instance_family, vCPU, ram): Observable<any> {
    let obj = {
      "SERVICE_NAME": serviceName,
      "INSTANCE_FAMILY": Array.isArray(instance_family) ? instance_family : [instance_family],
      "VCPU": Array.isArray(vCPU) ? vCPU : [vCPU],
      "RAM": Array.isArray(ram) ? ram : [ram]
    }
    if (this.transferState.hasKey(this.GET_REGIONS)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_REGIONS, null);
      this.transferState.remove(this.GET_REGIONS); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.post<any>(`${this.resourceUrl}/web/reve/portal/providers/products/services/regions`, obj, { context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_REGIONS, data);
          }
        })
      );
    }
  }

  getOperatingSystems(obj): Observable<any> {
    if (this.transferState.hasKey(this.GET_OPERATING_SYSTEMS)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_OPERATING_SYSTEMS, null);
      this.transferState.remove(this.GET_OPERATING_SYSTEMS); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.post<any>(`${this.resourceUrl}/web/reve/portal/providers/products/services/os-family`, obj, { context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_OPERATING_SYSTEMS, data);
          }
        })
      );
    }
  }

  getHosts(obj): Observable<any> {
    if (this.transferState.hasKey(this.GET_HOSTS)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_HOSTS, null);
      this.transferState.remove(this.GET_HOSTS); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.post<any>(`${this.resourceUrl}/web/reve/portal/providers/products/services/host`, obj, { context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_HOSTS, data);
          }
        })
      );
    }
  }

  getFilters(obj): Observable<any> {
    if (this.transferState.hasKey(this.GET_FILTERS)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_FILTERS, null);
      this.transferState.remove(this.GET_FILTERS); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.post<any>(`${this.resourceUrl}/web/reve/portal/providers/products/services/filters`, obj, { context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_FILTERS, data);
          }
        })
      );
    }
  }

  getPricingData(obj: any): Observable<any> {
    if (this.transferState.hasKey(this.GET_PRICING_DATA)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_PRICING_DATA, null);
      this.transferState.remove(this.GET_PRICING_DATA); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.post<any>(`${this.resourceUrl}/web/reve/portal/providers/products/services/pricing/index`, obj, { context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_PRICING_DATA, data);
          }
        })
      );
    }
  }

  getAutofillList(value: string): Observable<any> {
    if (this.transferState.hasKey(this.GET_AUTOFILL_LIST)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_AUTOFILL_LIST, null);
      this.transferState.remove(this.GET_AUTOFILL_LIST); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.get<any>(`${this.resourceUrl}/web/reve/portal/providers/products/services/autofill?QUERY=${value}&LIMIT=5`, { context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_AUTOFILL_LIST, data);
          }
        })
      );
    }
  }

  getResource(id): Observable<any> {
    if (this.transferState.hasKey(this.GET_RESOURCE)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_RESOURCE, null);
      this.transferState.remove(this.GET_RESOURCE); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.get<any>(`${this.resourceUrl}/web/reve/portal/providers/products/services/resource/details?ID=${id}`, { context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_RESOURCE, data);
          }
        })
      );
    }
  }

  getResourceDetails(obj: any): Observable<any> {
    if (this.transferState.hasKey(this.GET_RESOURCE_DETAILS)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_RESOURCE_DETAILS, null);
      this.transferState.remove(this.GET_RESOURCE_DETAILS); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.post<any>(`${this.resourceUrl}/web/reve/portal/providers/products/services/resource/details/external`, obj, { context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_RESOURCE_DETAILS, data);
          }
        })
      );
    }
  }

  getRegionalPricing(obj: any): Observable<any> {
    if (this.transferState.hasKey(this.GET_REGIONAL_PRICING)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_REGIONAL_PRICING, null);
      this.transferState.remove(this.GET_REGIONAL_PRICING); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.post<any>(`${this.resourceUrl}/web/reve/portal/providers/products/services/pricing/index/region`, obj, { context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_REGIONAL_PRICING, data);
          }
        })
      );
    }
  }

  getSimilarDeals(obj: any): Observable<any> {
    if (this.transferState.hasKey(this.GET_SIMILAR_DEALS)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_SIMILAR_DEALS, null);
      this.transferState.remove(this.GET_SIMILAR_DEALS); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.post<any>(`${this.resourceUrl}/web/reve/portal/providers/products/services/pricing/index/similar`, obj, { context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_SIMILAR_DEALS, data);
          }
        })
      );
    }
  }

  getCompareResources(obj: any): Observable<any> {
    if (this.transferState.hasKey(this.GET_COMPARE_RESOURCES)) {
      // ✅ On browser: read from TransferState (SSR result)
      const items = this.transferState.get(this.GET_COMPARE_RESOURCES, null);
      this.transferState.remove(this.GET_COMPARE_RESOURCES); // cleanup after hydration
      return of(items);
    }
    else {
      return this.http.post<any>(`${this.resourceUrl}/web/reve/portal/providers/products/services/resources/compare`, obj, { context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
        tap((data) => {
          if (isPlatformServer(this.platformId)) {
            this.transferState.set(this.GET_COMPARE_RESOURCES, data);
          }
        })
      );
    }
  }

  async getAllBlogs() {
    return storyblok.get('cdn/stories', {
      starts_with: 'blogs/',
      version: 'published',
      per_page: 100
    });
  }

  async getBlog(slug: string) {
    return storyblok.get(`cdn/stories/blogs/${slug}`, {
      version: 'published'
    });
  }

}