import { Injectable } from '@angular/core';
import { BehaviorSubject, ReplaySubject, Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SearchStateService {
  private searchKeyword: string = '';
  private searchFormState: any = null;
  private categoryProductsMapState: { [index: number]: any[] } = {};
  private categoryInstancesMapState: { [index: number]: any[] } = {};
  private vCPUMapState: { [index: number]: any[] } = {};
  private RAMMapState: { [index: number]: any[] } = {};
  private regionsMapState: { [index: number]: any[] } = {};
  private citiesMapState: { [index: number]: any[] } = {};
  private operatingSystemsMapState: { [index: number]: any[] } = {};
  private hostTypesMapState: { [index: number]: any[] } = {};
  private currentSearchResults: any[] = [];
  private showFilterState: boolean = false;
  private bestDealRequestedSubject = new ReplaySubject<void>(1);
  bestDealRequested$ = this.bestDealRequestedSubject.asObservable();
  private clearFiltersRequestedSubject = new Subject<void>();
  clearFiltersRequested$ = this.clearFiltersRequestedSubject.asObservable();
  public filterSidebarVisible$ = new BehaviorSubject<boolean>(false);
  public cartSidebarVisible$ = new BehaviorSubject<boolean>(false);
  public showFilterCard$ = new BehaviorSubject<boolean>(false);

  constructor() { }

  // Save the entire search panel state
  saveSearchState(searchKeyword: string, formValue: any, categoryProductsMap: { [index: number]: any[] }, categoryInstancesMap: { [index: number]: any[] }, vCPUMap: { [index: number]: any[] }, RAMMap: { [index: number]: any[] }, regionsMap: { [index: number]: any[] }, citiesMap: { [index: number]: any[] }, operatingSystemsMap: { [index: number]: any[] }, hostTypes: { [index: number]: any[] }, showFilter?: boolean) {
    this.searchKeyword = searchKeyword;
    this.searchFormState = formValue;
    this.categoryProductsMapState = { ...categoryProductsMap };
    this.categoryInstancesMapState = { ...categoryInstancesMap };
    this.vCPUMapState = { ...vCPUMap };
    this.RAMMapState = { ...RAMMap };
    this.regionsMapState = { ...regionsMap };
    this.citiesMapState = { ...citiesMap };
    this.operatingSystemsMapState = { ...operatingSystemsMap };
    this.hostTypesMapState = { ...hostTypes };
    if (showFilter !== undefined) {
      this.showFilterState = showFilter;
      this.showFilterCard$.next(showFilter);
    }
  }

  getSearchKeyword() {
    return this.searchKeyword;
  }

  // Get the saved form state
  getSearchFormState() {
    return this.searchFormState;
  }

  // getCategoryServicesMap() {
  //   return this.categoryServicesMapState;
  // }

  getCategoryProductsMap() {
    return this.categoryProductsMapState;
  }

  getCategoryInstancesMap() {
    return this.categoryInstancesMapState;
  }

  getvCPUMap() {
    return this.vCPUMapState;
  }

  getRAMMap() {
    return this.RAMMapState;
  }

  getRegionsMap() {
    return this.regionsMapState;
  }

  getCitiesMap() {
    return this.citiesMapState;
  }

  getOperatingSystemsMap() {
    return this.operatingSystemsMapState;
  }

  getHostTypesMap() {
    return this.hostTypesMapState;
  }

  // Check if we have saved state
  hasState(): boolean {
    return this.searchFormState !== null;
    // return this.searchKeyword !== '';
  }

  // Clear the state if needed
  clearState() {
    this.searchKeyword = '';
    this.searchFormState = null;
    // this.categoryServicesMapState = {};
    this.categoryProductsMapState = {};
    this.categoryInstancesMapState = {};
    this.vCPUMapState = {};
    this.RAMMapState = {};
    this.regionsMapState = {};
    this.citiesMapState = {};
    this.operatingSystemsMapState = {};
    this.hostTypesMapState = {};
    this.currentSearchResults = [];
    this.showFilterState = false;
    this.showFilterCard$.next(false);
  }

  requestBestDeal() {
    this.bestDealRequestedSubject.next();
  }

  // Method to request filter clearing
  requestFilterClear() {
    this.clearFiltersRequestedSubject.next();
  }

  showFilterSidebar() {
    this.filterSidebarVisible$.next(true);
    this.cartSidebarVisible$.next(false);
  }

  hideFilterSidebar() {
    this.filterSidebarVisible$.next(false);
  }

  showCartSidebar() {
    this.cartSidebarVisible$.next(true);
    this.filterSidebarVisible$.next(false);
  }

  hideCartSidebar() {
    this.cartSidebarVisible$.next(false);
  }

  setShowFilterCard(show: boolean) {
    this.showFilterState = show;
    this.showFilterCard$.next(show);
  }

  getShowFilterCard(): boolean {
    return this.showFilterState;
  }

}