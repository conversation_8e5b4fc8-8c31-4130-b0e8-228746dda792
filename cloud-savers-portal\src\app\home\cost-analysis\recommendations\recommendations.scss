.right-bottom-section {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 25px;
    width: 100%;
}

.header-row {
    color: #000000;
    font-size: 16px;
    font-weight: 500;
}

.recommendations {
    background: white;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgb(0 0 0 / 0.07);
    padding: 20px;
    width: 100%;

    .section {
        display: flex;
        flex-direction: column;
        gap: 15px;

        .recommend-row {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: start;
            gap: 15px;

            .recommend-title {
                display: flex;
                flex-direction: column;
                gap: 5px;

                .title {
                    font-weight: 500;
                    font-size: 14px;
                    color: #1F2937;
                }

                .desc {
                    font-size: 12px;
                    color: #4B5563;
                }
            }
        }

        .btn-custom-recommendations {
            border: 1px solid #35A5DB;
            border-radius: 8px;
            color: #35A5DB;
            font-weight: 400;
            font-size: 16px;
            padding: 7px 15px;
            height: auto;
            width: 100%;
            line-height: 24px;
            text-align: center;
        }
    }
}

.case-study-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgb(0 0 0 / 0.07);
    width: 100%;
    display: flex;
    flex-direction: column;

    .case-img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
    }

    .case-study {
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap: 15px;

        .case-desc {
            font-size: 14px;
            color: #4B5563;
            line-height: 20px;
        }

        .case-points {
            display: flex;
            justify-content: start;
            align-items: center;
            gap: 10px;

            .content {
                background-color: #DBEAFE;
                border-radius: 50%;
                font-size: 15px;
                font-weight: 600;
                color: #2563EB;
                padding: 5px 8px;
            }

            .case-features {
                font-size: 14px;
                line-height: 20px;
                color: #000000;
            }
        }

        .case-btn {
            border: 1px solid #D1D5DB;
            border-radius: 8px;
            color: #374151;
            font-weight: 400;
            font-size: 16px;
            padding: 7px 15px;
            height: auto;
            width: 100%;
            line-height: 24px;
            text-align: center;
        }
    }
}

.help-container {
    background: linear-gradient(90deg, #35A5DB 0%, #2563EB 100%);
    border-radius: 8px;
    padding: 20px;
    width: 100%;

    .section {
        display: flex;
        flex-direction: column;
        gap: 15px;

        .help-head {
            display: flex;
            justify-content: start;
            color: #FFFFFF;
            font-size: 16px;
            font-weight: 500;
            align-items: center;
        }

        .help-desc {
            color: #FFFFFF;
            font-size: 14px;
            line-height: 20px;
        }

        .help-btn {
            border: none;
            background-color: #FFFFFF;
            border-radius: 8px;
            color: #2563EB;
            font-weight: 400;
            font-size: 16px;
            padding: 7px 15px;
            height: auto;
            width: 100%;
            line-height: 24px;
            text-align: center;
        }
    }
}