<!-- <div class="custom-overlay" *ngIf="loading">
  <div class="custom-overlay__inner">
      <div class="custom-loader">
          <svg class="progress-circle" viewBox="0 0 50 50">
              <circle class="progress-bg" cx="25" cy="25" r="20" />
              <circle class="progress-bar" cx="25" cy="25" r="20" />
          </svg>
          <div class="loader-title">Loading data</div>
          <div class="loader-desc">Please wait...</div>
      </div>
  </div>
</div> -->

<div class="mobile-bottom-nav">
  <button mat-button (click)="backToHome()" class="nav-button" aria-label="Home">
    <div class="button-content">
      <iconify-icon icon="material-symbols-light:home-outline-rounded" width="24" height="24"></iconify-icon>
      <span>Home</span>
    </div>
  </button>
  <button mat-button (click)="goToCart()" class="nav-button cart-button" aria-label="Cart"
    [class.active]="showCartPanel">
    <div class="button-content">
      <iconify-icon icon="humbleicons:cart" width="24" height="24"></iconify-icon>
      <span>Cart</span>
    </div>
    <span class="cart-badge" *ngIf="!isCartLoading && cartItemCount > 0" aria-hidden="true">{{ cartItemCount }}</span>
    <span class="sr-only" *ngIf="!isCartLoading && cartItemCount > 0">{{ cartItemCount }} items in cart</span>
    <mat-spinner *ngIf="isCartLoading" diameter="16" class="cart-loader">Please wait...</mat-spinner>
  </button>
  <!-- <button mat-button (click)="goToFilter()" class="nav-button" aria-label="Filters"> 
    -->
  <button mat-button (click)="goToFilter()" class="nav-button" [class.active]="showFilterPanel"
    aria-label="Filters" [disabled]="!isCostAnalysisPage">
    <div class="button-content">
      <iconify-icon icon="mage:filter" width="24" height="24"
        [class.icon-disabled]="!isCostAnalysisPage"></iconify-icon>
      <span>Filters</span>
    </div>
  </button>
</div>

<!-- Cart Side Panel -->
<div class="cart-side-panel" [class.show]="showCartPanel">
  <div class="cart-header">
    <h2>Your Cart</h2>
    <button mat-icon-button (click)="closeCartPanel()" class="close-button" aria-label="Close cart">
      <mat-icon>close</mat-icon>
    </button>
  </div>
  <div class="cart-content">
    <app-cart #cartComponent *ngIf="showCartPanel" (loadingChange)="onCartLoading($event)"></app-cart>
    <div class="loading-spinner" *ngIf="isCartLoading">
      <mat-spinner diameter="40"></mat-spinner>
    </div>
  </div>
</div>

<!-- Filter Side Panel -->
<!-- <div class="cart-side-panel filter-panel" [class.show]="showFilterPanel">
  <div class="cart-header">
    <h2>Filters</h2>
    <button mat-icon-button (click)="closeFilterPanel()" class="close-button" aria-label="Close filters">
      <mat-icon>close</mat-icon>
    </button>
  </div>
  <div class="cart-content">
    <app-filters-sidebar #filterComponent *ngIf="showFilterPanel"></app-filters-sidebar>
  </div>
</div> -->
<!-- </div> -->

<!-- <div class="filter-side-panel" [class.show]="showFilterPanel">
  <div class="filter-header">
    <h2>Filters</h2>
    <button mat-icon-button (click)="closeFilterPanel()" class="close-button" aria-label="Close filters">
      <mat-icon>close</mat-icon>
    </button>
  </div>
  <div class="filter-content">
    <app-filters-sidebar #filterComponent *ngIf="showFilterPanel" (loadingChange)="onFilterLoadingChange($event)"></app-filters-sidebar>
  </div>
</div> -->
<div class="overlay" *ngIf="showCartPanel" (click)="closeCartPanel()"></div>
<!-- <div class="overlay" *ngIf="showCartPanel || showFilterPanel" 
     (click)="closeCartPanel(); closeFilterPanel()">
</div> -->