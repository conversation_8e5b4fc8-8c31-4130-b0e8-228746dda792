// 

// .privacy-policy {
//     display: flex;
//     flex-direction: column;
// }

// .policy-head {
//     padding: 30px;
//     display: flex;
//     justify-content: space-between;
//     align-items: center;
// }

// .title {
//     font-size: 20px;
//     font-weight: 700;
//     color: #111827;
// }

// .close-icon {
//     border-radius: 50%;
//     background: #f3f3f3;
//     color: #373737 !important;
//     padding: 6px;
//     height: 30px;
//     width: 30px;
// }

// .policy-content {
//     display: flex;
//     flex-direction: column;
//     gap: 10px;
//     padding: 30px;
//     font-size: 16px;
//     line-height: 24px;
//     color: #000000;
//     overflow-y: scroll;
//     overflow-x: hidden;
//     -ms-overflow-style: none;
//     scrollbar-width: none;
//     height: auto;
//     max-height: 350px;

//     .policy-title {
//         font-weight: 700;
//     }
// }

// .policy-content ::-webkit-scrollbar {
//     display: none;
// }

.back-arrow-div {
  display: flex;
  align-items: baseline;
  padding: 20px 50px;
}

.back-arrow {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  color: #374151;
  cursor: pointer;
  text-align: start;
  width: 10%;
}

.terms-container {
  padding: 0 20px;
  background-color: #FFFFFF;
}

.terms-header {
  text-align: center;
  padding: 40px 0;
  margin-bottom: 40px;
}

.container-header {
  width: 90%;
}

.terms-title {
  font-size: 34px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #111827;
}

.terms-subtitle {
  font-size: 20px;
  color: #4B5563;
  line-height: 28px;
  margin: 0 0 16px 0;
  font-weight: 400;
}

.last-updated {
  font-size: 14px;
  color: #888;
  font-weight: 400;
}

.terms-content {
  padding: 20px 18%;
}

.info-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: #F9FAFB;
  padding: 15px;
  border-radius: 16px;
  margin-bottom: 15px;
}

.info-items.sub {
  background: #EFF6FF;
}

.info-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 0 2px 0;
}

.item-icon {
  margin-right: 12px;
  margin-top: 2px;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  color: #007bff;
}

.item-content {
  flex: 1;
}

.item-description {
  font-size: 14px !important;
  color: #4B5563;
  margin: 0;
  line-height: 20px;
  font-weight: 400;
}

/* Table of Contents */
.table-of-contents {
  flex: 0 0 300px;
  background-color: #FFFFFF;
  padding: 24px;
  border-radius: 8px;
  top: 20px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  margin: 0 10px;
}

.table-of-contents h2 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 20px 0;
  color: #1a1a1a;
}

.toc-grid {
  display: flex;
  padding: 0px 20px;
  gap: 55px;
}

.toc-column {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.toc-link {
  color: #3b82f6;
  text-decoration: none;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.toc-link:hover {
  background: #dbeafe;
  color: #1d4ed8;
}

/* Main Content */
// .main-content {
//   // flex: 1;
//   // min-width: 0;
//   // background: #FFFFFF;
//   padding: 50px 50px;
//   margin: 15px 10px;
//   // border-radius: 8px;
//   // box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
// }

.content-section {
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 1px solid #e5e7eb;

  ul {
    margin: 12px 0;
    padding-left: 20px;

    li {
      font-size: 14px;
      color: #4B5563;
      margin-bottom: 8px;
      line-height: 1.5;
    }
  }
}

.content-section p {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #374151;
}

.content-section:last-child {
  border-bottom: none;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.icons {
  margin-top: -28px;
  color: #007bff;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 15px 0;
  padding-bottom: 10px;
  line-height: 32px;
}

.subsection {
  margin-bottom: 25px;
  margin-left: 32px;
}

.subsection p {
  font-size: 16px;
  line-height: 24px;
  color: #374151;
  font-weight: 400;
}

.subsection-title {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 15px 0;
  line-height: 28px;
}

.sub-desc {
  margin-bottom: 20px;
}

.requirement-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 12px 16px;
  background-color: #F9FAFB;
  border-radius: 16px;
}

.requirement-item span {
  font-size: 16px;
  color: #374151;
  line-height: 24px;
  font-weight: 400;
}

.requirement-icon {
  color: #007bff;
  margin-right: 12px;
}

.info-title {
  font-size: 16px;
  font-weight: 500;
  color: #111827;
  margin: 0 0 4px 0;
}

.sub-description {
  background: #F0FDF4;
  padding: 16px;
  border-radius: 16px;
}

.bullet-list {
  margin: 15px 0;
  padding-left: 20px;
}

.bullet-list li {
  margin-bottom: 8px;
  color: #374151;
  list-style: none;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
}

.contact-details {
  background: #F9FAFB;
  padding: 20px;
  border-radius: 8px;
  margin-top: 15px;
}

.email-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
}

.email-link:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media only screen and (max-width: 1250px) {

  .main-content {
    padding: 20px;
    margin: 0px;
    box-shadow: none;
  }

  .terms-content {
    padding: 0 20px;
    background: #FFFFFF;
  }

  .container-header {
    width: 100%;
  }

  .terms-header {
    padding: 20px 0;
    margin-bottom: 0;
  }

  .section-title {
    font-size: 20px;
    padding-bottom: 0px;
  }

  .subsection-title {
    font-size: 16px;
    color: #374151;
    margin: 0px;
  }

  .subsection {
    margin-left: 17px;
  }

  .info-items {
    gap: 2px;
    background: #FFFFFF;
    padding: 0px;
  }

  .info-items.sub {
    background: #FFFFFF;
  }

  .info-item {
    padding: 12px 0 2px 0;
  }

  .sub-description {
    background: #FFFFFF;
    padding: 0px;
  }

  .main-title {
    font-size: 2rem;
  }

  .toc-grid {
    grid-template-columns: 1fr;
  }

  .info-columns {
    grid-template-columns: 1fr;
  }

  .back-arrow-div {
    padding: 20px;
    gap: 20px;
  }

}