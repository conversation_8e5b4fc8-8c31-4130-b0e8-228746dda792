.container {
    width: 100%;
    height: auto;
    background-image: url('../../../assets/images/background_img.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.head-row {
    display: flex;
    align-items: center;
    padding: 20px 50px;
    background-color: #FFFFFF;
    border-bottom: 1px solid #E5E7EB;

    .back-arrow {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 16px;
        color: #374151;
        cursor: pointer;
        text-align: start;
        width: 13%;

        iconify-icon {
            font-size: 24px;
        }
    }

    .pricing-head {
        font-size: 20px;
        font-weight: 600;
        color: #000000;
        text-align: center;
        width: 72%;
    }
}

.cart-content {
    display: flex;
    gap: 30px;
    margin-top: 20px;
    padding: 30px 50px;
    flex-direction: row;
    width: 100%;
    justify-content: space-between;

    .cart-items {
        width: 70%;
    }

    .cart-item {
        display: flex;
        align-items: flex-start;
        gap: 24px;
        background: #fff;
        border-radius: 12px;
        margin-bottom: 18px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
        padding: 24px;

        .image-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
            align-items: center;
        }

        .reve-image {
            width: 60px;
            height: 56px;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
                max-width: 80%;
                max-height: 80%;
            }
        }

        .item-image {
            width: 56px;
            height: 56px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f6f8fa;
            border-radius: 8px;

            img {
                max-width: 80%;
                max-height: 80%;
            }
        }

        .item-details {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;

            .item-header {
                align-items: center;
                gap: 8px;

                .item-name {
                    font-weight: 600;
                    font-size: 16px;
                }
            }

            .item-provider {
                font-size: 14px;
                color: #6B7280;
            }

            .item-tags-row {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;

                .tag {
                    background: #EFF6FF;
                    color: #1D4ED8;
                    border-radius: 20px;
                    padding: 5px 10px;
                    font-size: 13px;
                    display: inline-block;
                }
            }

            .item-controls-row {
                display: flex;
                align-items: center;
                gap: 24px;
                margin-top: 12px;

                .item-qty-row {
                    display: flex;
                    align-items: center;

                    .qty-btn-minus {
                        min-width: 36px;
                        height: 36px;
                        border-top-left-radius: 6px;
                        border-bottom-left-radius: 6px;
                        border-top-right-radius: 0;
                        border-bottom-right-radius: 0;
                        font-size: 18px;
                        font-weight: 500;
                        background: #F9FAFB;
                        color: #374151;
                        border: 1px solid #D1D5DB;
                        box-shadow: none;
                        transition: border-color 0.2s;

                        &:hover {
                            border-color: #35A5DB;
                        }
                    }

                    .qty-btn-plus {
                        min-width: 36px;
                        height: 36px;
                        border-top-right-radius: 6px;
                        border-bottom-right-radius: 6px;
                        border-top-left-radius: 0;
                        border-bottom-left-radius: 0;
                        font-size: 18px;
                        font-weight: 500;
                        background: #F9FAFB;
                        color: #374151;
                        border: 1px solid #D1D5DB;
                        box-shadow: none;
                        transition: border-color 0.2s;

                        &:hover {
                            border-color: #35A5DB;
                        }
                    }

                    .item-qty {
                        font-size: 15px;
                        min-width: 48px;
                        text-align: center;
                        background: #fff;
                        border: 1px solid #E5E7EB;
                        height: 36px;
                        line-height: 36px;
                        display: inline-block;
                        border-left: none;
                        border-right: none;
                    }
                }

            }
        }

        .item-actions {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 25px;

            .item-price-details {
                display: flex;
                flex-direction: column;
                gap: 7px;
                align-items: flex-end;
            }

            .payment-option {
                font-size: 13px;
                color: #1F2937;
                background-color: #dcdcdc;
                padding: 5px 10px;
                border-radius: 20px;
                text-align: center;
            }

            .price-item {
                font-size: 14px;
                color: #6B7280;

                span {
                    font-weight: 600;
                    color: #1F2937;
                }
            }

            .item-price {
                font-weight: 600;
                font-size: 16px;
                margin-bottom: 4px;
            }

            .price-period {
                font-size: 14px;
                font-weight: 400;
                color: #6B7280;
            }

            .item-qty-row {
                display: flex;
                align-items: center;
                margin-bottom: 12px;

                .qty-btn {
                    min-width: 36px;
                    height: 36px;
                    border-radius: 6px;
                    font-size: 18px;
                    font-weight: 500;
                    background: #F6F8FA;
                    color: #374151;
                    border: 1px solid #E5E7EB;
                    box-shadow: none;
                    transition: border-color 0.2s;

                    &:hover {
                        border-color: #35A5DB;
                    }
                }

                .item-qty {
                    font-size: 15px;
                    min-width: 32px;
                    text-align: center;
                    background: #fff;
                    border: 1px solid #E5E7EB;
                    border-radius: 6px;
                    height: 36px;
                    line-height: 36px;
                    display: inline-block;
                }
            }

            .item-action-btns-opposite {
                display: flex;
                gap: 12px;
                margin-top: auto;

                .view-btn {
                    border: 1px solid #35A5DB;
                    color: #35A5DB;
                    background: transparent;
                    font-weight: 500;
                    border-radius: 8px;
                    min-width: 120px;
                }

                .remove-btn {
                    border: 1px solid #DC2626;
                    color: #DC2626;
                    background: transparent;
                    font-weight: 500;
                    border-radius: 8px;
                    min-width: 120px;
                }
            }
        }
    }

    .order-summary {
        flex: 1;
        background: white;
        border-radius: 12px;
        padding: 24px;
        height: fit-content;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        width: 30%;

        h3 {
            font-size: 18px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 15px;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            font-size: 16px;
            line-height: 24px;
            font-weight: 400;
            color: #4B5563;

            &.total {
                margin-top: 16px;
                padding-top: 16px;
                border-top: 1px solid #E5E7EB;
                font-weight: 600;
                font-size: 16px;
                color: #111827;
            }
        }

        // .promo-code {
        //     display: flex;
        //     flex-direction: column;
        //     gap: 8px;

        //     label {
        //         display: block;
        //         margin-bottom: 4px;
        //         font-weight: 500;
        //         color: #374151;
        //         font-size: 14px;
        //         line-height: 20px;
        //         letter-spacing: 0px;
        //         margin-top: 18px;
        //     }

        //     .promo-code-row {
        //         display: flex;
        //         width: 100%;

        //         input {
        //             flex: 1;
        //             padding: 10px 14px;
        //             font-size: 14px;
        //             border: 1px solid #d1d5db;
        //             border-top-left-radius: 8px;
        //             border-bottom-left-radius: 8px;
        //             border-right: none;
        //             height: 40px;
        //             margin: 0;
        //             outline: none;

        //             &::placeholder {
        //                 color: #9ca3af;
        //             }
        //         }

        //         button {
        //             background-color: #35A5DB;
        //             color: white;
        //             border: none;
        //             border-top-left-radius: 0;
        //             border-bottom-left-radius: 0;
        //             border-top-right-radius: 8px;
        //             border-bottom-right-radius: 8px;
        //             padding: 0 16px;
        //             cursor: pointer;
        //             font-weight: 500;
        //             white-space: nowrap;
        //             height: 40px;

        //             &:disabled {
        //                 opacity: 0.5;
        //                 cursor: not-allowed;
        //             }
        //         }
        //     }
        // }

        .btn-purchase {
            width: 100%;
            background-color: #2C2C2C;
            color: white;
            font-weight: 500;
            font-size: 16px;
            padding: 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin-top: 18px;
        }

        // .order-summary-actions {
        //     margin-top: 20px;
        //     display: flex;
        //     gap: 12px;

        //     button {
        //         display: flex;
        //         align-items: center;
        //         justify-content: center;
        //         gap: 8px;
        //         padding: 8px 12px;
        //         font-size: 14px;
        //         border-radius: 8px;
        //         transition: all 0.2s ease;
        //         flex: 1;

        //         &:hover {
        //             background-color: #f5f5f5;
        //         }

        //         iconify-icon {
        //             margin-bottom: -2px
        //         }

        //         &.export-btn {
        //             border: 1px solid #35A5DB;
        //             color: #35A5DB;
        //         }
        //     }
        // }
    }

    .order-summary-note {
        font-size: 12px;
        color: #6B7280;
        text-align: center;
        margin-top: 16px;
    }
}

// .special-offers-container {
//     padding: 20px 50px;
//     width: 100%;

//     h2 {
//         font-size: 20px;
//         font-weight: 700;
//         color: #111827;
//         margin-bottom: 24px;
//         line-height: 28.13px;
//         letter-spacing: 0px;
//     }

//     .special-offers-list {
//         display: flex;
//         flex-wrap: wrap;
//         gap: 20px;
//     }

//     .offer-card {
//         background: #ffffff;
//         border-radius: 12px;
//         box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08);
//         width: 285px;
//         padding: 20px;
//         display: flex;
//         flex-direction: column;
//         gap: 12px;

//         .offer-header {
//             display: flex;
//             align-items: center;
//             gap: 16px;

//             .offer-icon {
//                 width: 40px;
//                 height: 40px;
//                 object-fit: contain;
//                 border-radius: 6px;
//             }

//             .offer-title-section {
//                 display: flex;
//                 flex-direction: column;

//                 .offer-title {
//                     font-size: 16px;
//                     color: #111827;
//                     font-weight: 500;
//                     line-height: 24.11px;
//                     letter-spacing: 0px;
//                 }

//                 .offer-validity {
//                     font-size: 12px;
//                     color: #6B7280;
//                     font-weight: 400;
//                     line-height: 16.07px;
//                     letter-spacing: 0px;
//                 }
//             }
//         }

//         .offer-description {
//             font-size: 14px;
//             color: #4B5563;
//             flex-grow: 1;
//             min-height: 60px;
//             font-weight: 400;
//             line-height: 20.09px;
//             letter-spacing: 0px;
//         }

//         .offer-amount {
//             font-weight: 700;
//             color: #35A5DB;
//             padding: 6px 12px;
//             background-color: #EFF6FF;
//             border-radius: 20px;
//             width: fit-content;
//             text-align: center;
//             margin-bottom: 12px;
//             font-size: 18px;
//             line-height: 28px;
//             letter-spacing: 0px;
//         }

//         button {
//             align-self: stretch;
//             border-radius: 8px;
//             height: 40px;
//             background: #35A5DB;
//             font-weight: 400;
//             font-size: 16px;
//             line-height: 24px;
//             letter-spacing: 0px;
//             text-align: center;
//             color: #FFFFFF;
//         }
//     }

//     @media (max-width: 720px) {
//         .special-offers-list {
//             flex-direction: column;
//             width: 100%;

//             .offer-card {
//                 width: 100%;
//             }
//         }
//     }
// }

// .you-might-also-like {
//     margin-top: 30px;
//     padding: 30px 50px;
//     width: 100%;

//     h2 {
//         font-weight: 700;
//         margin-bottom: 24px;
//         color: #111827;
//         font-size: 20px;
//         line-height: 28px;
//         letter-spacing: 0px;
//     }

//     .cards-container {
//         display: flex;
//         gap: 16px;
//         flex-wrap: wrap;
//     }

//     .card {
//         background: #ffffff;
//         border: 1px solid #e4e6ef;
//         border-radius: 6px;
//         padding: 16px;
//         width: 380px;
//         box-shadow: 0 2px 6px rgb(16 38 63 / 8%);
//         display: flex;
//         flex-direction: column;
//         justify-content: space-between;
//         transition: box-shadow 0.2s ease;
//         cursor: default;

//         &:hover {
//             box-shadow: 0 8px 24px rgb(16 38 63 / 12%);
//         }
//     }

//     .card-header {
//         display: flex;
//         gap: 12px;
//         align-items: center;
//         margin-bottom: 8px;
//     }

//     .card-icon {
//         flex-shrink: 0;
//         border-radius: 6px;
//         background: #f7f9fc;
//     }

//     .card-title-container {
//         display: flex;
//         flex-direction: column;
//         overflow: hidden;
//     }

//     .card-title {
//         margin: 0;
//         color: #111827;
//         white-space: nowrap;
//         overflow: hidden;
//         text-overflow: ellipsis;
//         font-weight: 500;
//         font-size: 16px;
//         line-height: 24px;
//         letter-spacing: 0px;
//     }

//     .card-subtitle {
//         margin: 2px 0 0 0;
//         color: #6B7280;
//         font-weight: 400;
//         font-size: 12px;
//         line-height: 16px;
//         letter-spacing: 0px;
//     }

//     .card-description {
//         color: #4B5563;
//         font-size: 14px;
//         line-height: 20px;
//         margin: 8px 0 16px 0;
//         flex-grow: 1;
//     }

//     .card-price-container {
//         display: flex;
//         justify-content: space-between;
//         align-items: center;
//     }

//     .card-price {
//         color: #111827;
//         font-weight: 700;
//         font-size: 16px;
//         line-height: 24px;
//         letter-spacing: 0px;
//     }

//     .add-to-cart-btn {
//         align-self: flex-end;
//         color: #35A5DB;
//         background: transparent;
//         border: none;
//         cursor: pointer;
//         transition: color 0.2s ease;
//         padding: 0;
//         font-weight: 400;
//         font-size: 14px;
//         line-height: 20px;
//         letter-spacing: 0px;
//         text-align: center;

//         &:hover {
//             text-decoration: underline;
//         }
//     }
// }

// .featured-cloud-solutions {
//     margin-top: 30px;
//     padding: 30px 50px;
//     width: 100%;

//     h2 {
//         font-weight: 700;
//         margin-bottom: 8px;
//         color: #111827;
//         font-size: 24px;
//         line-height: 32px;
//         letter-spacing: 0px;
//     }

//     .description {
//         color: #4B5563;
//         margin-bottom: 1.75rem;
//         font-weight: 400;
//         font-size: 16px;
//         line-height: 24px;
//         letter-spacing: 0px;
//     }

//     .solutions-grid {
//         display: grid;
//         grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
//         gap: 1.5rem;
//     }

//     .solution-card {
//         background: #fff;
//         border-radius: 0.5rem;
//         box-shadow: 0 4px 12px rgb(0 0 0 / 0.05);
//         padding: 1rem 1rem 1.5rem;
//         display: flex;
//         flex-direction: column;
//         text-align: left;
//         transition: box-shadow 0.3s ease;

//         &:hover {
//             box-shadow: 0 8px 20px rgb(0 0 0 / 0.1);
//         }

//         .image-container {
//             position: relative;
//             height: 120px;
//             margin-bottom: 1rem;

//             img {
//                 max-height: 100%;
//                 width: auto;
//                 max-width: 100%;
//                 object-fit: contain;
//             }

//             .tag {
//                 position: absolute;
//                 top: -15px;
//                 right: -15.5px;
//                 padding: 0.2rem 0.6rem;
//                 border-top-right-radius: 8.04px;
//                 border-bottom-left-radius: 8.04px;
//                 color: #FFFFFF;
//                 user-select: none;
//                 font-weight: 400;
//                 font-size: 12px;
//                 line-height: 16px;
//                 letter-spacing: 0px;
//             }

//             .tag.featured {
//                 background-color: #35A5DB;
//             }

//             .tag.new {
//                 background-color: #22C55E;
//             }

//             .tag.popular {
//                 background-color: #F59E0B;
//             }

//             .tag.best-value {
//                 background-color: #A855F7;
//             }
//         }

//         h3 {
//             margin-bottom: 3px;
//             font-weight: 600;
//             font-size: 18px;
//             line-height: 28px;
//             letter-spacing: 0px;
//             color: #111827;
//         }

//         .subtitle {
//             color: #4B5563;
//             margin-bottom: 0.75rem;
//             font-weight: 400;
//             font-size: 14px;
//             line-height: 20px;
//             letter-spacing: 0px;
//         }

//         .price-container {
//             display: flex;
//             justify-content: space-between;
//             align-items: center;

//             .price {
//                 color: #35A5DB;
//                 font-weight: 700;
//                 font-size: 16px;
//                 line-height: 24px;
//                 letter-spacing: 0px;
//             }
//         }

//         .learn-more {
//             margin-top: auto;
//             color: #35A5DB;
//             text-decoration: none;
//             font-weight: 400;
//             font-size: 14px;
//             line-height: 20px;
//             letter-spacing: 0px;
//             text-align: center;
//             vertical-align: middle;

//             &:hover {
//                 text-decoration: underline;
//             }
//         }
//     }
// }


.detail-content {
    padding: 30px 50px;
    display: flex;
    flex-direction: row;
    width: 100%;
    gap: 30px;
    justify-content: space-between;

    .billing-wrapper {
        width: 70%;
        height: fit-content;
        display: flex;
        flex-direction: column;
        gap: 25px;
        background: #fff;
        border-radius: 8px;
        padding: 30px;
        box-shadow: 0 4px 12px rgb(0 0 0 / 0.1);

        h2 {
            margin-bottom: 24px;
            color: #111827;
            font-weight: 700;
            font-size: 20px;
            line-height: 28px;
            letter-spacing: 0px;
        }

        form {
            width: 100%;
        }

        .form-row {
            display: flex;
            flex-direction: column;
            margin-bottom: 16px;

            &.two-cols {
                flex-direction: row;
                gap: 20px;

                >div,
                input,
                mat-select {
                    width: 100%;
                }
            }

            .form-column {
                display: flex;
                flex-direction: column;
            }

            label {
                font-size: 14px;
                color: #374151;
                margin-bottom: 6px;
                font-weight: 500;
                line-height: 20px;
                letter-spacing: 0px;

                .required {
                    color: #f44336;
                    margin-left: 2px;
                }
            }

            input,
            mat-select {
                padding: 10px 14px;
                border-radius: 6px;
                border: 1px solid #d1d5db;
                font-size: 14px;
                height: 40px;
                transition: border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            // input:focus,
            // mat-select:focus {
            //     outline: none;
            //     border-color: #7c3aed;
            //     box-shadow: 0 0 0 3px rgb(124 58 237 / 0.3);
            // }
        }

        fieldset.billing-address {
            margin-top: 24px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 16px 20px;

            .billing-address {
                font-size: 16px;
                font-weight: 600;
                color: #374151;
                margin-bottom: 5px;
            }
        }

        .billing-address-header {
            font-size: 14px;
            color: #374151;
            margin-bottom: 6px;
            font-weight: 500;
            line-height: 20px;
            letter-spacing: 0px;

            .required {
                color: #f44336;
                margin-left: 2px;
                font-weight: 500;
            }
        }

        .error {
            font-size: 12px;
            color: #f44336;
            margin-top: 4px;
            min-height: 16px;
        }

        .btn-submit {
            background-color: #7c3aed;
            color: white;
            padding: 12px 24px;
            font-weight: 600;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease;

            &:hover {
                background-color: #5b21b6;
            }
        }
    }

    .order-wrapper {
        width: 30%;
        display: flex;
        flex-direction: column;
        gap: 20px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgb(0 0 0 / 0.1);
        padding: 20px;
        height: fit-content;

        h2 {
            font-weight: 700;
            font-size: 20px;
            line-height: 28px;
            color: #111827;
            padding: 0 0 16px 0;
            text-align: left;
        }

        .order-items {
            overflow-y: auto;
            margin: 0 -20px;
            padding: 0 20px;

            .order-item {
                display: flex;
                align-items: flex-start;
                gap: 12px;
                border-bottom: 1px solid #e5e7eb;
                padding-bottom: 12px;
                margin-bottom: 12px;

                .image-container {
                    display: flex;
                    flex-direction: column;
                    gap: 5px;
                    align-items: center;
                }

                &:last-child {
                    border-bottom: none;
                    margin-bottom: 0;
                    padding-bottom: 0;
                }

                .provider-icon {
                    width: 40px;
                    height: 40px;
                    object-fit: contain;
                    border-radius: 4px;
                }

                .order-item-details {
                    flex-grow: 1;
                    width: 50%;
                    display: flex;
                    flex-direction: column;
                    gap: 5px;

                    .item-title-row {
                        display: flex;
                        justify-content: space-between;
                        align-items: flex-start;
                        width: 100%;
                    }

                    .item-title {
                        font-weight: 500;
                        font-size: 14px;
                        line-height: 20px;
                        color: #111827;
                    }

                    .item-price {
                        font-weight: 700;
                        font-size: 14px;
                        line-height: 20px;
                        color: #111827;
                        white-space: nowrap;
                    }

                    .price-period {
                        font-size: 14px;
                        font-weight: 400;
                        color: #6B7280;
                    }

                    .item-provider {
                        font-size: 13px;
                        color: #6B7280;
                    }

                    .item-tags {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 6px;
                        margin: 5px 0;

                        .tag {
                            font-size: 12px;
                            font-weight: 400;
                            line-height: 16px;
                            padding: 4px 8px;
                            border-radius: 20px;
                            background-color: #EFF6FF;
                            color: #1D4ED8;
                            text-transform: capitalize;
                        }
                    }

                    .item-activation-row {
                        display: flex;
                        justify-content: end;
                        align-items: center;
                        width: 100%;
                        margin-top: 4px;
                    }

                    .item-activation {
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 16px;
                        color: #6b7280;
                    }

                    .item-qty {
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 16px;
                        color: #6b7280;
                        margin-left: 16px;
                        white-space: nowrap;
                    }
                }

                .order-price-details {
                    display: flex;
                    flex-direction: column;
                    gap: 7px;
                    align-items: flex-start;

                    .payment-option {
                        font-size: 12px;
                        color: #1F2937;
                        background-color: #dcdcdc;
                        padding: 4px 8px;
                        border-radius: 20px;
                        text-align: center;
                    }

                    .price-item {
                        font-size: 13px;
                        color: #6B7280;

                        span {
                            font-weight: 600;
                            color: #1F2937;
                        }
                    }
                }

                .item-right-col {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-end;
                    margin-left: 16px;
                    min-width: 80px;
                    justify-content: space-between;
                    padding: 4px 0;

                    .item-qty {
                        font-size: 12px;
                        color: #6b7280;
                        font-weight: 400;
                        line-height: 16px;
                        text-align: right;
                    }
                }
            }
        }

        .order-totals {
            display: flex;
            flex-direction: column;
            gap: 12px;
            padding: 16px 0;

            div {
                display: flex;
                justify-content: space-between;
                font-size: 16px;
                line-height: 24px;
                color: #4B5563;
                font-weight: 400;

                span:last-child {
                    color: #4B5563;
                }
            }

            .estimated-savings {

                span:last-child {
                    color: #10b981;
                }
            }
        }

        // .promo-code {
        //     display: flex;
        //     flex-direction: column;
        //     gap: 8px;

        //     label {
        //         display: block;
        //         margin-bottom: 4px;
        //         font-weight: 500;
        //         color: #374151;
        //         font-size: 14px;
        //         line-height: 20px;
        //         letter-spacing: 0px;
        //     }

        //     .promo-code-row {
        //         display: flex;
        //         width: 100%;

        //         input {
        //             flex: 1;
        //             padding: 10px 14px;
        //             font-size: 14px;
        //             border: 1px solid #d1d5db;
        //             border-top-left-radius: 8px;
        //             border-bottom-left-radius: 8px;
        //             border-right: none;
        //             height: 40px;
        //             margin: 0;
        //             outline: none;

        //             &::placeholder {
        //                 color: #9ca3af;
        //             }
        //         }

        //         button {
        //             background-color: #35A5DB;
        //             color: white;
        //             border: none;
        //             border-top-left-radius: 0;
        //             border-bottom-left-radius: 0;
        //             border-top-right-radius: 8px;
        //             border-bottom-right-radius: 8px;
        //             padding: 0 16px;
        //             cursor: pointer;
        //             font-weight: 500;
        //             white-space: nowrap;
        //             height: 40px;

        //             &:disabled {
        //                 opacity: 0.5;
        //                 cursor: not-allowed;
        //             }
        //         }
        //     }
        // }

        .total {
            display: flex;
            justify-content: space-between;
            font-size: 18px;
            font-weight: 700;
            line-height: 28px;
            color: #000000;
            padding: 16px 0 0 0;
            border-top: 1px solid #e5e7eb;
        }

        .btn-purchase {
            width: 100%;
            background-color: #2C2C2C;
            color: white;
            font-weight: 500;
            font-size: 16px;
            padding: 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px 0 16px 0;

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
        }

        .total-text {
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
            letter-spacing: 0px;
            color: #6B7280;
        }
    }
}

@media only screen and (max-width: 1250px) {
    .head-row {
        padding: 20px;
    }

    .detail-content {
        padding: 20px;
        align-items: center;
        justify-content: center;

        .billing-wrapper {
            width: 90%;
            gap: 0;
            align-items: center;
            justify-content: center;

            .form-row.two-cols {
                flex-wrap: wrap;
            }
        }

        .btn-purchase {
            width: 100%;
            background-color: #2C2C2C;
            color: white;
            font-weight: 500;
            font-size: 16px;
            padding: 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin-top: 18px;

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
        }
    }
}