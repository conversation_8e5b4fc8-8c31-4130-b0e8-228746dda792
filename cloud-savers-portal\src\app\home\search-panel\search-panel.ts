import { CommonModule, isPlatformBrowser } from '@angular/common';
import { AfterViewInit, Component, CUSTOM_ELEMENTS_SCHEMA, EventEmitter, HostListener, OnInit, Output, Inject, PLATFORM_ID } from '@angular/core';
import { MaterialModule } from '../../material-module';
import { Router } from '@angular/router';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { CloudAccounts } from '../cloud-accounts/cloud-accounts';
import { MatDialog } from '@angular/material/dialog';
import { Notification } from '../../services/notification';
import { colorObj } from '../../shared/color-object';
import { Auth } from '../../services/auth';
import { SearchStateService } from '../../services/search-state.service';
import { Login } from '../../auth/login/login';
import { CartService } from '../../services/cart-service';

@Component({
  selector: 'app-search-panel',
  imports: [CommonModule, MaterialModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './search-panel.html',
  styleUrl: './search-panel.scss'
})
export class SearchPanel implements OnInit, AfterViewInit {

  searchKeyword = new FormControl('');
  autoFillList: any[] = [];
  compareForm: FormGroup;
  loading = false;
  showFilter: boolean;
  filterData: any[] = [];
  serviceTypes: any = [];
  categoryProductsMap: { [index: number]: any[] } = {};
  categoryInstancesMap: { [index: number]: any[] } = {};
  vCPUMap: { [index: number]: any[] } = {};
  RAMMap: { [index: number]: any[] } = {};
  regionsMap: { [index: number]: any[] } = {};
  filteredRegionsMap: { [index: number]: any[] } = {};
  citiesMap: { [index: number]: any[] } = {};
  filteredCitiesMap: { [index: number]: any[] } = {};
  operatingSystemsMap: { [index: number]: any[] } = {};
  hostTypesMap: { [index: number]: any[] } = {};
  @Output() scrollToPricing = new EventEmitter<void>();
  showMoreFiltersIndices: number[] = [];
  mobile: boolean = false;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenWidth();
  }
  searchSuggestions: string[] = [
    'Virtual Machine with 4 VCPU and 8 GB RAM',
    'Virtual Machine with 8 VCPU and 16 GB RAM'
  ];

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private dialog: MatDialog,
    private router: Router,
    private authService: Auth,
    private notify: Notification,
    private fb: FormBuilder,
    private searchStateService: SearchStateService,
    private cartService: CartService
  ) {
    this.compareForm = this.fb.group({
      compareArray: fb.array([])
    })
  }

  ngOnInit() {
    this.checkScreenWidth();
    this.restoreState();
    this.getPricingFilters();
    // this.getCategories();
    this.showMoreFiltersIndices = this.authService.getShowMoreFiltersIndices();
    // Subscribe to showFilter state changes
    this.searchStateService.showFilterCard$.subscribe(show => {
      this.showFilter = show;
      // Ensure validation is applied when filter panel visibility changes
      this.ensureServiceValidation();
    });
    if (!this.searchStateService.hasState()) {
      this.addCompare();
    }
    // Ensure validation is applied after initialization
    this.ensureServiceValidation();
    this.searchKeyword.valueChanges.subscribe(value => {
      if (value && value.length > 0) {
        this.resetCompareFormArrayOnTyping();
      }
    });
  }

  ngAfterViewInit() {
    this.checkScreenWidth();
    // !this.searchKeyword.valueChanges.pipe(
    //   debounceTime(300),
    //   distinctUntilChanged()).subscribe({
    //     next: (value) => {
    //       if (value && value.length > 2) {
    //         this.getAutofillList(value);
    //       }
    //       else {
    //         this.autoFillList = [];
    //       }
    //     },
    //     error: (err) => {
    //       this.notify.showNotification(
    //         err.error.message,
    //         "top",
    //         (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
    //         err.error.status
    //       );
    //     }
    //   });
  }

  private checkScreenWidth() {
    if (isPlatformBrowser(this.platformId)) {
      if (window.innerWidth <= 1250) {
        this.mobile = true;
      } else {
        this.mobile = false;
      }
    }
  }

  // Save state before navigating
  private saveState() {
    this.searchStateService.saveSearchState(
      this.searchKeyword.value,
      this.compareForm.value,
      this.categoryProductsMap,
      this.categoryInstancesMap,
      this.vCPUMap,
      this.RAMMap,
      this.regionsMap,
      this.citiesMap,
      this.operatingSystemsMap,
      this.hostTypesMap,
      this.showFilter
    );
  }

  // Restore state from service
  private restoreState() {
    this.searchKeyword.setValue(this.searchStateService.getSearchKeyword());
    this.showFilter = this.searchStateService.getShowFilterCard();
    const savedForm = this.searchStateService.getSearchFormState();
    if (savedForm && savedForm.compareArray) {
      // Clear existing form array
      const formArray = this.compareForm.get('compareArray') as FormArray;
      while (formArray.length) {
        formArray.removeAt(0);
      }
      // Restore each form group with proper validation
      savedForm.compareArray.forEach((item: any, index: number) => {
        const fg = this.fb.group({
          selectedService: this.fb.control(item.selectedService || '', [Validators.required]),
          selectedProduct: this.fb.control(item.selectedProduct || ''),
          selectedInstance: this.fb.control(item.selectedInstance || ''),
          vCPU: this.fb.control(item.vCPU || ''),
          RAM: this.fb.control(item.RAM || ''),
          selectedRegion: this.fb.control(item.selectedRegion || ''),
          selectedCity: this.fb.control(item.selectedCity || ''),
          selectedOS: this.fb.control(item.selectedOS || ''),
          selectedHost: this.fb.control(item.selectedHost || '')
        });
        formArray.push(fg);
      });
      // Restore maps
      this.categoryProductsMap = this.searchStateService.getCategoryProductsMap();
      this.categoryInstancesMap = this.searchStateService.getCategoryInstancesMap();
      this.vCPUMap = this.searchStateService.getvCPUMap();
      this.RAMMap = this.searchStateService.getRAMMap();
      this.regionsMap = this.searchStateService.getRegionsMap();
      this.filteredRegionsMap = { ...this.regionsMap };
      this.citiesMap = this.searchStateService.getCitiesMap();
      this.filteredCitiesMap = { ...this.citiesMap };
      this.operatingSystemsMap = this.searchStateService.getOperatingSystemsMap();
      this.hostTypesMap = this.searchStateService.getHostTypesMap();
      this.ensureServiceValidation();
    }
  }

  get compareArrayLength(): number {
    return this.compareForm ? (this.compareForm.get('compareArray') as FormArray).length : 0;
  }

  isRemoveDisabled(index: number): boolean {
    return this.compareArrayLength <= 1;
  }

  getCompare() {
    return (this.compareForm.get('compareArray') as FormArray).controls;
  }

  resetCompare(index: number) {
    const formArray = this.compareForm.get('compareArray') as FormArray;
    const formGroup = formArray.at(index);
    // Reset the form group but maintain validation
    formGroup.reset();
    // Re-apply required validation to selectedService after reset
    formGroup.get('selectedService')?.setValidators([Validators.required]);
    formGroup.get('selectedService')?.updateValueAndValidity();
    // Clear associated maps
    this.categoryProductsMap[index] = [];
    this.categoryInstancesMap[index] = [];
    this.vCPUMap[index] = [];
    this.RAMMap[index] = [];
    this.regionsMap[index] = [];
    this.filteredRegionsMap[index] = [];
    this.citiesMap[index] = [];
    this.filteredCitiesMap[index] = [];
    this.operatingSystemsMap[index] = [];
    this.hostTypesMap[index] = [];
  }

  removeCompare(index: number) {
    (this.compareForm.get('compareArray') as FormArray).removeAt(index);
    // Reorganize the maps to match the new indices
    this.reorganizeMaps(index);
  }

  addCompare() {
    let codeIndex = (this.compareForm.get('compareArray') as FormArray).length;
    if (codeIndex < 20) {
      let fg = this.fb.group({
        selectedService: this.fb.control('', [Validators.required]),
        selectedProduct: this.fb.control(''),
        selectedInstance: this.fb.control(''),
        vCPU: this.fb.control(''),
        RAM: this.fb.control(''),
        selectedRegion: this.fb.control(''),
        selectedCity: this.fb.control(''),
        selectedOS: this.fb.control(''),
        selectedHost: this.fb.control('')
      });
      (this.compareForm.get('compareArray') as FormArray).push(fg);
      this.ensureServiceValidation();
    }
  }

  getPricingFilters(params?: any, index?: number) {
    let obj = params || {
      "SERVICE_TYPE": [],
      "PRODUCT_NAME": [],
      "INSTANCE_FAMILY": [],
      "VCPU_CORES": [],
      "RAM_GB": [],
      "CS_REGION": [],
      "CS_CITY": [],
      "OS_TYPE": [],
      "HOST_TYPE": []
    };
    this.authService.getFilters(obj).subscribe({
      next: (data) => {
        this.loading = false;
        this.filterData = data.object?.results[0].facet_counts || [];
        if (!params) {
          this.serviceTypes = this.filterData.find(item => item.field_name === 'service_type')?.counts.map(item => item.value) || [];
        }
        else if (index !== undefined) {
          this.populateDropdownFromResponse(params, index); // Populate next dropdown based on current selection
        }
      },
      error: (e) => {
        this.loading = false;
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        )
      }
    })
  }

  populateDropdownFromResponse(params: any, index: number) {
    // Determine which dropdown to populate next based on current parameters
    if (params.SERVICE_TYPE.length > 0 && params.PRODUCT_NAME.length === 0) {
      this.categoryProductsMap[index] = this.filterData.find(item => item.field_name === 'product_name')?.counts.map(item => item.value) || [];
    }
    else if (params.SERVICE_TYPE[0] === 'vm' && params.PRODUCT_NAME.length > 0 && params.INSTANCE_FAMILY.length === 0) {
      this.categoryInstancesMap[index] = this.filterData.find(item => item.field_name === 'instance_family')?.counts.map(item => item.value) || [];
    }
    else if (params.SERVICE_TYPE[0] !== 'vm' && params.PRODUCT_NAME.length > 0 && params.CS_REGION.length === 0) {
      this.regionsMap[index] = this.filterData.find(item => item.field_name === 'cs_region')?.counts.map(item => item.value) || [];
      this.filteredRegionsMap[index] = [...this.regionsMap[index]];
    }
    else if (params.INSTANCE_FAMILY.length > 0 && params.VCPU_CORES.length === 0) {
      let vCPUData = this.filterData.find(item => item.field_name === 'vcpu_cores')?.counts.map(item => item.value) || [];
      this.vCPUMap[index] = [...vCPUData].sort((a, b) => {
        const numA = parseFloat(a) || 0;
        const numB = parseFloat(b) || 0;
        return numA - numB;
      });
    }
    else if (params.VCPU_CORES.length > 0 && params.RAM_GB.length === 0) {
      let ramData = this.filterData.find(item => item.field_name === 'ram_gb')?.counts.map(item => item.value) || [];
      this.RAMMap[index] = [...ramData].sort((a, b) => {
        const numA = parseFloat(a) || 0;
        const numB = parseFloat(b) || 0;
        return numA - numB;
      });
    }
    else if (params.RAM_GB.length > 0 && params.CS_REGION.length === 0) {
      this.regionsMap[index] = this.filterData.find(item => item.field_name === 'cs_region')?.counts.map(item => item.value) || [];
      this.filteredRegionsMap[index] = [...this.regionsMap[index]];
    }
    else if (params.CS_REGION.length > 0 && params.CS_CITY.length === 0) {
      this.citiesMap[index] = this.filterData.find(item => item.field_name === 'cs_city')?.counts.map(item => item.value) || [];
      this.filteredCitiesMap[index] = [...this.citiesMap[index]];
    }
    else if (params.CS_CITY.length > 0 && params.OS_TYPE.length === 0) {
      this.operatingSystemsMap[index] = this.filterData.find(item => item.field_name === 'os_type')?.counts.map(item => item.value) || [];
    }
    else if (params.OS_TYPE.length > 0 && params.HOST_TYPE.length === 0) {
      const hostData = this.filterData.find(item => item.field_name === 'host_type')?.counts.map(item => item.value) || [];
      this.hostTypesMap[index] = hostData.map(item => {
        return item?.replace(/-/g, ' ')?.replace(/\b\w/g, char => char?.toUpperCase());
      });
    }
  }

  selectServiceType(serviceType: string, index: number) {
    this.searchKeyword.setValue('');
    this.resetDependentFields(index);
    const params = {
      "SERVICE_TYPE": [serviceType],
      "PRODUCT_NAME": [],
      "INSTANCE_FAMILY": [],
      "VCPU_CORES": [],
      "RAM_GB": [],
      "CS_REGION": [],
      "CS_CITY": [],
      "OS_TYPE": [],
      "HOST_TYPE": []
    };
    this.loading = true;
    this.getPricingFilters(params, index);
  }

  resetDependentFields(index: number) {
    const formArray = this.compareForm.get('compareArray') as FormArray;
    formArray.at(index).get('selectedProduct')?.reset();
    formArray.at(index).get('selectedInstance')?.reset();
    formArray.at(index).get('vCPU')?.reset();
    formArray.at(index).get('RAM')?.reset();
    formArray.at(index).get('selectedRegion')?.reset();
    formArray.at(index).get('selectedCity')?.reset();
    formArray.at(index).get('selectedOS')?.reset();
    formArray.at(index).get('selectedHost')?.reset();
    this.categoryProductsMap[index] = [];
    this.categoryInstancesMap[index] = [];
    this.vCPUMap[index] = [];
    this.RAMMap[index] = [];
    this.regionsMap[index] = [];
    this.filteredRegionsMap[index] = [];
    this.citiesMap[index] = [];
    this.filteredCitiesMap[index] = [];
    this.operatingSystemsMap[index] = [];
    this.hostTypesMap[index] = [];
  }

  selectProductName(productName: string, index: number) {
    const formArray = this.compareForm.get('compareArray') as FormArray;
    const selectedService = formArray.at(index).get('selectedService')?.value;
    formArray.at(index).get('selectedInstance')?.reset();
    formArray.at(index).get('vCPU')?.reset();
    formArray.at(index).get('RAM')?.reset();
    formArray.at(index).get('selectedRegion')?.reset();
    formArray.at(index).get('selectedCity')?.reset();
    formArray.at(index).get('selectedOS')?.reset();
    formArray.at(index).get('selectedHost')?.reset();
    const params = {
      "SERVICE_TYPE": selectedService ? [selectedService] : [],
      "PRODUCT_NAME": [productName],
      "INSTANCE_FAMILY": [],
      "VCPU_CORES": [],
      "RAM_GB": [],
      "CS_REGION": [],
      "CS_CITY": [],
      "OS_TYPE": [],
      "HOST_TYPE": []
    };
    this.loading = true;
    this.getPricingFilters(params, index);
  }

  selectInstance(instance: string, index: number) {
    const formArray = this.compareForm.get('compareArray') as FormArray;
    const selectedService = formArray.at(index).get('selectedService')?.value;
    const selectedProduct = formArray.at(index).get('selectedProduct')?.value;
    formArray.at(index).get('vCPU')?.reset();
    formArray.at(index).get('RAM')?.reset();
    formArray.at(index).get('selectedRegion')?.reset();
    formArray.at(index).get('selectedCity')?.reset();
    formArray.at(index).get('selectedOS')?.reset();
    formArray.at(index).get('selectedHost')?.reset();
    const params = {
      "SERVICE_TYPE": selectedService ? [selectedService] : [],
      "PRODUCT_NAME": selectedProduct ? [selectedProduct] : [],
      "INSTANCE_FAMILY": [instance],
      "VCPU_CORES": [],
      "RAM_GB": [],
      "CS_REGION": [],
      "CS_CITY": [],
      "OS_TYPE": [],
      "HOST_TYPE": []
    };
    this.loading = true;
    this.getPricingFilters(params, index);
  }

  selectvCPU(cpu: number, index: number) {
    const formArray = this.compareForm.get('compareArray') as FormArray;
    const selectedService = formArray.at(index).get('selectedService')?.value;
    const selectedProduct = formArray.at(index).get('selectedProduct')?.value;
    const selectedInstance = formArray.at(index).get('selectedInstance')?.value;
    formArray.at(index).get('RAM')?.reset();
    formArray.at(index).get('selectedRegion')?.reset();
    formArray.at(index).get('selectedCity')?.reset();
    formArray.at(index).get('selectedOS')?.reset();
    formArray.at(index).get('selectedHost')?.reset();
    const params = {
      "SERVICE_TYPE": selectedService ? [selectedService] : [],
      "PRODUCT_NAME": selectedProduct ? [selectedProduct] : [],
      "INSTANCE_FAMILY": selectedInstance ? [selectedInstance] : [],
      "VCPU_CORES": [cpu],
      "RAM_GB": [],
      "CS_REGION": [],
      "CS_CITY": [],
      "OS_TYPE": [],
      "HOST_TYPE": []
    };
    this.loading = true;
    this.getPricingFilters(params, index);
  }

  selectRAM(ram: number, index: number) {
    const formArray = this.compareForm.get('compareArray') as FormArray;
    const selectedService = formArray.at(index).get('selectedService')?.value;
    const selectedProduct = formArray.at(index).get('selectedProduct')?.value;
    const selectedInstance = formArray.at(index).get('selectedInstance')?.value;
    const selectedCPU = formArray.at(index).get('vCPU')?.value;
    formArray.at(index).get('selectedRegion')?.reset();
    formArray.at(index).get('selectedCity')?.reset();
    formArray.at(index).get('selectedOS')?.reset();
    formArray.at(index).get('selectedHost')?.reset();
    const params = {
      "SERVICE_TYPE": selectedService ? [selectedService] : [],
      "PRODUCT_NAME": selectedProduct ? [selectedProduct] : [],
      "INSTANCE_FAMILY": selectedInstance ? [selectedInstance] : [],
      "VCPU_CORES": selectedCPU ? [selectedCPU] : [],
      "RAM_GB": [ram],
      "CS_REGION": [],
      "CS_CITY": [],
      "OS_TYPE": [],
      "HOST_TYPE": []
    };
    this.loading = true;
    this.getPricingFilters(params, index);
  }

  selectRegion(region: string, index: number) {
    this.filteredRegionsMap[index] = [...this.regionsMap[index]];
    const formArray = this.compareForm.get('compareArray') as FormArray;
    const selectedService = formArray.at(index).get('selectedService')?.value;
    const selectedProduct = formArray.at(index).get('selectedProduct')?.value;
    const selectedInstance = formArray.at(index).get('selectedInstance')?.value;
    const selectedCPU = formArray.at(index).get('vCPU')?.value;
    const selectedRAM = formArray.at(index).get('RAM')?.value;
    formArray.at(index).get('selectedCity')?.reset();
    formArray.at(index).get('selectedOS')?.reset();
    formArray.at(index).get('selectedHost')?.reset();
    const params = {
      "SERVICE_TYPE": selectedService ? [selectedService] : [],
      "PRODUCT_NAME": selectedProduct ? [selectedProduct] : [],
      "INSTANCE_FAMILY": selectedInstance ? [selectedInstance] : [],
      "VCPU_CORES": selectedCPU ? [selectedCPU] : [],
      "RAM_GB": selectedRAM ? [selectedRAM] : [],
      "CS_REGION": [region],
      "CS_CITY": [],
      "OS_TYPE": [],
      "HOST_TYPE": []
    };
    this.loading = true;
    this.getPricingFilters(params, index);
  }

  selectCity(city: string, index: number) {
    this.filteredCitiesMap[index] = [...this.citiesMap[index]];
    const formArray = this.compareForm.get('compareArray') as FormArray;
    const selectedService = formArray.at(index).get('selectedService')?.value;
    const selectedProduct = formArray.at(index).get('selectedProduct')?.value;
    const selectedInstance = formArray.at(index).get('selectedInstance')?.value;
    const selectedCPU = formArray.at(index).get('vCPU')?.value;
    const selectedRAM = formArray.at(index).get('RAM')?.value;
    const selectedRegion = formArray.at(index).get('selectedRegion')?.value;
    formArray.at(index).get('selectedOS')?.reset();
    formArray.at(index).get('selectedHost')?.reset();
    const params = {
      "SERVICE_TYPE": selectedService ? [selectedService] : [],
      "PRODUCT_NAME": selectedProduct ? [selectedProduct] : [],
      "INSTANCE_FAMILY": selectedInstance ? [selectedInstance] : [],
      "VCPU_CORES": selectedCPU ? [selectedCPU] : [],
      "RAM_GB": selectedRAM ? [selectedRAM] : [],
      "CS_REGION": selectedRegion ? [selectedRegion] : [],
      "CS_CITY": [city],
      "OS_TYPE": [],
      "HOST_TYPE": []
    };
    this.loading = true;
    this.getPricingFilters(params, index);
  }

  selectOS(os: string, index: number) {
    const formArray = this.compareForm.get('compareArray') as FormArray;
    const selectedService = formArray.at(index).get('selectedService')?.value;
    const selectedProduct = formArray.at(index).get('selectedProduct')?.value;
    const selectedInstance = formArray.at(index).get('selectedInstance')?.value;
    const selectedCPU = formArray.at(index).get('vCPU')?.value;
    const selectedRAM = formArray.at(index).get('RAM')?.value;
    const selectedRegion = formArray.at(index).get('selectedRegion')?.value;
    const selectedCity = formArray.at(index).get('selectedCity')?.value;
    formArray.at(index).get('selectedHost')?.reset();
    const params = {
      "SERVICE_TYPE": selectedService ? [selectedService] : [],
      "PRODUCT_NAME": selectedProduct ? [selectedProduct] : [],
      "INSTANCE_FAMILY": selectedInstance ? [selectedInstance] : [],
      "VCPU_CORES": selectedCPU ? [selectedCPU] : [],
      "RAM_GB": selectedRAM ? [selectedRAM] : [],
      "CS_REGION": selectedRegion ? [selectedRegion] : [],
      "CS_CITY": selectedCity ? [selectedCity] : [],
      "OS_TYPE": [os],
      "HOST_TYPE": []
    };
    this.loading = true;
    this.getPricingFilters(params, index);
  }

  // getCategories() {
  //   this.authService.getProductCategories().subscribe({
  //     next: (data) => {
  //       this.productNames = data.object;
  //     },
  //     error: (e) => {
  //       this.notify.showNotification(
  //         e?.error?.message,
  //         "top",
  //         (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
  //         e?.error?.status
  //       )
  //     }
  //   })
  // }

  // selectCategory(category, index) {
  //   this.getCategoryServices(category, index);
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedService').reset();
  //   this.categoryServicesMap[index] = [];
  //   // (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedInstance').reset();
  //   // this.categoryInstancesMap[index] = [];
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('vCPU').reset();
  //   this.vCPUMap[index] = [];
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('RAM').reset();
  //   this.RAMMap[index] = [];
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedRegion').reset();
  //   this.regionsMap[index] = [];
  //   this.filteredRegionsMap[index] = [];
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedCity').reset();
  //   this.citiesMap[index] = [];
  //   this.filteredCitiesMap[index] = [];
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedOS').reset();
  //   this.operatingSystemsMap[index] = [];
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedHost').reset();
  //   this.hostTypesMap[index] = [];
  // }

  // getCategoryServices(categoryId, index) {
  //   this.loading = true;
  //   this.authService.getProductCategoryServices(categoryId).subscribe({
  //     next: (data) => {
  //       this.categoryServicesMap[index] = data.object;
  //       this.loading = false;
  //     },
  //     error: (e) => {
  //       this.loading = false;
  //       this.notify.showNotification(
  //         e?.error?.message,
  //         "top",
  //         (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
  //         e?.error?.status
  //       )
  //     }
  //   })
  // }

  // selectService(service, index) {
  //   this.getvCPUs(service, index);
  //   // (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedInstance').reset();
  //   // this.categoryInstancesMap[index] = [];
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('vCPU').reset();
  //   this.vCPUMap[index] = [];
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('RAM').reset();
  //   this.RAMMap[index] = [];
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedRegion').reset();
  //   this.regionsMap[index] = {};
  //   this.filteredRegionsMap[index] = {};
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedOS').reset();
  //   this.operatingSystemsMap[index] = {};
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedHost').reset();
  //   this.hostTypesMap[index] = [];
  // }

  // getCategoryInstances(serviceName, index) {
  //   this.loading = true;
  //   this.authService.getProductCategoryInstances(serviceName).subscribe({
  //     next: (data) => {
  //       this.categoryInstancesMap[index] = data.object;
  //       this.loading = false;
  //       if (this.categoryInstancesMap[index].length === 0) {
  //         (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedInstance').clearValidators();
  //         (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedInstance').updateValueAndValidity();
  //         this.getvCPUs(serviceName, [], index);
  //       }
  //       else {
  //         (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedInstance').updateValueAndValidity();
  //         (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedInstance').setValidators([Validators.required]);
  //       }
  //     },
  //     error: (e) => {
  //       this.loading = false;
  //       this.notify.showNotification(
  //         e?.error?.message,
  //         "top",
  //         (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
  //         e?.error?.status
  //       )
  //     }
  //   })
  // }

  // selectInstance(instance, index) {
  //   const serviceName = (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedService').value;
  //   this.getvCPUs(serviceName, instance, index);
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('vCPU').reset();
  //   this.vCPUMap[index] = [];
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('RAM').reset();
  //   this.RAMMap[index] = [];
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedRegion').reset();
  //   this.regionsMap[index] = {};
  //   this.filteredRegionsMap[index] = {};
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedOS').reset();
  //   this.operatingSystemsMap[index] = {};
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedHost').reset();
  //   this.hostTypesMap[index] = [];
  // }

  // getvCPUs(serviceName: string, index) {
  //   this.loading = true;
  //   this.authService.getvCPU(serviceName, []).subscribe({
  //     next: (data) => {
  //       this.vCPUMap[index] = data.object;
  //       this.loading = false;
  //     },
  //     error: (e) => {
  //       this.loading = false;
  //       this.notify.showNotification(
  //         e?.error?.message,
  //         "top",
  //         (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
  //         e?.error?.status
  //       )
  //     }
  //   })
  // }

  // selectvCPU(cpu: number, index) {
  //   const serviceName = (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedService').value;
  //   // const instance = (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedInstance').value || [];
  //   this.getRAMs(serviceName, [], cpu, index);
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('RAM').reset();
  //   this.RAMMap[index] = [];
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedRegion').reset();
  //   this.regionsMap[index] = {};
  //   this.filteredRegionsMap[index] = {};
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedOS').reset();
  //   this.operatingSystemsMap[index] = {};
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedHost').reset();
  //   this.hostTypesMap[index] = [];
  // }

  // getRAMs(serviceName: string, instance_family, vCPU: number, index) {
  //   this.loading = true;
  //   this.authService.getRAM(serviceName, instance_family, vCPU).subscribe({
  //     next: (data) => {
  //       this.RAMMap[index] = data.object;
  //       this.loading = false;
  //     },
  //     error: (e) => {
  //       this.loading = false;
  //       this.notify.showNotification(
  //         e?.error?.message,
  //         "top",
  //         (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
  //         e?.error?.status
  //       )
  //     }
  //   })
  // }

  // selectRAM(ram: number, index) {
  //   const serviceName = (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedService').value;
  //   // const instance = (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedInstance').value || [];
  //   const cpu = (this.compareForm.get('compareArray') as FormArray).at(index).get('vCPU').value;
  //   this.getRegion(serviceName, [], cpu, ram, index);
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedRegion').reset();
  //   this.regionsMap[index] = {};
  //   this.filteredRegionsMap[index] = {};
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedOS').reset();
  //   this.operatingSystemsMap[index] = {};
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedHost').reset();
  //   this.hostTypesMap[index] = [];
  // }

  showMoreFilters(index: number) {
    if (this.showMoreFiltersIndices?.includes(index)) {
      this.showMoreFiltersIndices = this.showMoreFiltersIndices.filter(i => i !== index);
    } else {
      this.showMoreFiltersIndices.push(index);
    }
    // Save the state in Auth service
    this.authService.setShowMoreFiltersIndices(this.showMoreFiltersIndices);
  }

  // getRegion(serviceName: string, instance_family, vCPU: number, ram: number, index) {
  //   this.loading = true;
  //   this.authService.getRegions(serviceName, instance_family, vCPU, ram).subscribe({
  //     next: (data) => {
  //       this.regionsMap[index] = data.object;
  //       this.filteredRegionsMap[index] = this.regionsMap[index];
  //       this.loading = false;
  //     },
  //     error: (e) => {
  //       this.loading = false;
  //       this.notify.showNotification(
  //         e?.error?.message,
  //         "top",
  //         (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
  //         e?.error?.status
  //       )
  //     }
  //   })
  // }

  filterRegion(index: number, event) {
    const search = event.target.value;
    if (!search) {
      this.filteredRegionsMap[index] = [...this.regionsMap[index]];
      return;
    }
    this.filteredRegionsMap[index] = this.regionsMap[index].filter(region =>
      region?.toLowerCase()?.includes(search?.toLowerCase())
    );
    // const filtered = {};
    // Object.keys(this.regionsMap[index] || {}).forEach(key => {
    //   if (key.toLowerCase().includes(search.toLowerCase())) {
    //     filtered[key] = this.regionsMap[index][key];
    //   }
    // });
    // this.filteredRegionsMap[index] = filtered;
  }

  filterCity(index: number, event) {
    const search = event.target.value;
    if (!search) {
      this.filteredCitiesMap[index] = [...this.citiesMap[index]];
      return;
    }
    this.filteredCitiesMap[index] = this.citiesMap[index].filter(city =>
      city?.toLowerCase()?.includes(search?.toLowerCase())
    );
  }

  // selectRegion(region, index) {
  //   this.filteredRegionsMap[index] = { ...this.regionsMap[index] };
  //   const serviceName = (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedService').value;
  //   // const instance = (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedInstance').value || [];
  //   const cpu = (this.compareForm.get('compareArray') as FormArray).at(index).get('vCPU').value;
  //   const ram = (this.compareForm.get('compareArray') as FormArray).at(index).get('RAM').value;
  //   this.getOS(serviceName, [], cpu, ram, region, index);
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedOS').reset();
  //   this.operatingSystemsMap[index] = {};
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedHost').reset();
  //   this.hostTypesMap[index] = [];
  // }

  onRegionSearchClear(index: number) {
    this.filteredRegionsMap[index] = [...this.regionsMap[index]];
  }

  onCitySearchClear(index: number) {
    this.filteredCitiesMap[index] = [...this.citiesMap[index]];
  }

  // getOS(serviceName: string, instance_family, vCPU: number, ram: number, region_group: string, index) {
  //   this.loading = true;
  //   let obj = {
  //     "SERVICE_NAME": serviceName,
  //     "INSTANCE_FAMILY": Array.isArray(instance_family) ? instance_family : [instance_family],
  //     "VCPU": Array.isArray(vCPU) ? vCPU : [vCPU],
  //     "RAM": Array.isArray(ram) ? ram : [ram],
  //     "REGION_GROUP": Array.isArray(region_group) ? region_group : [region_group]
  //   }
  //   this.authService.getOperatingSystems(obj).subscribe({
  //     next: (data) => {
  //       this.operatingSystemsMap[index] = data.object;
  //       this.loading = false;
  //     },
  //     error: (e) => {
  //       this.loading = false;
  //       this.notify.showNotification(
  //         e?.error?.message,
  //         "top",
  //         (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
  //         e?.error?.status
  //       )
  //     }
  //   })
  // }

  // selectOS(os, index) {
  //   const serviceName = (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedService').value;
  //   // const instance = (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedInstance').value || [];
  //   const cpu = (this.compareForm.get('compareArray') as FormArray).at(index).get('vCPU').value;
  //   const ram = (this.compareForm.get('compareArray') as FormArray).at(index).get('RAM').value;
  //   const region = (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedRegion').value;
  //   this.getHostTypes(serviceName, [], cpu, ram, region, os, index);
  //   (this.compareForm.get('compareArray') as FormArray).at(index).get('selectedHost').reset();
  //   this.hostTypesMap[index] = [];
  // }

  // getHostTypes(serviceName: string, instance_family, vCPU: number, ram: number, region_group: string, os: string, index) {
  //   this.loading = true;
  //   let obj = {
  //     "SERVICE_NAME": serviceName,
  //     "INSTANCE_FAMILY": Array.isArray(instance_family) ? instance_family : [instance_family],
  //     "VCPU": Array.isArray(vCPU) ? vCPU : [vCPU],
  //     "RAM": Array.isArray(ram) ? ram : [ram],
  //     "REGION_GROUP": Array.isArray(region_group) ? region_group : [region_group],
  //     "OPERATING_SYSTEM": Array.isArray(os) ? os : [os]
  //   }
  //   this.authService.getHosts(obj).subscribe({
  //     next: (data) => {
  //       this.hostTypesMap[index] = data.object;
  //       this.loading = false;
  //     },
  //     error: (e) => {
  //       this.loading = false;
  //       this.notify.showNotification(
  //         e?.error?.message,
  //         "top",
  //         (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
  //         e?.error?.status
  //       )
  //     }
  //   })
  // }

  connectCloudAccount() {
    const dialogRef = this.dialog.open(CloudAccounts, {
      width: '1000px',
      height: '550px',
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(result => {
    });
  }

  getAutofillList(value) {
    this.authService.getAutofillList(value).subscribe({
      next: (data) => {
        this.autoFillList = data.object.results[0].hits;
      },
      error: (e) => {
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        )
      }
    })
  }

  onSelectionChange(event) {
    this.searchKeyword.setValue(event.option.value);
    this.compareServices();
  }

  selectSuggestion(value) {
    this.searchKeyword.setValue(value);
    this.compareServices();
  }

  onSearchEnter() {
    if (this.searchKeyword.value && this.searchKeyword.value.length >= 2) {
      this.compareServices();
    }
  }

  resetCompareFormArrayOnTyping() {
    const formArray = this.compareForm.get('compareArray') as FormArray;
    formArray.controls.forEach((control, index) => {
      this.resetCompare(index);
    });
  }

  showFilterCard() {
    this.showFilter = !this.showFilter;
    this.searchStateService.setShowFilterCard(this.showFilter);
    // Ensure validation is maintained when toggling filter panel
    this.ensureServiceValidation();
  }

  compareServices() {
    this.ensureServiceValidation();
    this.saveState(); // Save state before navigating
    const formArrayValues = this.compareForm.value.compareArray;
    let finalArray = [];
    formArrayValues.forEach((item, index) => {
      if (!item.selectedService) return;
      let obj = {};
      if (item.selectedService) {
        obj["service_type"] = [item.selectedService];
      }
      if (item.selectedProduct) {
        obj["product_name"] = [item.selectedProduct];
      }
      if (item.selectedInstance) {
        obj["instance_family"] = [item.selectedInstance];
      }
      if (item.vCPU) {
        obj["vcpu_cores"] = [item.vCPU];
      }
      if (item.RAM) {
        obj["ram_gb"] = [item.RAM];
      }
      if (item.selectedRegion) {
        obj["cs_region"] = [item.selectedRegion];
      }
      if (item.selectedCity) {
        obj["cs_city"] = [item.selectedCity];
      }
      if (item.selectedOS) {
        obj["os_type"] = [item.selectedOS];
      }
      if (item.selectedHost) {
        obj["host_type"] = [item.selectedHost];
      }
      finalArray.push(obj);
    });
    if (!this.authService.isLoggedIn()) {
      const dialogRef = this.dialog.open(Login, {
        width: '558px',
        height: 'auto',
        disableClose: true
      });
      dialogRef.afterClosed().subscribe(result => {
        if (result !== false) {
          this.searchStateService.requestFilterClear();
          // this.authService.setFormArray(formArrayValues); // Emit values
          this.authService.setFormArray(finalArray);
          this.authService.setSearchKeyword(this.searchKeyword.value);
          this.scrollToPricing.emit();
          this.router.navigate(['/skyscanner4cloud']);
          this.searchStateService.requestBestDeal();
          if (result === true) {
            this.cartService.notifyLoginSuccess();
          }
        }
      });
    }
    else {
      this.searchStateService.requestFilterClear();
      // this.authService.setFormArray(formArrayValues);
      this.authService.setFormArray(finalArray);
      this.authService.setSearchKeyword(this.searchKeyword.value);
      this.scrollToPricing.emit();
      this.router.navigate(['/skyscanner4cloud']);
      this.searchStateService.requestBestDeal();
    }
  }

  // Add this new method to reorganize maps after removing an item
  private reorganizeMaps(removedIndex: number) {
    // Create temporary copies of the maps
    const tempCategoryProductsMap: { [index: number]: any[] } = {};
    const tempCategoryInstancesMap: { [index: number]: any[] } = {};
    const tempVCPUMap: { [index: number]: any[] } = {};
    const tempRAMMap: { [index: number]: any[] } = {};
    const tempRegionsMap: { [index: number]: any[] } = {};
    const tempFilteredRegionsMap: { [index: number]: any[] } = {};
    const tempCitiesMap: { [index: number]: any[] } = {};
    const tempFilteredCitiesMap: { [index: number]: any[] } = {};
    const tempOperatingSystemsMap: { [index: number]: any[] } = {};
    const tempHostTypesMap: { [index: number]: any[] } = {};
    // Reorganize categoryProductsMap
    Object.keys(this.categoryProductsMap).forEach(key => {
      const keyNum = parseInt(key);
      if (keyNum > removedIndex) {
        // Move items down one index
        tempCategoryProductsMap[keyNum - 1] = this.categoryProductsMap[keyNum];
      } else if (keyNum < removedIndex) {
        // Keep items at the same index
        tempCategoryProductsMap[keyNum] = this.categoryProductsMap[keyNum];
      }
    });
    this.categoryProductsMap = tempCategoryProductsMap;
    // Reorganize categoryInstancesMap
    Object.keys(this.categoryInstancesMap).forEach(key => {
      const keyNum = parseInt(key);
      if (keyNum > removedIndex) {
        tempCategoryInstancesMap[keyNum - 1] = this.categoryInstancesMap[keyNum];
      } else if (keyNum < removedIndex) {
        tempCategoryInstancesMap[keyNum] = this.categoryInstancesMap[keyNum];
      }
    });
    this.categoryInstancesMap = tempCategoryInstancesMap;
    // Reorganize vCPUMap
    Object.keys(this.vCPUMap).forEach(key => {
      const keyNum = parseInt(key);
      if (keyNum > removedIndex) {
        tempVCPUMap[keyNum - 1] = this.vCPUMap[keyNum];
      } else if (keyNum < removedIndex) {
        tempVCPUMap[keyNum] = this.vCPUMap[keyNum];
      }
    });
    this.vCPUMap = tempVCPUMap;
    // Reorganize RAMMap
    Object.keys(this.RAMMap).forEach(key => {
      const keyNum = parseInt(key);
      if (keyNum > removedIndex) {
        tempRAMMap[keyNum - 1] = this.RAMMap[keyNum];
      } else if (keyNum < removedIndex) {
        tempRAMMap[keyNum] = this.RAMMap[keyNum];
      }
    });
    this.RAMMap = tempRAMMap;
    // Reorganize regionsMap
    Object.keys(this.regionsMap).forEach(key => {
      const keyNum = parseInt(key);
      if (keyNum > removedIndex) {
        tempRegionsMap[keyNum - 1] = this.regionsMap[keyNum];
        tempFilteredRegionsMap[keyNum - 1] = this.filteredRegionsMap[keyNum];
      } else if (keyNum < removedIndex) {
        tempRegionsMap[keyNum] = this.regionsMap[keyNum];
        tempFilteredRegionsMap[keyNum] = this.filteredRegionsMap[keyNum];
      }
    });
    this.regionsMap = tempRegionsMap;
    this.filteredRegionsMap = tempFilteredRegionsMap;
    // Reorganize citiesMap
    Object.keys(this.citiesMap).forEach(key => {
      const keyNum = parseInt(key);
      if (keyNum > removedIndex) {
        tempCitiesMap[keyNum - 1] = this.citiesMap[keyNum];
        tempFilteredCitiesMap[keyNum - 1] = this.filteredCitiesMap[keyNum];
      } else if (keyNum < removedIndex) {
        tempCitiesMap[keyNum] = this.citiesMap[keyNum];
        tempFilteredCitiesMap[keyNum] = this.filteredCitiesMap[keyNum];
      }
    });
    this.citiesMap = tempCitiesMap;
    this.filteredCitiesMap = tempFilteredCitiesMap;
    // Reorganize operatingSystemsMap
    Object.keys(this.operatingSystemsMap).forEach(key => {
      const keyNum = parseInt(key);
      if (keyNum > removedIndex) {
        tempOperatingSystemsMap[keyNum - 1] = this.operatingSystemsMap[keyNum];
      } else if (keyNum < removedIndex) {
        tempOperatingSystemsMap[keyNum] = this.operatingSystemsMap[keyNum];
      }
    });
    this.operatingSystemsMap = tempOperatingSystemsMap;
    // Reorganize hostTypesMap
    Object.keys(this.hostTypesMap).forEach(key => {
      const keyNum = parseInt(key);
      if (keyNum > removedIndex) {
        tempHostTypesMap[keyNum - 1] = this.hostTypesMap[keyNum];
      } else if (keyNum < removedIndex) {
        tempHostTypesMap[keyNum] = this.hostTypesMap[keyNum];
      }
    });
    this.hostTypesMap = tempHostTypesMap;
  }

  // Ensures that all selectedProduct FormControls have required validation applied
  private ensureServiceValidation() {
    const formArray = this.compareForm.get('compareArray') as FormArray;
    if (formArray && formArray.controls) {
      formArray.controls.forEach((control, index) => {
        const selectedServiceControl = control.get('selectedService');
        if (selectedServiceControl) {
          // Always ensure required validation is set
          selectedServiceControl.setValidators([Validators.required]);
          selectedServiceControl.updateValueAndValidity();
        }
      });
    }
  }

  formatText(input: string) {
    return input?.replace(/-/g, ' ')?.replace(/\b\w/g, char => char?.toUpperCase());
  }

}