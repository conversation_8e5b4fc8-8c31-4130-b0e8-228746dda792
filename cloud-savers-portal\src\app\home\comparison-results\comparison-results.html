<div id="pageContent">
    <app-header></app-header>
    <div class="head-row">
        <div (click)="backToHome()" class="back-arrow">
            <iconify-icon icon="material-symbols:arrow-back-rounded"></iconify-icon>Back
        </div>
        <div class="pricing-head">VM Comparison Portal</div>
        <div class="comparison-count">{{getComparisonCount()}}</div>
    </div>
    <div class="comparison-table-container">
        <table class="comparison-table">
            <thead>
                <tr>
                    <th class="spec-column">Specifications</th>
                    <th *ngFor="let instance of compareInstances || []; let i = index" class="instance-column">
                        <div class="instance-header">
                            <!-- <button mat-icon-button class="remove-instance-btn" (click)="removeFromComparison(instance)"
                                aria-label="Remove instance from comparison">
                                <iconify-icon icon="material-symbols:close"></iconify-icon>
                            </button> -->
                            <div class="instnace-title">
                                <img class="provider-logo"
                                    [src]="'assets/images/' + providerIconMap[instance?.document?.service_provider | lowercase]" />
                                <div class="instance-text">
                                    <div class="instance-title">{{instance?.document?.product_name}}</div>
                                    <div class="instance-subtitle">{{instance?.document?.vm_name}}</div>
                                    <div class="instance-type">{{instance?.document?.product_type}}</div>
                                </div>
                            </div>
                            <div class="instnace-title">
                                <div class="instance-selection">
                                    <input type="radio" [id]="'instance-' + instance?.document?.record_id"
                                        name="selectedInstance" [value]="instance?.document?.record_id"
                                        [checked]="isInstanceSelected(instance)" (change)="selectInstance(instance)"
                                        class="instance-radio"
                                        [attr.aria-label]="'Select ' + (instance?.document?.product_name)">
                                    <label [for]="'instance-' + instance?.document?.record_id" class="radio-label">
                                        <span class="radio-custom"></span>
                                    </label>
                                </div>
                                <div class="instance-price">Avg. ${{getMonthlyPrice(instance?.document?.hourly_rate_usd,
                                    instance?.document?.service_provider)}}/mo</div>
                            </div>
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr class="spec-group-header">
                    <td [attr.colspan]="compareInstances.length + 1">
                        <div class="group-title">
                            <iconify-icon icon="material-symbols:memory"></iconify-icon>
                            Compute Specifications
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="spec-label">vCPU</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value"
                        [ngClass]="getHighlightClass(instance, 'vcpu_cores')">
                        {{instance?.document?.vcpu_cores || '--'}} vCPU
                    </td>
                </tr>
                <tr>
                    <td class="spec-label">Architecture</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value">
                        {{instance?.document?.cpu_model || '--'}}
                    </td>
                </tr>
                <tr>
                    <td class="spec-label">Base Clock Speed</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value"
                        [ngClass]="getHighlightClass(instance, 'cpu_clock_ghz')">
                        {{instance?.document?.cpu_clock_ghz || '--'}}</td>
                </tr>
                <tr class="spec-group-header">
                    <td [attr.colspan]="compareInstances.length + 1">
                        <div class="group-title">
                            <iconify-icon icon="bx:memory-card"></iconify-icon>
                            Memory Specifications
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="spec-label">RAM</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value"
                        [ngClass]="getHighlightClass(instance, 'ram_gb')">
                        {{instance?.document?.ram_gb || '--'}} GB
                    </td>
                </tr>
                <tr class="spec-group-header">
                    <td [attr.colspan]="compareInstances.length + 1">
                        <div class="group-title">
                            <iconify-icon icon="mingcute:storage-line"></iconify-icon>
                            Storage Specifications
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="spec-label">Storage Type</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value">
                        {{instance?.document?.storage_spec || '--'}}
                    </td>
                </tr>
                <tr class="spec-group-header">
                    <td [attr.colspan]="compareInstances.length + 1">
                        <div class="group-title">
                            <iconify-icon icon="lucide:globe"></iconify-icon>
                            Networking
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="spec-label">Network Performance</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value">
                        {{instance?.document?.network_performance || '--'}}</td>
                </tr>
                <tr class="spec-group-header">
                    <td [attr.colspan]="compareInstances.length + 1">
                        <div class="group-title">
                            <iconify-icon icon="majesticons:dollar-circle-line"></iconify-icon>
                            Payment Plan
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="spec-label">Hourly Rate</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value"
                        [ngClass]="getHighlightClass(instance, 'hourly_rate_usd')">
                        ${{instance?.document?.hourly_rate_usd?.toFixed(2) || '--'}}</td>
                </tr>
                <tr>
                    <td class="spec-label">Monthly Price</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value"
                        [ngClass]="getHighlightClass(instance, 'monthly_rate_usd')">
                        <div class="reserved-info">
                            <div>${{instance?.document?.monthly_rate_usd?.toFixed(2) || '--'}}</div>
                            <div>({{formatText(instance?.document?.payment_plan)}})</div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="spec-label">Term</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value"
                        [ngClass]="getHighlightClass(instance, 'upfront_amount_usd')">
                        <div class="reserved-info">
                            <div>${{instance?.document?.upfront_amount_usd?.toFixed(2) || '--'}}</div>
                            <div>({{formatText(instance?.document?.term)}})</div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="spec-label">Plan</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value">
                        <div class="reserved-info">
                            <div>{{formatText(instance?.document?.payment_option)}}</div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="spec-label">Average Monthly Price</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value"
                        [ngClass]="getHighlightClass(instance, 'avg_monthly_price')">
                        <div class="reserved-info">
                            <div>${{getMonthlyPrice(instance?.document?.hourly_rate_usd,
                                instance?.document?.service_provider)}}</div>
                        </div>
                    </td>
                </tr>
                <tr class="spec-group-header" *ngIf="hasGpuData()">
                    <td [attr.colspan]="compareInstances.length + 1">
                        <div class="group-title">
                            <iconify-icon icon="famicons:play-forward-outline"></iconify-icon>
                            GPU Specification
                        </div>
                    </td>
                </tr>
                <tr *ngIf="hasGpuData()">
                    <td class="spec-label">GPU model</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value">
                        {{instance?.document?.gpu_model || '--'}}
                    </td>
                </tr>
                <tr *ngIf="hasGpuData()">
                    <td class="spec-label">GPU count</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value"
                        [ngClass]="getHighlightClass(instance, 'gpu_count')">
                        {{instance?.document?.gpu_count || '--'}}
                    </td>
                </tr>
                <tr *ngIf="hasGpuData()">
                    <td class="spec-label">GPU memory info</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value"
                        [ngClass]="getHighlightClass(instance, 'gpu_memory_mb')">
                        {{instance?.document?.gpu_memory_mb || '--'}}
                    </td>
                </tr>
                <tr class="spec-group-header">
                    <td [attr.colspan]="compareInstances.length + 1">
                        <div class="group-title">
                            <iconify-icon icon="material-symbols:check-box-outline"></iconify-icon>
                            Feature Comparison
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="spec-label">Auto Scaling</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value feature-value">
                        <iconify-icon [icon]="getFeatureIcon(instance?.document?.auto_scaling)"
                            [class]="getFeatureClass(instance?.document?.auto_scaling)">
                        </iconify-icon>
                    </td>
                </tr>
                <tr>
                    <td class="spec-label">Load Balancer</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value feature-value">
                        <iconify-icon [icon]="getFeatureIcon(instance?.document?.load_balancer)"
                            [class]="getFeatureClass(instance?.document?.load_balancer)">
                        </iconify-icon>
                    </td>
                </tr>
                <tr>
                    <td class="spec-label">Backup Service</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value feature-value">
                        <iconify-icon [icon]="getFeatureIcon(instance?.document?.backup_service)"
                            [class]="getFeatureClass(instance?.document?.backup_service)">
                        </iconify-icon>
                    </td>
                </tr>
                <tr>
                    <td class="spec-label">Monitoring</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value feature-value">
                        <iconify-icon [icon]="getFeatureIcon(instance?.document?.monitoring)"
                            [class]="getFeatureClass(instance?.document?.monitoring)">
                        </iconify-icon>
                    </td>
                </tr>
                <tr>
                    <td class="spec-label">Public IP</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value feature-value">
                        <iconify-icon [icon]="getFeatureIcon(instance?.document?.public_ip)"
                            [class]="getFeatureClass(instance?.document?.public_ip)">
                        </iconify-icon>
                    </td>
                </tr>
                <tr>
                    <td class="spec-label">24/7 Support</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value feature-value">
                        <iconify-icon [icon]="getFeatureIcon(instance?.document?.support_24_7)"
                            [class]="getFeatureClass(instance?.document?.support_24_7)">
                        </iconify-icon>
                    </td>
                </tr>
                <tr>
                    <td class="spec-label">SLA Guarantee</td>
                    <td *ngFor="let instance of compareInstances || []" class="spec-value"
                        [ngClass]="getHighlightClass(instance, 'sla_guarantee')">
                        <!-- {{instance?.document?.sla_guarantee || '--'}}% --> 99.99%
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="selection-footer" [class.footer-visible]="selectedInstance" *ngIf="selectedInstance">
        <div class="footer-content">
            <div class="footer-left">
                <div class="selected-instance-info">
                    <img class="footer-provider-logo"
                        [src]="'assets/images/' + providerIconMap[selectedInstance?.service_provider | lowercase]" />
                    <div class="instance-details">
                        <div class="instance-name">{{selectedInstance?.product_name}}</div>
                        <div class="instance-specs">{{selectedInstance?.product_type}}</div>
                    </div>
                </div>
                <div class="pricing-info">
                    <div class="monthly-cost">
                        <div class="cost-label">Total Average Monthly Cost:</div>
                        <div class="cost-value">${{getTotalMonthlyCost()}}</div>
                    </div>
                    <div class="monthly-cost" *ngIf="getBestValue()">
                        <div class="cost-label">Best Value:</div>
                        <div class="best-value">{{getBestValue()}}</div>
                    </div>
                </div>
            </div>
            <div class="footer-actions">
                <button mat-button class="footer-btn secondary" (click)="viewSelectedDetails()">
                    View Details
                </button>
                <button mat-button class="footer-btn primary" (click)="addSelectedToCart()">
                    Add to Cart
                </button>
                <button mat-icon-button class="close-footer-btn" (click)="clearSelection()"
                    aria-label="Clear selection">
                    <iconify-icon icon="material-symbols:close"></iconify-icon>
                </button>
            </div>
        </div>
    </div>
    <!-- Footer spacer when footer is visible -->
    <div class="footer-spacer" *ngIf="selectedInstance"></div>
</div>