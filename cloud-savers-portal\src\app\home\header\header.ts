import { CommonModule, isPlatformBrowser } from '@angular/common';
import { ChangeDetectorRef, Component, CUSTOM_ELEMENTS_SCHEMA, HostListener, Inject, PLATFORM_ID } from '@angular/core';
import { MaterialModule } from '../../material-module';
import { MatDialog } from '@angular/material/dialog';
import { Login } from '../../auth/login/login';
import { CartService } from '../../services/cart-service';
import { environment } from '../../../environments/environment';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { colorObj } from '../../shared/color-object';
import { Notification } from '../../services/notification';
import { CookiePolicy } from '../cookie-policy/cookie-policy';

@Component({
  selector: 'app-header',
  imports: [CommonModule, MaterialModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './header.html',
  styleUrl: './header.scss'
})
export class Header {

  userInfo: any;
  cookieInfo: any;
  loggedIn: boolean = false;
  public dashboardUrl = `${environment.DASHBOARD_URL}`;
  cartCount: number = 0;
  private destroy$ = new Subject<void>();
  mobile: boolean = false;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenWidth();
  }

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    public dialog: MatDialog,
    private cartService: CartService,
    private router: Router,
    private notify: Notification,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit() {
    this.checkScreenWidth();
    this.subscribeToCookies();
    this.setupLoginSuccessListener();
    if (isPlatformBrowser(this.platformId)) {
      if (window.localStorage.getItem('userLoggedIn') === 'true') {
        this.loggedIn = true;
        const userData = window.localStorage.getItem('userInfo') || '{}';
        this.userInfo = userData ? JSON.parse(userData) : null;
        this.subscribeToCartUpdates();
      }
      else {
        this.loggedIn = false;
      }
    }
  }

  ngAfterViewChecked() {
    if (isPlatformBrowser(this.platformId)) {
      if (window.localStorage.getItem('userLoggedIn') === 'true') {
        this.loggedIn = true;
        const userData = window.localStorage.getItem('userInfo') || '{}';
        this.userInfo = userData ? JSON.parse(userData) : null;
      }
      else {
        this.loggedIn = false;
      }
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private checkScreenWidth() {
    if (isPlatformBrowser(this.platformId)) {
      if (window.innerWidth <= 1250) {
        this.mobile = true;
      } else {
        this.mobile = false;
      }
    }
  }

  goToHome() {
    this.router.navigate(['/']);
  }

  subscribeToCookies() {
    if (this.router.url?.includes('legal/cookie-policy')) {
      return;
    }
    if (isPlatformBrowser(this.platformId)) {
      const cookieConsent = window.localStorage.getItem('cookieConsent') || '{}';
      this.cookieInfo = cookieConsent ? JSON.parse(cookieConsent) : null;
      if (this.cookieInfo && this.cookieInfo.CONSENT == true) {
        if (window.localStorage.getItem('userLoggedIn') === 'true') {
          this.acceptCookies(this.cookieInfo);
        }
      }
      else {
        this.viewCookiePolicy();
      }
    }
  }

  acceptCookies(cookies) {
    let obj = {
      "CONSENT": cookies.CONSENT,
      "CATEGORIES": cookies.CATEGORIES,
      "TIMESTAMP": cookies.TIMESTAMP
    }
    this.cartService.cookiePolicy(obj).subscribe({
      next: (resp) => {
        if (resp.status === 200 && resp.object.length > 0) {
          let selectedCategories = []
          for (let obj of resp.object) {
            selectedCategories.push(obj.CATEGORY);
          }
        }
      },
      error: (e) => {
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        );
      }
    });
  }

  viewCookiePolicy() {
    const dialogRef = this.dialog.open(CookiePolicy, {
      width: '100%',
      height: 'auto',
      position: { left: '0px', bottom: '10px' },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result && result.categories) {
        if (isPlatformBrowser(this.platformId)) {
          window.localStorage.setItem('cookieConsent', JSON.stringify({ CONSENT: true, CATEGORIES: result.categories, TIMESTAMP: new Date() }));
        }
      }
    });
  }

  setupLoginSuccessListener(): void {
    // Listen for login success events from any component
    this.cartService.loginSuccess$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (isPlatformBrowser(this.platformId)) {
          // Update login state and cart when login success is broadcasted
          if (window.localStorage.getItem('userLoggedIn') === 'true') {
            this.loggedIn = true;
            const userData = window.localStorage.getItem('userInfo') || '{}';
            this.userInfo = userData ? JSON.parse(userData) : null;
            this.subscribeToCartUpdates();
          }
        }
      });
  }

  private subscribeToCartUpdates(): void {
    if (!this.loggedIn) {
      this.cartCount = 0;
      return;
    }
    // Initial cart load
    this.refreshCart();
    // Subscribe to cart count updates
    this.cartService.cartItemCount$
      .pipe(takeUntil(this.destroy$))
      .subscribe(count => {
        this.cartCount = count;
        this.cdr.detectChanges();
      });
  }

  private refreshCart(): void {
    this.cartService.getCartItems().subscribe({
      next: () => {
        this.cdr.detectChanges();
      },
      error: (error) => {
        this.cdr.detectChanges();
      }
    });
  }

  getAdminPortal() {
    if (isPlatformBrowser(this.platformId)) {
      window.open(this.dashboardUrl, '_blank');
    }
  }

  getSpecialOffers() {
    this.router.navigate(['/offers']);
  }

  getBlogs() {
    this.router.navigate(['/blogs']);
  }

  viewCart() {
    this.router.navigate(['/order-billing']);
  }

  login() {
    const dialogRef = this.dialog.open(Login, {
      width: '558px',
      height: 'auto',
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        this.subscribeToCookies();
        this.subscribeToCartUpdates();
        this.cartService.triggerComparisonRefresh();
      }
    });
  }

  logout() {
    this.cartCount = 0;
    if (isPlatformBrowser(this.platformId)) {
      const cookieConsent = window.localStorage.getItem('cookieConsent');
      window.localStorage.clear();
      if (cookieConsent) {
        window.localStorage.setItem('cookieConsent', cookieConsent);
      }
      window.location.href = '/';
    }
  }

  orderHistory() {
    this.router.navigate(['/order-history']);
  }

}