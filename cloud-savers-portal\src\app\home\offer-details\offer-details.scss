.head-row {
    display: flex;
    align-items: center;
    padding: 20px 50px;
    background-color: #FFFFFF;
    border-bottom: 1px solid #E5E7EB;

    .back-arrow {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 16px;
        color: #374151;
        cursor: pointer;
        text-align: start;
        width: 5%;
    }

    .pricing-head {
        font-size: 20px;
        font-weight: 600;
        color: #000000;
        text-align: center;
        width: 95%;
    }
}

.detail-container {
    width: 100%;
    height: auto;
    background-image: url('../../../assets/images/background_img.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding: 50px;
    display: flex;
    flex-direction: column;
    align-items: start;
    gap: 15px;
}

.provider-name {
    font-size: 14px;
    font-weight: 500;
    color: #1E40AF;
    background-color: #DBEAFE;
    padding: 5px 10px;
    border-radius: 20px;
    text-align: center;
}

.offer-title {
    font-size: 48px;
    font-weight: 700;
    color: #111827;
}

.instance-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
    font-size: 20px;
    color: #374151;
    padding-left: 1.5em;
}

.instance-price {
    font-size: 30px;
    font-weight: 700;
    color: #111827;
}

.btn-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 20px;
}

.cart-btn {
    background-color: #35A5DB;
    color: #FFFFFF;
    font-size: 16px;
    font-weight: 600;
    padding: 15px 25px;
    border-radius: 8px;
}

.compare-btn {
    background: transparent;
    color: #35A5DB;
    font-size: 16px;
    font-weight: 600;
    padding: 15px 25px;
    border-radius: 8px;
    border: 1px solid #35A5DB;
}

:host ::ng-deep .mat-mdc-form-field-flex>.mat-mdc-form-field-infix {
    padding: 10px 0px 1em 0px !important;
    min-height: 0px !important;
}

.region-select {
    width: 200px;
    height: 50px;
    background-color: #FFFFFF !important;
    border-radius: 8px;
    border: 1px solid #E5E7EB !important;
    outline: none !important;
}

.price-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 50px 70px;
    gap: 20px;
    background-color: #FFFFFF;
}

.price-head {
    font-size: 30px;
    font-weight: 700;
    color: #111827;
}

.price-desc {
    font-size: 18px;
    color: #4B5563;
    line-height: 28px;
}

.price-table {
    width: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 20px;
    border: 1px solid #E5E7EB;

    .table-header {
        display: flex;
        align-items: center;
        background-color: #F9FAFB;
        padding: 20px;
        font-size: 16px;
        font-weight: 600;
        color: #111827;
        gap: 20px;
        border-radius: 20px 20px 0 0;
    }

    .region-div {
        width: 40%;
    }

    .price-div {
        width: 30%;
    }

    .region {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .region-details {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .region-type {
        font-size: 14px;
        font-weight: 400;
        color: #6B7280;
    }

    .table-content {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .table-row {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 600;
        color: #111827;
        gap: 20px;
        padding: 20px;
    }
}

@media only screen and (max-width: 1250px) {
    .head-row {
        padding: 15px 20px;

        .back-arrow {
            width: auto;
            font-size: 14px;
        }

        .pricing-head {
            font-size: 18px;
        }
    }

    .detail-container {
        padding: 30px 20px;

        .offer-title {
            font-size: 26px;
            line-height: 40px;
        }

        .instance-details {
            font-size: 16px;
            gap: 16px;

            li {
                margin-left: 15px;
            }
        }

        .instance-price {
            font-size: 24px;
        }
    }

    .btn-row {
        flex-wrap: wrap;
        gap: 12px;

        .cart-btn,
        .compare-btn {
            padding: 12px 20px;
            font-size: 14px;
            flex: 1 1 calc(50% - 6px);
            min-width: 120px;
        }

        .cart-btn {
            background-color: #FFFFFF;
            color: #2C2C2C;
            border: 1px solid #D1D5DB;
        }

        .compare-btn {
            background-color: #2C2C2C;
            color: #FFFFFF;
        }

        .region-select {
            width: 100%;
            margin-top: 10px;
        }
    }

    .price-container {
        padding: 30px 20px 90px 20px;

        .price-head {
            font-size: 24px;
            text-align: center;
        }

        .price-desc {
            font-size: 16px;
            text-align: center;
            margin-bottom: 20px;
        }
    }

    .price-table {
        .table-header {
            display: none;
            /* Hide header on mobile */
        }

        .table-content {
            &:first-child .table-row {
                border-top: none;
            }

            &:last-child .table-row {
                border-bottom: none;
            }
        }

        .table-row {
            flex-direction: column;
            align-items: flex-start;
            padding: 15px;
            gap: 15px;
            border-bottom: none;
            margin-bottom: 0;

            &:last-child {
                border-bottom: 1px solid #E5E7EB;
            }
        }

        .region-div,
        .price-div {
            width: 100%;
            display: flex;
            justify-content: space-between;

            &::before {
                content: attr(data-label);
                font-weight: 600;
                color: #6B7280;
                margin-right: 15px;
            }
        }

        .region {
            width: 100%;
            justify-content: space-between;
            align-items: baseline;

            .region-details {
                align-items: flex-end;
                width: 100%;
                display: flex;
                justify-content: space-between;
                gap: 10px;
            }

            .region-name {
                text-align: end;
            }

            .region-icon {
                position: relative;
                top: 9px;
                left: -5px;
            }
        }
    }
}