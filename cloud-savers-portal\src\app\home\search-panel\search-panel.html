<div class="custom-overlay" *ngIf="loading">
    <div class="custom-overlay__inner">
        <div class="custom-loader">
            <svg class="progress-circle" viewBox="0 0 50 50">
                <circle class="progress-bg" cx="25" cy="25" r="20" />
                <circle class="progress-bar" cx="25" cy="25" r="20" />
            </svg>
            <div class="loader-title">Loading data</div>
            <div class="loader-desc">Please wait...</div>
        </div>
    </div>
</div>
<section class="search-panel">
    <div class="search-head">
        <h1 class="search-title">Best Cloud Deals by Cloud Atler</h1>
        <h2 class="search-desc">
            Compare cloud providers, features and pricing to make informed decisions for your business needs
        </h2>
        <div class="search-box-container">
            <input *ngIf="!mobile" type="text" class="search-box" [formControl]="searchKeyword"
                placeholder="Search for cloud services.... eg: Virtual Machine with 4 VCPU and 8 GB RAM"
                (keydown.enter)="onSearchEnter()">
            <input *ngIf="mobile" type="text" class="search-box" [formControl]="searchKeyword" placeholder="Search...."
                (keydown.enter)="onSearchEnter()">
            <!-- [matAutocomplete]="autoSearch" -->
            <!-- <mat-autocomplete #autoSearch="matAutocomplete" (optionSelected)="onSelectionChange($event)">
                <mat-option *ngFor="let sugg of autoFillList;"
                    [value]="sugg.document.product_type + ' ' + '-' + ' ' + sugg.document.product_name + ' ' + '-' + ' ' + sugg.document.instance_family + ' ' + '-' + ' ' + sugg.document.vcpu_cores + ' ' + 'VCPU' + ' ' + '-' + ' ' + sugg.document.ram_gb + ' ' + 'GB RAM'">
                    {{sugg.document.product_type}} - {{sugg.document.product_name}} - {{sugg.document.instance_family}}
                    - {{sugg.document.vcpu_cores}} VCPU - {{sugg.document.ram_gb}} GB RAM
                </mat-option>
            </mat-autocomplete> -->
            <div class="filter-btn" (click)="showFilterCard()">
                <iconify-icon icon="bx:filter"></iconify-icon>
                Advance Search
            </div>
            <button mat-button class="search-btn" (click)="compareServices()"
                [disabled]="searchKeyword.value.length < 2">
                <mat-icon>search</mat-icon>
            </button>
        </div>
    </div>
    <!-- <div *ngIf="!mobile" class="search-head">
        <div class="search-title">Best cloud deals powered by Cloud Atler</div>
        <button mat-button class="btn-connect" (click)="connectCloudAccount()">
            <mat-icon>link</mat-icon> Connect your Cloud and Optimize
        </button>
    </div>
    <div *ngIf="mobile" class="search-head">
        <div class="search-title">Best cloud deals powered by Cloud Atler</div>
        <button mat-button class="btn-connect" (click)="connectCloudAccount()">
            <mat-icon>link</mat-icon>
        </button>
    </div> -->
    <div class="params-card" *ngIf="showFilter">
        <div class="header-row">
            <img src="assets/images/cloud-compare.png">
            <label>Search for your cloud services</label>
        </div>
        <form [formGroup]="compareForm">
            <div formArrayName="compareArray" class="array-box">
                <div *ngFor="let control of getCompare() || []; let k = index;" [formGroupName]="k">
                    <div class="params-form">
                        <div class="params-row">
                            <div class="product-row">
                                <mat-form-field appearance="outline" class="form-field search-panel-field"
                                    [ngClass]="getCompare()[k]?.get('selectedService')?.value === 'vm' ? 'service-field' : 'form-field'">
                                    <mat-label>Service Type</mat-label>
                                    <mat-select formControlName="selectedService"
                                        (selectionChange)="selectServiceType($event.value, k)">
                                        <mat-option *ngFor="let service of serviceTypes || []" [value]="service">
                                            {{formatText(service)}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                                <mat-form-field appearance="outline" class="form-field search-panel-field"
                                    [ngClass]="getCompare()[k]?.get('selectedService')?.value === 'vm' ? 'service-field' : 'form-field'">
                                    <mat-label>Product Name</mat-label>
                                    <mat-select formControlName="selectedProduct"
                                        (selectionChange)="selectProductName($event.value, k)">
                                        <mat-option *ngFor="let category of categoryProductsMap[k] || []"
                                            [value]="category">
                                            {{formatText(category)}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                                <mat-form-field appearance="outline" class="form-field search-panel-field"
                                    *ngIf="getCompare()[k]?.get('selectedService')?.value === 'vm'">
                                    <mat-label>Instance Family</mat-label>
                                    <mat-select formControlName="selectedInstance"
                                        (selectionChange)="selectInstance($event.value, k)">
                                        <mat-option *ngFor="let instance of categoryInstancesMap[k] || []"
                                            [value]="instance">
                                            {{instance}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                                <mat-form-field appearance="outline" class="form-field number-field search-panel-field"
                                    *ngIf="getCompare()[k]?.get('selectedService')?.value === 'vm'">
                                    <mat-label>vCPUs</mat-label>
                                    <mat-select formControlName="vCPU" (selectionChange)="selectvCPU($event.value, k)">
                                        <mat-option *ngFor="let cpu of vCPUMap[k] || []"
                                            [value]="cpu">{{cpu}}</mat-option>
                                    </mat-select>
                                </mat-form-field>
                                <mat-form-field appearance="outline" class="form-field number-field search-panel-field"
                                    *ngIf="getCompare()[k]?.get('selectedService')?.value === 'vm'">
                                    <mat-label>RAM</mat-label>
                                    <mat-select formControlName="RAM" (selectionChange)="selectRAM($event.value, k)">
                                        <mat-option *ngFor="let ram of RAMMap[k] || []"
                                            [value]="ram">{{ram}}</mat-option>
                                    </mat-select>
                                </mat-form-field>
                                <mat-form-field appearance="outline" class="form-field search-panel-field"
                                    *ngIf="getCompare()[k]?.get('selectedService')?.value !== 'vm'">
                                    <mat-label>Region</mat-label>
                                    <mat-select formControlName="selectedRegion"
                                        (selectionChange)="selectRegion($event.value, k)">
                                        <mat-option>
                                            <ngx-mat-select-search placeholderLabel="Search"
                                                (input)="filterRegion(k, $event)"
                                                noEntriesFoundLabel="No Matching Found">
                                                <mat-icon ngxMatSelectSearchClear (click)="onRegionSearchClear(k)"
                                                    class="close-icon">close</mat-icon>
                                            </ngx-mat-select-search>
                                        </mat-option>
                                        <mat-option *ngFor="let region of filteredRegionsMap[k] || []" [value]="region">
                                            {{region}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                                <mat-form-field appearance="outline" class="form-field search-panel-field"
                                    *ngIf="getCompare()[k]?.get('selectedService')?.value !== 'vm'">
                                    <mat-label>City</mat-label>
                                    <mat-select formControlName="selectedCity"
                                        (selectionChange)="selectCity($event.value, k)">
                                        <mat-option>
                                            <ngx-mat-select-search placeholderLabel="Search"
                                                (input)="filterCity(k, $event)" noEntriesFoundLabel="No Matching Found">
                                                <mat-icon ngxMatSelectSearchClear (click)="onCitySearchClear(k)"
                                                    class="close-icon">close</mat-icon>
                                            </ngx-mat-select-search>
                                        </mat-option>
                                        <mat-option *ngFor="let city of filteredCitiesMap[k] || []" [value]="city">
                                            {{city}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
                            <div class="actions-row">
                                <div class="more-filters" (click)="showMoreFilters(k)"
                                    *ngIf="getCompare()[k]?.get('selectedService')?.value === 'vm'">
                                    {{ showMoreFiltersIndices?.includes(k) ? 'Less' : 'More' }}
                                </div>
                                <button mat-icon-button class="remove-btn" (click)="resetCompare(k)">
                                    <iconify-icon icon="material-symbols:autorenew-rounded"></iconify-icon>
                                </button>
                                <button mat-icon-button class="remove-btn" (click)="removeCompare(k)"
                                    *ngIf="!isRemoveDisabled(k)">
                                    <iconify-icon icon="material-symbols:delete-outline-rounded"></iconify-icon>
                                </button>
                            </div>
                        </div>
                        <div class="product-row"
                            *ngIf="showMoreFiltersIndices?.includes(k) && getCompare()[k]?.get('selectedService')?.value === 'vm'">
                            <mat-form-field appearance="outline" class="form-field search-panel-field">
                                <mat-label>Region</mat-label>
                                <mat-select formControlName="selectedRegion"
                                    (selectionChange)="selectRegion($event.value, k)">
                                    <mat-option>
                                        <ngx-mat-select-search placeholderLabel="Search"
                                            (input)="filterRegion(k, $event)" noEntriesFoundLabel="No Matching Found">
                                            <mat-icon ngxMatSelectSearchClear (click)="onRegionSearchClear(k)"
                                                class="close-icon">close</mat-icon>
                                        </ngx-mat-select-search>
                                    </mat-option>
                                    <mat-option *ngFor="let region of filteredRegionsMap[k] || []" [value]="region">
                                        {{region}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                            <mat-form-field appearance="outline" class="form-field search-panel-field">
                                <mat-label>City</mat-label>
                                <mat-select formControlName="selectedCity"
                                    (selectionChange)="selectCity($event.value, k)">
                                    <mat-option>
                                        <ngx-mat-select-search placeholderLabel="Search" (input)="filterCity(k, $event)"
                                            noEntriesFoundLabel="No Matching Found">
                                            <mat-icon ngxMatSelectSearchClear (click)="onCitySearchClear(k)"
                                                class="close-icon">close</mat-icon>
                                        </ngx-mat-select-search>
                                    </mat-option>
                                    <mat-option *ngFor="let city of filteredCitiesMap[k] || []" [value]="city">
                                        {{city}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                            <mat-form-field appearance="outline" class="form-field search-panel-field">
                                <mat-label>Operating System</mat-label>
                                <mat-select formControlName="selectedOS" (selectionChange)="selectOS($event.value, k)">
                                    <mat-option *ngFor="let os of operatingSystemsMap[k] || []" [value]="os">
                                        {{os}}
                                    </mat-option>
                                    <!-- <mat-optgroup *ngFor="let os of operatingSystemsMap[k] | keyvalue"
                                        [label]="os?.key">
                                        <mat-option *ngFor="let option of os?.value" [value]="option">
                                            <div class="region-option">
                                                <div class="os-name">{{option}}</div>
                                            </div>
                                        </mat-option>
                                    </mat-optgroup> -->
                                </mat-select>
                            </mat-form-field>
                            <mat-form-field appearance="outline" class="form-field search-panel-field">
                                <mat-label>Host Type</mat-label>
                                <mat-select formControlName="selectedHost">
                                    <mat-option *ngFor="let host of hostTypesMap[k] || []"
                                        [value]="host">{{host}}</mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <div class="params-actions">
            <div>
                <button mat-button class="btn-compare" (click)="compareServices()" [disabled]="!compareForm.valid">
                    Get Best Deal
                </button>
            </div>
            <!-- <div>
                <button mat-button class="btn-add-param" [disabled]="compareArrayLength >= 20" (click)="addCompare()">
                    <mat-icon>add</mat-icon> Add More
                </button>
            </div> -->
        </div>
    </div>
    <div class="suggestions" *ngIf="searchSuggestions.length > 0">
        <div class="suggestions-title">Popular Searches: </div>
        <mat-chip-list>
            <mat-chip *ngFor="let suggestion of searchSuggestions || []" (click)="selectSuggestion(suggestion)">
                {{suggestion}}
            </mat-chip>
        </mat-chip-list>
    </div>
</section>