.container {
  width: 100%;
  height: auto;
  background-image: url('/assets/images/background_img.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.head-row {
  display: flex;
  align-items: center;
  padding: 20px 50px;
  background-color: #FFFFFF;
  border-bottom: 1px solid #E5E7EB;

  .back-arrow {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 16px;
    color: #374151;
    cursor: pointer;
    text-align: start;
    width: 15%;
  }

  .pricing-head {
    font-size: 20px;
    font-weight: 600;
    color: #000000;
    text-align: center;
    width: 80%;
  }
}

.article-container {
  // max-width: 800px;
  margin: 0 auto;
  padding: 5% 20%;
  line-height: 1.6;
  color: #333;
  background: #FFFFFF;
}

.article-container.blog-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.content-wrapper {
  flex: 1;
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  padding: 20px;
}

.article-header {
  margin-bottom: 30px;
}

.article-title {
  font-size: 48px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 15px;
  line-height: 54px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.author-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #FFFFFF;
}

.author-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.author-details {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-size: 16px;
  line-height: 24px;
  color: #111827;
  font-weight: 500;
}

.company-name {
  font-size: 14px;
  line-height: 20px;
  color: #6B7280;
  font-weight: 400;
}

.hero-section {
  margin-bottom: 40px;
}

.hero-illustration {
  margin-bottom: 25px;
}

.hero-image {
  width: 100%;
  height: 100%;
  border-radius: 20px;
}

.hero-description {
  font-size: 16px;
  color: #374151;
  text-align: left;
  line-height: 26px;
  font-weight: 400;
  white-space: pre-line;
}

.providers-grid {
  white-space: pre-line;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.section-title {
  font-size: 30px;
  line-height: 36px;
  font-weight: 700;
  color: #111827;
  margin: 20px 0;
}

.section-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.section-content p {
  font-size: 16px;
  color: #374151;
  text-align: left;
  line-height: 26px;
  font-weight: 400;
}

.benefits-list,
.factors-list {
  padding: 0px 35px 10px 35px;
}

.benefits-list li,
.factors-list li {
  padding: 5px 0px 3px 0;
  font-size: 16px;
  color: #374151;
  line-height: 26px;
  font-weight: 400;
}

.provider-item {
  margin: 15px 0 30px 0;
}

.provider-item:last-child {
  margin-bottom: 0;
}

.cta-box {
  background-image: url('/assets/images/background_img.jpg');
  background-size: cover;
  background-position: center;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  margin: 40px 0;

  .cta-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 30px;

    .cta-text {
      flex: 1;
      text-align: left;

      h3 {
        font-size: 24px;
        color: #2C2C2C;
        margin-bottom: 12px;
        font-weight: 700;
        line-height: 1.3;
      }

      p {
        color: #2C2C2C;
        margin: 0;
        font-size: 18px;
        font-weight: 400;
        line-height: 1.5;
      }
    }

    .cta-button {
      background: #2C2C2C;
      color: white;
      border: none;
      padding: 12px 28px;
      border-radius: 6px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      white-space: nowrap;
    }
  }
}

.provider-title {
  font-size: 24px;
  font-weight: 600;
  color: #1F2937;
  margin-bottom: 10px;
  line-height: 32px;
}


@media only screen and (max-width: 1250px) {

  .head-row {
    padding: 12px 16px 12px 56px;
    position: relative;
    flex-direction: column;
    align-items: center;
    gap: 5px;

    .back-arrow {
      width: 100%;
    }

    .pricing-head {
      font-size: 16px;
      width: 100%;
      padding: 0 30px;
      text-align: left;
    }
  }

  .main-content {
    gap: 5px;
  }

  .article-container {
    padding: 20px 20px 60px 20px;
  }

  .article-header {
    margin-bottom: 0px;
  }

  .hero-image {
    width: 100%;
  }

  .article-title {
    font-size: 24px;
    line-height: 35px;
  }

  .hero-section {
    padding: 15px 20px 0px 20px;
  }

  .section-title {
    font-size: 24px;
    line-height: 30px;
  }

  .provider-title {
    font-size: 20px;
    line-height: 28px;
  }

  .cta-content {
    flex-direction: column;
    gap: 15px;
  }
}