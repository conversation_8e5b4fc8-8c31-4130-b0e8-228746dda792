.head-row {
    display: flex;
    align-items: center;
    padding: 20px 50px;
    background-color: #FFFFFF;
    border-bottom: 1px solid #E5E7EB;

    .back-arrow {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 16px;
        color: #374151;
        cursor: pointer;
        text-align: start;
        width: 5%;
    }
}

.detail-content {
    display: flex;
    flex-direction: column;
    max-width: 100%;
    overflow-x: hidden;

    .featured-section {
        padding: 20px 50px;
    }

    .featured-header {
        display: flex;
        flex-direction: column;
        align-items: start;
        gap: 12px;
    }

    .featured-head {
        font-size: 30px;
        font-weight: 700;
        color: #111827;
    }

    .featured-desc {
        font-size: 16px;
        color: #4B5563;
        text-align: center;
        line-height: 24px;
    }

    .premium-section {
        background-color: #FFFFFF;
        padding: 40px 50px;
        display: flex;
        flex-direction: column;
        gap: 30px;

        .premium-header {
            display: flex;
            flex-direction: column;
            align-items: start;
            gap: 12px;
        }

        .premium-head {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 24px;
            font-weight: 700;
            color: #111827;
        }

        .premium-cards {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 20px;

            .premium-card {
                width: 30%;
                background: linear-gradient(135deg, #EFF6FF 0%, #FAF5FF 100%);
                border-radius: 8px;
                border: 1px solid #BFDBFE;
                box-shadow: 0px 10px 15px -3px #0000001A;
                padding: 30px 20px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                gap: 20px;
            }
        }
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: start;
    }

    .provider-head {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .provider-name {
        font-size: 20px;
        font-weight: 700;
        color: #111827;
    }

    .provider-desc {
        font-size: 14px;
        color: #4B5563;
    }

    .premium-desc {
        font-size: 16px;
        color: #111827;
        font-weight: 700;
    }

    .service-offers {
        display: flex;
        flex-direction: column;
        gap: 15px;
        font-size: 16px;
        color: #4B5563;
        line-height: 24px;

        .features-list {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .feature-item {
                display: flex;
                gap: 5px;
                align-items: center;

                .content {
                    font-size: 16px;
                    font-weight: 400;
                    color: #2C2C2C;
                }

                .plan-features {
                    color: #374151;
                }
            }
        }
    }

    .premium-offers {
        font-size: 14px;
    }

    .pricing {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .pricing-container {
            display: flex;
            flex-direction: column;
            gap: 5px;

            .price-time {
                font-size: 14px;
                color: #6B7280;
            }
        }

        .price {
            font-size: 24px;
            font-weight: 700;
            color: #111827;
        }
    }

    .view-btn {
        background-color: #35A5DB;
        color: #FFFFFF;
        font-size: 16px;
        padding: 10px 20px;
        border-radius: 8px;
    }

    .detail-btn {
        width: 100%;
    }

    .featured-offers {
        padding: 40px 50px;
        display: flex;
        flex-direction: column;
        gap: 40px;
    }

    .all-offer-head {
        font-size: 24px;
        font-weight: 700;
        color: #111827;
    }

    .offer-cards {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: 50px;

        .offer-card {
            width: 30%;
            background: white;
            border-radius: 8px;
            box-shadow: 0 3px 8px rgb(0 0 0 / 0.11);
            padding: 30px 20px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            gap: 20px;
        }
    }

    .provider-logo {
        display: flex;
        gap: 8px;
        align-items: center;
    }

    .provider-name {
        font-size: 20px;
        font-weight: 700;
        color: #111827;
    }
}

@media only screen and (max-width: 1250px) {
    .detail-content {
        padding-bottom: 60px;

        .premium-head {
            align-self: center;
        }

        .featured-desc {
            align-self: center;
        }

        .featured-section {
            padding: 20px 50px;
        }

        .featured-head {
            align-self: center;
            font-size: 28px !important;
        }

        .feature-item {
            align-items: flex-start !important;
        }
    }

    .offer-cards {
        align-items: center !important;
        justify-content: center !important;

        .offer-card {
            width: 384px !important;
            height: -webkit-fill-available;
        }
    }

    .premium-section {
        padding: 20px 10px;

        .premium-cards {
            align-items: center !important;
            justify-content: center !important;

            .premium-card {
                width: 386px !important;
                height: -webkit-fill-available;
                gap: 10px !important;
            }
        }
    }

    .all-offer-head {
        align-self: center;
    }

    .pricing {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 10px !important;
    }
}