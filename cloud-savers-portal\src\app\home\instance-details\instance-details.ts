import { Component, CUSTOM_ELEMENTS_SCHEMA, HostListener, Inject, PLATFORM_ID, ViewChild, OnInit, AfterViewInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { MaterialModule } from '../../material-module';
import { Header } from '../header/header';
import { SearchPanel } from '../search-panel/search-panel';
import { MobileFooter } from '../mobile-footer/mobile-footer';
import { environment } from '../../../environments/environment';
import { PROVIDER_ICON_MAP } from '../../shared/filter-headers.constants';
import { Notification } from '../../services/notification';
import { colorObj } from '../../shared/color-object';
import { Auth } from '../../services/auth';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { ActivatedRoute } from '@angular/router';
import { MetaService } from '../../services/meta.service';
import { MatDialog } from '@angular/material/dialog';
import { Login } from 'src/app/auth/login/login';
import { CartService } from 'src/app/services/cart-service';

@Component({
  selector: 'app-instance-details',
  imports: [
    CommonModule,
    MaterialModule,
    Header,
    SearchPanel,
    MobileFooter
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './instance-details.html',
  styleUrl: './instance-details.scss'
})
export class InstanceDetails implements OnInit, AfterViewInit {

  loading: boolean = false;
  pricingLoading: boolean = true;
  dealLoading: boolean = true;
  get providerIconMap() {
    return PROVIDER_ICON_MAP;
  }
  instanceId: string;
  instanceDetails: any = {};
  resourceDetails: any = {};
  similarDeals: any = [];
  public monthlyHours = environment.HOURS_IN_A_MONTH;
  displayedColumns: string[] = ['region_name', 'payment_option', 'payment_plan', 'avg_monthly_price'];
  @ViewChild(MatPaginator) paginator: MatPaginator;
  dataSource = new MatTableDataSource<any>();
  public websiteUrl = environment.PORTAL_URL;
  public dashboardUrl = `${environment.DASHBOARD_URL}`;
  public mobile: boolean = false;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenWidth();
  }

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private router: Router,
    private route: ActivatedRoute,
    private authService: Auth,
    private metaService: MetaService,
    private notify: Notification,
    private cartService: CartService,
    private dialog: MatDialog
  ) { }

  ngOnInit() { }

  ngAfterViewInit() {
    this.checkScreenWidth();
    this.scrollToDetailsContent();
    this.loadInstanceData();
  }

  private checkScreenWidth() {
    if (isPlatformBrowser(this.platformId)) {
      this.mobile = window.innerWidth <= 1250;
    }
  }

  loadInstanceData() {
    const slug = this.route.snapshot.paramMap.get('slug');
    const parts = slug.split('--');
    this.instanceId = parts[parts.length - 1];
    if (!!this.instanceId) {
      this.getResource();
    }
    else {
      this.router.navigate(['/skyscanner4cloud']);
    }
    this.updateMetaTags(slug);
  }

  private updateMetaTags(slug) {
    const title = `Instance Details | Unbiased Cloud Instance Comparison`;
    const description = `Get unbiased, detailed specifications for any cloud instance.Compare CPU, memory, storage, and pricing to make an informed decision for your project needs.`;
    const canonicalPath = `/service/${slug}`;
    this.metaService.updateTitle(title);
    this.metaService.updateDescription(description);
    this.metaService.updateOgTitle(title);
    this.metaService.updateOgDescription(description);
    this.metaService.updateCanonicalUrl(this.websiteUrl, canonicalPath);
    this.metaService.updateHreflangTags(
      [
        { hreflang: 'en-us', href: canonicalPath },
        { hreflang: 'x-default', href: canonicalPath },
        { hreflang: 'en', href: canonicalPath }
      ],
      this.websiteUrl
    );
  }

  getResource() {
    this.loading = true;
    this.authService.getResource(this.instanceId).subscribe({
      next: (data) => {
        this.instanceDetails = data.object?.results[0]?.hits[0]?.document;
        if (!!this.instanceDetails) {
          this.getResourceDetails();
          this.getPricingDetails();
          this.getSimilarDeal();
        }
      },
      error: (e) => {
        this.loading = false;
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        );
      }
    })
  }

  getResourceDetails() {
    this.loading = true;
    let obj = {
      "PRODUCT_NAME": this.instanceDetails?.product_name,
      "PRODUCT_TYPE": this.instanceDetails?.product_type,
      "SERVICE_PROVIDER": this.instanceDetails?.service_provider,
      "VM_NAME": this.instanceDetails?.vm_name,
      "ACTUAL_REGION_CODE": this.instanceDetails?.actual_region_code,
      "ACTUAL_REGION_NAME": this.instanceDetails?.actual_region_name
    }
    this.authService.getResourceDetails(obj).subscribe({
      next: (data) => {
        this.resourceDetails = data.object;
        this.loading = false;
      },
      error: (e) => {
        this.loading = false;
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        );
      }
    })
  }

  getPricingDetails() {
    let obj = {
      "PRODUCT_TYPE": this.instanceDetails?.product_type,
      "PRODUCT_NAME": this.instanceDetails?.product_name,
      "INSTANCE_FAMILY": this.instanceDetails?.instance_family,
      "SERVICE_PROVIDER": this.instanceDetails?.service_provider,
      "VM_NAME": this.instanceDetails?.vm_name,
      "HOST_TYPE": this.instanceDetails?.host_type,
      "OS": this.instanceDetails?.os,
      "OS_TYPE": this.instanceDetails?.os_type,
      "PAYMENT_OPTION": this.instanceDetails?.payment_option,
      "PAYMENT_PLAN": this.instanceDetails?.payment_plan,
      "TERM": this.instanceDetails?.term
    }
    this.authService.getRegionalPricing(obj).subscribe({
      next: (data) => {
        this.dataSource.data = data.object.results[0].hits;
        this.dataSource.paginator = this.paginator;
        this.pricingLoading = false;
      },
      error: (e) => {
        this.pricingLoading = false;
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        );
      }
    })
  }

  getSimilarDeal() {
    let obj = {
      "PRODUCT_TYPE": this.instanceDetails?.product_type,
      "PRODUCT_NAME": this.instanceDetails?.product_name,
      "INSTANCE_FAMILY": this.instanceDetails?.instance_family,
      "OS": this.instanceDetails?.os,
      "OS_TYPE": this.instanceDetails?.os_type,
      "PAYMENT_OPTION": this.instanceDetails?.payment_option,
      "PAYMENT_PLAN": this.instanceDetails?.payment_plan,
      "TERM": this.instanceDetails?.term,
      "CS_CITY": this.instanceDetails?.cs_city,
      "RAM_GB": this.instanceDetails?.ram_gb,
      "VCPU_CORES": this.instanceDetails?.vcpu_cores
    }
    this.authService.getSimilarDeals(obj).subscribe({
      next: (data) => {
        this.similarDeals = data.object.results[0].grouped_hits;
        this.dealLoading = false;
      },
      error: (e) => {
        this.dealLoading = false;
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        );
      }
    })
  }

  viewInstanceDetails(instance: any) {
    const serviceProvider = this.slugify(instance.service_provider);
    const productName = this.slugify(instance.product_name);
    const slug = serviceProvider + '-' + productName + '--' + instance.record_id;
    this.instanceDetails = {};
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
      this.router.navigate(['/service', slug]);
    });
  }

  slugify(text: string): string {
    return text
      ?.toString()
      ?.toLowerCase()
      ?.trim()
      ?.replace(/[\/\s]+/g, '-')
      ?.replace(/[^\w-]+/g, '')
      ?.replace(/-+/g, '-')
      ?.replace(/^-+|-+$/g, '');
  }

  addToCart(instance?: any) {
    if (!this.authService.isLoggedIn()) {
      const dialogRef = this.dialog.open(Login, {
        width: '558px',
        height: 'auto',
        disableClose: true
      });
      dialogRef.afterClosed().subscribe(result => {
        if (result === true) {
          this.updateItemQuantity(instance, 1);
        }
      });
    }
    else {
      this.updateItemQuantity(instance, 1);
    }
  }

  updateItemQuantity(element: any, itemQuantity: number) {
    let obj = {
      "RESOURCE": [{
        "RESOURCE_TYPE": "external",
        "RESOURCE_ID": element?.record_id,
        "QUANTITY": itemQuantity,
        "SERVICE_DETAILS": element
      }]
    }
    this.cartService.addItemToCart(obj).subscribe({
      next: (resp) => {
        if (resp.status === 200 && resp.object.length > 0) {
          this.cartService.cartUpdated$.next();
          this.cartService.notifyLoginSuccess();
          this.router.navigate(['/skyscanner4cloud']);
        }
      },
      error: (e) => {
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        );
      }
    });
  }

  scrollToDetailsContent() {
    setTimeout(() => {
      if (isPlatformBrowser(this.platformId)) {
        const element = document.getElementById('detailContent');
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }
    }, 100);
  }

  backToInstances() {
    this.instanceDetails = {};
    this.router.navigate(['/skyscanner4cloud']);
  }

  getAdminPortal(event: Event) {
    if (isPlatformBrowser(this.platformId)) {
      event.preventDefault();
      const baseUrl = this.dashboardUrl.replace(/\/$/, '');
      const url = `${baseUrl}?source=signup`;
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  }

  formatText(input: string) {
    return input?.replace(/-/g, ' ')?.replace(/\b\w/g, char => char?.toUpperCase());
  }

  getMonthlyPrice(hourlyRate: number, provider: string): string {
    const monthlyHours = this.monthlyHours[!!provider ? provider.toLowerCase() : ''];
    return (hourlyRate * monthlyHours)?.toFixed(2);
  }

}