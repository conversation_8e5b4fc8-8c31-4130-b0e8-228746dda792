import { CommonModule, isPlatformBrowser } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, HostListener, Inject, PLATFORM_ID } from '@angular/core';
import { MaterialModule } from '../../material-module';
import { Header } from '../header/header';
import { SearchPanel } from '../search-panel/search-panel';
import { Footer } from '../footer/footer';
import { Notification } from '../../services/notification';
import { colorObj } from '../../shared/color-object';
import { Auth } from '../../services/auth';
import { MobileFooter } from '../mobile-footer/mobile-footer';
import { Router } from '@angular/router';
import offersData from 'src/assets/shared/internal-offers/internal-offers.json';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-comparison-portal',
  imports: [
    CommonModule,
    MaterialModule,
    Header,
    SearchPanel,
    Footer,
    MobileFooter
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './comparison-portal.html',
  styleUrl: './comparison-portal.scss'
})
export class ComparisonPortal {
  featuredOffers = offersData.FEATURED_OFFERS;
  public dashboardUrl = `${environment.DASHBOARD_URL}`;
  // featuredOffers = (offersData as any).FEATURED_OFFERS;
  mobile: boolean = false;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenWidth();
  }

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private authService: Auth,
    private notify: Notification,
    private router: Router
  ) { }

  ngOnInit() {
    this.checkScreenWidth();
    this.scrollToTopContent();
    this.getMasterData();
  }

  private checkScreenWidth() {
    if (isPlatformBrowser(this.platformId)) {
      if (window.innerWidth <= 1250) {
        this.mobile = true;
      } else {
        this.mobile = false;
      }
    }
  }

  getMasterData() {
    this.authService.getMasterData().subscribe({
      next: (data) => {
        if (isPlatformBrowser(this.platformId)) {
          for (let md_key in data.object[0]) {
            data.object[0][md_key].shift();
            window.localStorage.setItem(md_key, JSON.stringify(data.object[0][md_key]));
          }
        }
      },
      error: (e) => {
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        )
      }
    })
  }

  slugify(text: string): string {
    return text
      ?.toString()
      ?.toLowerCase()
      ?.trim()
      ?.replace(/[\/\s]+/g, '-')
      ?.replace(/[^\w-]+/g, '')
      ?.replace(/-+/g, '-')
      ?.replace(/^-+|-+$/g, '');
  }

  viewOfferDetails(offer: any) {
    const titleSlug = this.slugify(offer.title);
    this.router.navigate(['/offers', titleSlug]);
  }

  getAdminPortal(event: Event) {
    if (isPlatformBrowser(this.platformId)) {
      event.preventDefault();
      const baseUrl = this.dashboardUrl.replace(/\/$/, '');
      const url = `${baseUrl}?source=signup`;
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  }

  viewAllOffers() {
    this.router.navigate(['/offers']);
  }

  scrollToTopContent() {
    setTimeout(() => {
      if (isPlatformBrowser(this.platformId)) {
        const element = document.getElementById('pageContent');
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }
    }, 100);
  }

  getLowestPriceForDefaultRegion(offer: any): number {
    if (!offer?.region || !Array.isArray(offer.region)) {
      return 0;
    }
    const defaultRegion = offer.region.find(region => region.default === true);
    if (!defaultRegion) {
      return 0;
    }
    const awsPrice = defaultRegion.aws || 0;
    const azurePrice = defaultRegion.azure || 0;
    return Math.min(awsPrice, azurePrice);
  }

}