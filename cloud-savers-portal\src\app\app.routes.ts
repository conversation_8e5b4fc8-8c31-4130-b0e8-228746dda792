import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ComparisonPortal } from './home/<USER>/comparison-portal';
import { CostAnalysis } from './home/<USER>/cost-analysis';
import { InstanceDetails } from './home/<USER>/instance-details';
import { OrderBilling } from './home/<USER>/order-billing';
import { OfferDetails } from './home/<USER>/offer-details';
import { OrderHistory } from './home/<USER>/order-history';
import { CookiePolicyDetails } from './home/<USER>/cookie-policy-details';
import { TermsServices } from './home/<USER>/terms-services';
import { PrivacyPolicy } from './home/<USER>/privacy-policy';
import { SpecialOffers } from './home/<USER>/special-offers';
import { Blogs } from './home/<USER>/blogs';
import { BlogPostComponent } from './home/<USER>/blog-post/blog-post.component';
import { ComparisonResults } from './home/<USER>/comparison-results';
import { PageNotFound } from './home/<USER>/page-not-found';

export const routes: Routes = [
    {
        path: '',
        component: ComparisonPortal,
        data: {
            title: 'Cloudatler | Compare Cloud Services & Manage Costs Securely',
            description: 'Securely compare cloud providers, find exclusive deals, and manage costs with Cloudatler. Our FinOps platform turns bills into actionable insights with bank-level security.',
            ogTitle: 'Cloudatler | Compare Cloud Services & Manage Costs Securely',
            ogDescription: 'Securely compare cloud providers, find exclusive deals, and manage costs with Cloudatler. Our FinOps platform turns bills into actionable insights with bank-level security.',
            canonicalUrl: '/',
            hreflangs: [
                { lang: 'en-us', path: '/' },
                { lang: 'x-default', path: '/' },
                { lang: 'en', path: '/' }
            ]
        }
    },
    {
        path: 'skyscanner4cloud',
        component: CostAnalysis,
        data: {
            title: 'Skyscanner for Cloud | Compare Cloud Services Instantly',
            description: 'Use Cloudatler\'s "Skyscanner for Cloud" to instantly compare prices, features, and performance across AWS, Azure, GCP, and more. Find your perfect cloud solution.',
            ogTitle: 'Skyscanner for Cloud | Compare Cloud Services Instantly',
            ogDescription: 'Use Cloudatler\'s "Skyscanner for Cloud" to instantly compare prices, features, and performance across AWS, Azure, GCP, and more. Find your perfect cloud solution.',
            canonicalUrl: '/skyscanner4cloud',
            hreflangs: [
                { lang: 'en-us', path: '/skyscanner4cloud' },
                { lang: 'x-default', path: '/skyscanner4cloud' },
                { lang: 'en', path: '/skyscanner4cloud' }
            ]
        }
    },
    {
        path: 'service/:slug',
        component: InstanceDetails
    },
    {
        path: 'order-billing',
        component: OrderBilling,
        data: {
            title: 'Secure Order & Billing Information | Cloudatler',
            description: 'Securely manage your order and billing details. Cloudatler ensures your payment information is protected with bank-level security and complete privacy.',
            ogTitle: 'Secure Order & Billing Information | Cloudatler',
            ogDescription: 'Securely manage your order and billing details. Cloudatler ensures your payment information is protected with bank-level security and complete privacy.',
            canonicalUrl: '/order-billing',
            hreflangs: [
                { lang: 'en-us', path: '/order-billing' },
                { lang: 'x-default', path: '/order-billing' },
                { lang: 'en', path: '/order-billing' }
            ]
        }
    },
    {
        path: 'offers',
        component: SpecialOffers,
        data: {
            title: 'Exclusive Cloud Offer Details | Cloudatler',
            description: 'View the details of exclusive offers from leading cloud providers.Understand the terms, savings, and benefits to choose the best deal for your needs.',
            ogTitle: 'Exclusive Cloud Offer Details | Cloudatler',
            ogDescription: 'View the details of exclusive offers from leading cloud providers.Understand the terms, savings, and benefits to choose the best deal for your needs.',
            canonicalUrl: '/offers',
            hreflangs: [
                { lang: 'en-us', path: '/offers' },
                { lang: 'x-default', path: '/offers' },
                { lang: 'en', path: '/offers' }
            ]
        }
    },
    {
        path: 'offers/:title',
        component: OfferDetails
    },
    {
        path: 'order-history',
        component: OrderHistory,
        data: {
            title: 'Your Order History | Cloudatler',
            description: 'Securely access your complete order history. Review your past cloud service selections, billing details, and transaction dates with total privacy and confidence',
            ogTitle: 'Your Order History | Cloudatler',
            ogDescription: 'Securely access your complete order history. Review your past cloud service selections, billing details, and transaction dates with total privacy and confidence',
            canonicalUrl: '/order-history',
            hreflangs: [
                { lang: 'en-us', path: '/order-history' },
                { lang: 'x-default', path: '/order-history' },
                { lang: 'en', path: '/order-history' }
            ]
        }
    },
    {
        path: 'compare/:slug',
        component: ComparisonResults
    },
    {
        path: 'legal/cookie-policy',
        component: CookiePolicyDetails,
        data: {
            title: 'Cloudatler | Compare Cloud Services & Manage Costs Securely',
            description: 'Securely compare cloud providers, find exclusive deals, and manage costs with Cloudatler. Our FinOps platform turns bills into actionable insights with bank-level security.',
            ogTitle: 'Cloudatler | Compare Cloud Services & Manage Costs Securely',
            ogDescription: 'Securely compare cloud providers, find exclusive deals, and manage costs with Cloudatler. Our FinOps platform turns bills into actionable insights with bank-level security.',
            canonicalUrl: '/legal/cookie-policy',
            hreflangs: [
                { lang: 'en-us', path: '/legal/cookie-policy' },
                { lang: 'x-default', path: '/legal/cookie-policy' },
                { lang: 'en', path: '/legal/cookie-policy' }
            ]
        }
    },
    {
        path: 'legal/terms-services',
        component: TermsServices,
        data: {
            title: 'Cloudatler | Compare Cloud Services & Manage Costs Securely',
            description: 'Securely compare cloud providers, find exclusive deals, and manage costs with Cloudatler. Our FinOps platform turns bills into actionable insights with bank-level security.',
            ogTitle: 'Cloudatler | Compare Cloud Services & Manage Costs Securely',
            ogDescription: 'Securely compare cloud providers, find exclusive deals, and manage costs with Cloudatler. Our FinOps platform turns bills into actionable insights with bank-level security.',
            canonicalUrl: '/legal/terms-services',
            hreflangs: [
                { lang: 'en-us', path: '/legal/terms-services' },
                { lang: 'x-default', path: '/legal/terms-services' },
                { lang: 'en', path: '/legal/terms-services' }
            ]
        }
    },
    {
        path: 'legal/privacy-policy',
        component: PrivacyPolicy,
        data: {
            title: 'Cloudatler | Compare Cloud Services & Manage Costs Securely',
            description: 'Securely compare cloud providers, find exclusive deals, and manage costs with Cloudatler. Our FinOps platform turns bills into actionable insights with bank-level security.',
            ogTitle: 'Cloudatler | Compare Cloud Services & Manage Costs Securely',
            ogDescription: 'Securely compare cloud providers, find exclusive deals, and manage costs with Cloudatler. Our FinOps platform turns bills into actionable insights with bank-level security.',
            canonicalUrl: '/legal/privacy-policy',
            hreflangs: [
                { lang: 'en-us', path: '/legal/privacy-policy' },
                { lang: 'x-default', path: '/legal/privacy-policy' },
                { lang: 'en', path: '/legal/privacy-policy' }
            ]
        }
    },
    {
        path: 'blogs',
        component: Blogs,
        data: {
            title: 'Cloudatler | Compare Cloud Services & Manage Costs Securely',
            description: 'Securely compare cloud providers, find exclusive deals, and manage costs with Cloudatler. Our FinOps platform turns bills into actionable insights with bank-level security.',
            ogTitle: 'Cloudatler | Compare Cloud Services & Manage Costs Securely',
            ogDescription: 'Securely compare cloud providers, find exclusive deals, and manage costs with Cloudatler. Our FinOps platform turns bills into actionable insights with bank-level security.',
            canonicalUrl: '/blogs',
            hreflangs: [
                { lang: 'en-us', path: '/blogs' },
                { lang: 'x-default', path: '/blogs' },
                { lang: 'en', path: '/blogs' }
            ]
        }
    },
    {
        path: 'blog/:id',
        component: BlogPostComponent
    },
    {
        path: '**',
        component: PageNotFound
    },
    {
        path: '',
        redirectTo: '',
        pathMatch: 'full'
    }
];

@NgModule({
    imports: [RouterModule.forRoot(routes)],
    exports: [RouterModule]
})
export class AppRoutes { }