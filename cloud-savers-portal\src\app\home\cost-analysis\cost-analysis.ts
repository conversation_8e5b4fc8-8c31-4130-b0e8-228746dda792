import { CommonModule, isPlat<PERSON><PERSON>rowser } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, AfterViewInit, ViewChild, HostListener, OnDestroy, OnInit, ChangeDetectorRef, Inject, PLATFORM_ID } from '@angular/core';
import { MaterialModule } from '../../material-module';
import { SearchPanel } from '../search-panel/search-panel';
import { Header } from '../header/header';
import { FiltersSidebar } from './filters-sidebar/filters-sidebar';
import { CostCompareResults } from './cost-compare-results/cost-compare-results';
import { Cart } from './cart/cart';
import { Recommendations } from './recommendations/recommendations';
import { MobileFooter } from '../mobile-footer/mobile-footer';
import { Router, ActivatedRoute } from '@angular/router';
import { SearchStateService } from '../../services/search-state.service';
import { Auth } from '../../services/auth';
import { filter, takeUntil } from 'rxjs/operators';
import { Notification } from '../../services/notification';
import { colorObj } from '../../shared/color-object';
import { Subject } from 'rxjs';
import { CartService } from '../../services/cart-service';
import { environment } from '../../../environments/environment';
import { PROVIDER_ICON_MAP } from '../../shared/filter-headers.constants';

@Component({
  selector: 'app-cost-analysis',
  imports: [
    CommonModule,
    MaterialModule,
    Header,
    SearchPanel,
    FiltersSidebar,
    CostCompareResults,
    // Cart,
    // Recommendations,
    MobileFooter
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './cost-analysis.html',
  styleUrl: './cost-analysis.scss'
})
export class CostAnalysis implements AfterViewInit, OnInit, OnDestroy {

  loadingCount = 0;
  cartLoading = false;
  showFilterSidebar = false;
  showCartSidebar = false;
  cartItemCount = 0;
  mobile: boolean = false;
  // categories: any[] = [];
  // productsSelected: string[] = [];
  selectedTabIndex = 0;
  formArray: any[] = [];
  searchKeyword: string = '';
  @ViewChild(CostCompareResults) costCompareResults!: CostCompareResults;
  @ViewChild(FiltersSidebar) filtersSidebar!: FiltersSidebar;
  ignoreNextFilterChange = false;
  private destroy$ = new Subject<void>();
  private currentPage = 1;
  orderBy = 1;
  private currentFilters: any = null;
  private isApiCallInProgress = false;
  limit = environment.PER_PAGE;
  fallBack: boolean;
  get providerIconMap() {
    return PROVIDER_ICON_MAP;
  }
  compareInstances: any = [];
  get loading() {
    return this.loadingCount > 0 || this.cartLoading;
  }
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenWidth();
  }

  constructor(@Inject(PLATFORM_ID)
  private platformId: Object,
    private router: Router,
    private searchStateService: SearchStateService,
    private authService: Auth,
    private notify: Notification,
    private changeDetectorRef: ChangeDetectorRef,
    private cartService: CartService,
    private route: ActivatedRoute,
    private searchState: SearchStateService
  ) { }

  ngOnInit() {
    this.checkScreenWidth();
    // Verify we have search state, if not redirect back to comparison portal
    if (!this.searchStateService.hasState()) {
      this.router.navigate(['/']);
    }
    else {
      this.authService.searchKeyword$.subscribe(searchKeyword => {
        // Clear filters before processing new search
        this.searchStateService.requestFilterClear();
        this.searchKeyword = searchKeyword;
      });
      this.authService.formArray$.pipe(
        filter(formArray => !!formArray && formArray.length > 0),
        takeUntil(this.destroy$)).subscribe(formArray => {
          this.formArray = formArray;
          this.selectedTabIndex = 0;
        });
      this.resetPaginationState();
      this.triggerInitialDataFetch();
    }
    this.searchStateService.bestDealRequested$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (this.formArray && this.formArray.length > 0) {
          this.resetPaginationState();
          this.triggerInitialDataFetch();
        }
      });
    // this.route.queryParams.subscribe(params => {
    //   const action = params['action'];
    //   if (!action) {
    //     this.showFilterSidebar = false;
    //     this.showCartSidebar = false;
    //     return;
    //   }
    //   if (action === 'cart') {
    //     this.showCart();
    //   } else if (action === 'filter') {
    //     this.showFilters();
    //   }
    // });
    // this.searchState.filterSidebarVisible$.subscribe(show => {
    //   this.showFilterSidebar = show;
    //   this.changeDetectorRef.detectChanges();
    // });
    // this.searchState.cartSidebarVisible$.subscribe(show => {
    //   this.showCartSidebar = show;
    //   this.changeDetectorRef.detectChanges();
    // });
    this.searchState.filterSidebarVisible$
      .pipe(takeUntil(this.destroy$))
      .subscribe(show => {
        this.showFilterSidebar = show;
        this.changeDetectorRef.detectChanges();
      });
    // Subscribe to cart updates
    // this.cartService.cartUpdated$
    //   .pipe(takeUntil(this.destroy$))
    //   .subscribe(() => {
    //     if (this.authService.isLoggedIn()) {
    //       // this.updateCartItemCount();
    //     }
    //   });
    // // Initial cart item count update
    // if (this.authService.isLoggedIn()) {
    //   // this.updateCartItemCount();
    // }
    this.cartService.refreshComparisonPanel$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.refreshComparePanel();
      });
    this.refreshComparePanel();
    if (isPlatformBrowser(this.platformId)) {
      window.addEventListener('storage', () => this.refreshComparePanel());
    }
  }

  ngAfterViewInit() {
    this.checkScreenWidth();
    this.scrollToPricingContent();
  }

  private clearActionQueryParam() {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { action: null },
      queryParamsHandling: 'merge'
    });
  }

  private checkScreenWidth() {
    const wasMobile = this.mobile;
    if (isPlatformBrowser(this.platformId)) {
      this.mobile = window.innerWidth <= 1250;
    }
    if (wasMobile !== this.mobile) {
      this.changeDetectorRef.detectChanges();
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private resetPaginationState() {
    this.currentPage = 1;
    this.currentFilters = null;
    if (this.costCompareResults) {
      this.costCompareResults.resetPagination();
    }
  }

  private triggerInitialDataFetch() {
    setTimeout(() => {
      if (!this.isApiCallInProgress) {
        this.fetchPricingDataForTab(this.orderBy, 1);
      }
      // if (this.formArray && this.formArray.length > 0 && !this.isApiCallInProgress) {
      //   // Fetch pricing data for page 1
      //   this.fetchPricingDataForTab(1);
      //   // Fetch filter data for sidebar
      //   if (this.filtersSidebar) {
      //     this.filtersSidebar.fetchPricingData(this.formArray, 0);
      //   }
      // }
    }, 100);
  }

  // getCategories(formArray?: any[]) {
  //   this.authService.getProductCategories().subscribe({
  //     next: (data) => {
  //       this.categories = data.object || [];
  //       if (formArray) {
  //         this.productsSelected = formArray.map(item => {
  //           const category = this.categories.find(
  //             (cat: any) => cat.ID === item.selectedProduct
  //           );
  //           return category.PRODUCT_CATEGORY;
  //         });
  //       }
  //     }
  //   })
  // }

  fetchPricingDataForTab(orderBy, page: number) {
    this.scrollToPricingContent();
    // if (!this.formArray || this.formArray.length <= index || this.isApiCallInProgress) return;
    // const item = this.formArray[index];
    // if (!item) return;
    if (this.isApiCallInProgress) return;
    this.isApiCallInProgress = true;
    this.orderBy = orderBy;
    this.currentPage = page;
    let obj: any;
    // Use current filters if available, otherwise use form data
    // if (this.currentFilters) {
    //   const hasSelectedRegion = this.currentFilters.selectedRegion && this.currentFilters.selectedRegion.length > 0;
    //   obj = {
    //     SERVICE_NAME: item.selectedService,
    //     INSTANCE_FAMILY: this.currentFilters.selectedInstance || [],
    //     CPU: this.currentFilters.vCPU || [],
    //     MEMORY: this.currentFilters.RAM || [],
    //     OPERATING_SYSTEM: this.currentFilters.selectedOS || [],
    //     HOST_TYPE: this.currentFilters.selectedHost || [],
    //     PRICE_RANGE: {
    //       FROM: this.currentFilters.minPrice ? Number(this.currentFilters.minPrice) : null,
    //       TO: this.currentFilters.maxPrice ? Number(this.currentFilters.maxPrice) : null
    //     },
    //     PAGE: page,
    //     LIMIT: 1,
    //     ORDER_BY: "price"
    //   };
    //   if (hasSelectedRegion) {
    //     obj.REGION_CODE = this.currentFilters.selectedRegion || [];
    //   } else if (item.selectedRegion) {
    //     obj.REGION_GROUP = Array.isArray(item.selectedRegion) ? item.selectedRegion : [item.selectedRegion];
    //   } else {
    //     obj.REGION_GROUP = [];
    //   }
    // } else {
    //   // Use original form data
    //   obj = {
    //     SERVICE_NAME: item.selectedService,
    //     INSTANCE_FAMILY: [],
    //     CPU: item.vCPU ? (Array.isArray(item.vCPU) ? item.vCPU : [item.vCPU]) : [],
    //     MEMORY: item.RAM ? (Array.isArray(item.RAM) ? item.RAM : [item.RAM]) : [],
    //     REGION_GROUP: item.selectedRegion ? (Array.isArray(item.selectedRegion) ? item.selectedRegion : [item.selectedRegion]) : [],
    //     OPERATING_SYSTEM: item.selectedOS ? (Array.isArray(item.selectedOS) ? item.selectedOS : [item.selectedOS]) : [],
    //     HOST_TYPE: item.selectedHost ? (Array.isArray(item.selectedHost) ? item.selectedHost : [item.selectedHost]) : [],
    //     PAGE: page,
    //     LIMIT: 1,
    //     ORDER_BY: "price"
    //   };
    // }
    obj = {
      "QUERY": this.searchKeyword,
      // "FILTERS": this.currentFilters?.selectedFilters || [],
      "PRICE_RANGE": {
        "FROM": this.currentFilters?.minPrice ? Number(this.currentFilters?.minPrice)?.toFixed(2) : null,
        "TO": this.currentFilters?.maxPrice ? Number(this.currentFilters?.maxPrice)?.toFixed(2) : null
      },
      "ORDER_BY": this.orderBy,
      "PAGE": 1,
      // "PAGE": this.currentPage,
      "LIMIT": this.limit * this.currentPage,
      "FALLBACK": this.fallBack === undefined ? null : this.fallBack
    }
    if (this.formArray.length > 0 && (!this.currentFilters?.selectedFilters || this.currentFilters?.selectedFilters.length === 0)) {
      obj.FILTERS = this.formArray;
    }
    else if (this.currentFilters?.selectedFilters.length > 0) {
      obj.FILTERS = this.currentFilters.selectedFilters;
    }
    else {
      obj.FILTERS = [];
    }
    this.onLoadingChange(true);
    let APIInitiatedTime = Date.now();
    this.authService.getPricingData(obj).subscribe({
      next: (resp) => {
        let APIResponseTime = Date.now() - APIInitiatedTime;
        if (this.costCompareResults) {
          this.costCompareResults.setPricingData(resp.object, page, APIResponseTime);
        }
        this.fallBack = resp.object?.IS_FALLBACK;
        // if (this.filtersSidebar) {
        //   this.filtersSidebar.fetchPricingData(resp.object);
        // }
        this.onLoadingChange(false);
        this.isApiCallInProgress = false;
      },
      error: (e) => {
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        );
        this.onLoadingChange(false);
        this.isApiCallInProgress = false;
      }
    });
  }

  // onTabSelect(index: number): void {
  //   if (index === this.selectedTabIndex || this.isApiCallInProgress) return;
  //   this.selectedTabIndex = index;
  //   this.resetPaginationState();
  //   // Call the child method to fetch pricing and filter data for the selected tab
  //   this.fetchPricingDataForTab(1);
  //   if (this.filtersSidebar && this.formArray && this.formArray.length > index) {
  //     this.filtersSidebar.fetchPricingData(this.formArray, index);
  //   }
  // }

  onFiltersChanged(updatedFilters: any[]) {
    if (this.ignoreNextFilterChange) {
      this.ignoreNextFilterChange = false;
      return; // Skip this initial emission
    }
    if (this.isApiCallInProgress) return;
    // Store current filters and reset pagination to page 1
    this.currentFilters = { ...updatedFilters };
    this.currentPage = 1;
    if (this.costCompareResults) {
      this.costCompareResults.resetPagination();
    }
    // Fetch data with new filters starting from page 1
    this.fetchPricingDataForTab(this.orderBy, 1);
  }

  onSortFilterChange(orderBy) {
    if (!this.isApiCallInProgress) {
      this.orderBy = orderBy;
      this.fetchPricingDataForTab(this.orderBy, 1);
    }
  }

  // Method to handle pagination navigation from cost-compare-results
  onPageChange(page: number) {
    if (page !== this.currentPage && !this.isApiCallInProgress) {
      this.fetchPricingDataForTab(this.orderBy, page);
    }
  }

  // Method to refresh current page (for cart interactions)
  refreshCurrentPage() {
    if (!this.isApiCallInProgress) {
      this.fetchPricingDataForTab(this.orderBy, this.currentPage);
    }
  }

  onLoadingChange(isLoading: boolean) {
    if (isLoading) {
      this.loadingCount++;
    } else {
      this.loadingCount = Math.max(0, this.loadingCount - 1);
    }
  }

  onCartLoadingChange(loading: boolean) {
    this.cartLoading = loading;
  }

  scrollToPricingContent() {
    setTimeout(() => {
      if (isPlatformBrowser(this.platformId)) {
        const element = document.getElementById('pricingContent');
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }
    }, 100);
  }

  closeFilterSidebar(): void {
    this.searchState.hideFilterSidebar();
  }

  toggleFilterSidebar(): void {
    this.showFilterSidebar = !this.showFilterSidebar;
    // if (this.showFilterSidebar) {
    //   this.showCartSidebar = false;
    // } else {
    //   this.clearActionQueryParam();
    // }
    if (this.showFilterSidebar) {
      this.searchState.hideFilterSidebar();
    } else {
      this.searchState.showFilterSidebar();
      this.clearActionQueryParam();
    }
  }

  // toggleCartSidebar(): void {
  //   this.showCartSidebar = !this.showCartSidebar;
  //   if (this.showCartSidebar) {
  //     this.showFilterSidebar = false;
  //     this.cartService.cartUpdated$.next();
  //   } else {
  //     this.clearActionQueryParam();
  //   }
  //   // this.updateBodyClass();
  // }

  // private updateCartItemCount(): void {
  //   this.cartService.getCartItems().subscribe({
  //     next: (data: any) => {
  //       if (data && data.object && Array.isArray(data.object)) {
  //         this.cartItemCount = data.object.length;
  //       } else {
  //         this.cartItemCount = 0;
  //       }
  //     },
  //     error: (error: any) => {
  //       this.cartItemCount = 0;
  //     }
  //   });
  // }

  // private updateBodyClass(): void {
  //   if (typeof document !== 'undefined') {
  //     if (this.showFilterSidebar || this.showCartSidebar) {
  //       document.body.classList.add('modal-open');
  //     } else {
  //       document.body.classList.remove('modal-open');
  //     }
  //   }
  // }

  backToHome() {
    this.searchStateService.clearState(); // Clear state before navigating back to comparison portal
    this.authService.clearShowMoreFiltersIndices(); // Hide all More Filters
    this.router.navigate(['/']);
  }

  // showCart() {
  //   this.showCartSidebar = true;
  //   this.showFilterSidebar = false;
  //   // this.updateBodyClass();
  //   setTimeout(() => {
  //     const cartContent = document.querySelector('.cart-sidebar .cart-content');
  //     if (cartContent) {
  //       (cartContent as HTMLElement).scrollTop = 0;
  //     }
  //   }, 0);
  // }

  showFilters() {
    this.showFilterSidebar = true;
    this.showCartSidebar = false;
    setTimeout(() => {
      if (isPlatformBrowser(this.platformId)) {
        const filterContent = document.querySelector('.filter-wrapper .filter-content');
        if (filterContent) {
          (filterContent as HTMLElement).scrollTop = 0;
        }
      }
    }, 0);
    this.clearActionQueryParam();
  }

  refreshComparePanel() {
    let compareArray = [];
    if (isPlatformBrowser(this.platformId)) {
      const compArray = window.localStorage.getItem('COMPARE_INSTANCES') || '[]';
      compareArray = compArray ? JSON.parse(compArray) : [];
    }
    if (this.authService.isLoggedIn()) {
      if (compareArray.length > 0) {
        this.cartService.getComparisonData().subscribe({
          next: (data: any) => {
            if (data && data.object && Array.isArray(data.object)) {
              this.addResourceToComparison(compareArray);
            }
          },
          error: (e) => {
            this.notify.showNotification(
              e?.error?.message,
              "top",
              (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
              e?.error?.status
            )
          }
        });
      }
      else if (compareArray.length == 0) {
        this.cartService.getComparisonData().subscribe({
          next: (data: any) => {
            if (data && data.object && Array.isArray(data.object)) {
              this.compareInstances = data.object;
              if (isPlatformBrowser(this.platformId)) {
                window.localStorage.setItem('COMPARE_INSTANCES', JSON.stringify(this.compareInstances));
              }
            }
          },
          error: (e) => {
            this.notify.showNotification(
              e?.error?.message,
              "top",
              (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
              e?.error?.status
            )
          }
        });
      }
    }
    else {
      this.compareInstances = Array.isArray(compareArray) ? compareArray : [];
    }
    this.changeDetectorRef.detectChanges();
  }

  addResourceToComparison(compareArray) {
    let comparisonResources = [];
    compareArray.forEach((item: any) => {
      comparisonResources.push({
        "RECORD_ID": item.RECORD_ID,
        "DOCUMENT": item.DOCUMENT
      });
    });
    let obj = {
      "RESOURCE": comparisonResources
    }
    this.cartService.addToComparison(obj).subscribe({
      next: (data: any) => {
        if (data && data.object && Array.isArray(data.object)) {
          this.compareInstances = data.object;
        }
      },
      error: (e) => {
        this.notify.showNotification(
          e?.error?.message,
          "top",
          (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
          e?.error?.status
        )
      }
    });
  }

  removeFromCompare(item: any) {
    if (this.authService.isLoggedIn()) {
      let obj = {
        "ID": [item.ID]
      };
      this.cartService.removeComparison(obj).subscribe({
        next: (data: any) => {
          if (data && data.object && Array.isArray(data.object)) {
            this.compareInstances = [...data.object];
            if (isPlatformBrowser(this.platformId)) {
              window.localStorage.setItem('COMPARE_INSTANCES', JSON.stringify(this.compareInstances));
            }
            if (this.costCompareResults && typeof this.costCompareResults.loadCompareInstances === 'function') {
              this.costCompareResults.loadCompareInstances();
              this.costCompareResults.cdr.detectChanges();
            }
            this.changeDetectorRef.detectChanges();
          }
        },
        error: (e) => {
          this.notify.showNotification(
            e?.error?.message,
            "top",
            (!!colorObj[e?.error?.status] ? colorObj[e?.error?.status] : "error"),
            e?.error?.status
          )
        }
      });
    }
    else {
      const updated = this.compareInstances.filter(i => i.RECORD_ID !== item.RECORD_ID);
      if (isPlatformBrowser(this.platformId)) {
        window.localStorage.setItem('COMPARE_INSTANCES', JSON.stringify(updated));
      }
      this.compareInstances = [...updated]; // Ensure a new array reference
      // Notify child (cost-compare-results) to reload compareInstances
      if (this.costCompareResults && typeof this.costCompareResults.loadCompareInstances === 'function') {
        this.costCompareResults.loadCompareInstances();
        this.costCompareResults.cdr.detectChanges();
      }
      this.changeDetectorRef.detectChanges();
    }
  }

  goToComparison() {
    let slug = '';
    this.compareInstances.forEach((item: any, index: number) => {
      const serviceProvider = this.slugify(item.DOCUMENT.service_provider);
      const productName = this.slugify(item.DOCUMENT.product_name);
      slug += serviceProvider + '-' + productName + '--' + item.DOCUMENT.id + '--' + item.DOCUMENT.record_id;
      if (index < this.compareInstances.length - 1) {
        slug += '--vs--';
      }
    });
    this.router.navigate(['/compare', slug]);
  }

  slugify(text: string): string {
    return text
      ?.toString()
      ?.toLowerCase()
      ?.trim()
      ?.replace(/[\/\s]+/g, '-')
      ?.replace(/[^\w-]+/g, '')
      ?.replace(/-+/g, '-')
      ?.replace(/^-+|-+$/g, '');
  }

}