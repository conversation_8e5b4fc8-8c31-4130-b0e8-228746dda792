import { CommonModule } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { MaterialModule } from '../../material-module';
import { MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';

@Component({
  selector: 'app-cookie-settings',
  imports: [CommonModule, MaterialModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './cookie-settings.html',
  styleUrl: './cookie-settings.scss'
})
export class CookieSettings {

  acceptedCookies: string[] = ['essential', 'functional', 'location'];

  constructor(
    public dialogRef: MatDialogRef<CookieSettings>,
    private router: Router
  ) { }

  ngOnInit() { }

  selectCookies(event, name) {
    let requiredIdIndex = this.acceptedCookies.findIndex(x => x == name);
    if (requiredIdIndex == -1 && event.checked == true) {
      this.acceptedCookies.push(name);
    }
    else if (requiredIdIndex != -1 && event.checked == false) {
      this.acceptedCookies.splice(requiredIdIndex, 1);
    }
  }

  acceptCookies() {
    this.dialogRef.close({ 'categories': this.acceptedCookies });
  }

  viewDetails() {
    window.open('/legal/cookie-policy', '_blank');
    this.dialogRef.close();
  }

  rejectCookies() {
    this.acceptedCookies = ['essential', 'functional', 'location'];
    this.dialogRef.close({ 'categories': this.acceptedCookies });
  }

}