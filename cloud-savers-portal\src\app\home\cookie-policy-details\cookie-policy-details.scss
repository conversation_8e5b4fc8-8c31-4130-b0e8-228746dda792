.head-row {
    display: flex;
    align-items: center;
    padding: 20px 50px;
    background-color: #FFFFFF;
    border-bottom: 1px solid #E5E7EB;

    .back-arrow {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 16px;
        color: #374151;
        cursor: pointer;
        text-align: start;
        width: 10%;
    }

    .pricing-head {
        font-size: 20px;
        font-weight: 600;
        color: #000000;
        text-align: center;
        width: 90%;
    }
}

.cookie-policy-container {
    padding: 4% 18%;
}

.policy-header {
    padding: 20px 0;
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 30px;
    text-align: center;

    .policy-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;
        color: #1a1a1a;
    }

    .last-updated {
        font-size: 14px;
        color: #666;
        font-weight: 400;
    }
}

.policy-content {
    padding-bottom: 0px;
}

.policy-section {
    margin-bottom: 40px;

    h2 {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 18px 0;
        color: #000000;
        line-height: 32px;
    }

    h3 {
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 8px 0;
        color: #000000;
        line-height: 24px;
    }

    p {
        font-size: 16px;
        color: #4B5563;
        margin: 0 0 16px 0;
        line-height: 26px;
    }

    ul {
        margin: 12px 0;
        padding-left: 20px;

        li {
            font-size: 14px;
            color: #4B5563;
            margin-bottom: 8px;
            line-height: 1.5;
        }
    }
}

.info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 16px;
    background-color: #FFFFFF;
    border: 1px solid #E5E7EB;
    border-radius: 8px;

    .info-icon {
        margin-right: 16px;
        margin-top: 2px;
        flex-shrink: 0;
    }

    .info-content {
        flex: 1;

        h3 {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 600;
            color: #000000;
            line-height: 24px;
        }

        p {
            margin: 0;
            font-size: 16px;
            color: #4B5563;
            line-height: 26px;
        }
    }
}

// .cookie-types-container {
//     background: #FFFFFF;
//     padding: 20px;
//     border-radius: 8px;
//     border: 1px solid #E5E7EB;
// }

.cookie-type {
    margin-bottom: 24px;
    // padding-left: 16px;

    h3 {
        font-size: 15px;
        font-weight: 600;
        margin: 0 0 8px 0;
        color: #4B5563;
    }

    p {
        font-size: 16px;
        color: #4B5563;
        margin: 0 0 12px 0;
    }

    ul {
        margin: 8px 0 0 16px;

        li {
            font-size: 14px;
            color: #4B5563;
            margin-bottom: 6px;
        }
    }
}

.contact-link {
    color: #4B5563;
    text-decoration: none;
    font-weight: 600;

    &:hover {
        text-decoration: underline;
    }
}

@media only screen and (max-width: 1250px) {

    .head-row {
        padding: 12px 16px;
    }

    .cookie-policy-container {
        padding: 15px 20px;
    }

    .policy-header {
        padding: 16px 0;

        .policy-title {
            font-size: 20px;
        }
    }

    .info-item {
        padding: 12px;

        .info-icon {
            margin-right: 12px;
        }
    }

    .policy-content {
        padding-bottom: 40px;
    }

    .policy-section {
        margin-bottom: 32px;

        h2 {
            font-size: 18px;
            margin: 0 0 12px 0;
        }
    }

    .cookie-types-container {
        padding: 14px;
    }
}